{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_133e7ea9._.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_587cc249._.js", "server/edge/chunks/[root-of-the-server]__1274d79d._.js", "server/edge/chunks/apps_web_client_edge-wrapper_0447e01a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UbnSsXifywJT6PaEeFyV81RV7w9GKP7hknsOxWW+8jA=", "__NEXT_PREVIEW_MODE_ID": "0272a31cebebb427ceee5a4c9bd8e697", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9d124a003d5fa56b4a3692787c392e0d63d4daa818a26d71d7d7a9d1197d20d4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ce072c5ec80f9944a6e4899b9af4cfd3d8b005247019e658ebdf4449733b544a"}}}, "sortedMiddleware": ["/"], "functions": {}}