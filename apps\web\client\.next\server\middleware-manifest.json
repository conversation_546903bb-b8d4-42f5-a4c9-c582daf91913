{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_133e7ea9._.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_587cc249._.js", "server/edge/chunks/[root-of-the-server]__1274d79d._.js", "server/edge/chunks/apps_web_client_edge-wrapper_0447e01a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UbnSsXifywJT6PaEeFyV81RV7w9GKP7hknsOxWW+8jA=", "__NEXT_PREVIEW_MODE_ID": "e7b1ebbbbb57b982c2d7e7d6ffec60ef", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7896115a3d99e9d93b9125a44e55525af3bfcc38ef1fce7bab0b1806e00442ee", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2468fcfc876ba84cc868eb833829d4d3e0bc515a5f85ca73195b948a964405e1"}}}, "sortedMiddleware": ["/"], "functions": {}}