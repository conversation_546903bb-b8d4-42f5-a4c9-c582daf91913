{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/right-panel/dev-tab/code-mirror-config.ts"], "sourcesContent": ["import { autocompletion } from '@codemirror/autocomplete';\r\nimport { css } from '@codemirror/lang-css';\r\nimport { html } from '@codemirror/lang-html';\r\nimport { javascript } from '@codemirror/lang-javascript';\r\nimport { json } from '@codemirror/lang-json';\r\nimport { markdown } from '@codemirror/lang-markdown';\r\nimport { bracketMatching, HighlightStyle, syntaxHighlighting } from '@codemirror/language';\r\nimport { lintGutter } from '@codemirror/lint';\r\nimport { highlightSelectionMatches } from '@codemirror/search';\r\nimport { drawSelection, EditorView, highlightActiveLine, highlightActiveLineGutter, highlightSpecialChars, keymap, lineNumbers } from '@codemirror/view';\r\nimport { tags } from '@lezer/highlight';\r\n\r\n// Custom colors for CodeMirror\r\nconst customColors = {\r\n    orange: '#FFAC60',\r\n    purple: '#C478FF',\r\n    blue: '#3FA4FF',\r\n    green: '#1AC69C',\r\n    pink: '#FF32C6'\r\n}\r\n\r\n// Basic theme for CodeMirror\r\nexport const basicTheme = {\r\n    '&': {\r\n        fontSize: '13px',\r\n        backgroundColor: 'transparent',\r\n    },\r\n    '&.cm-focused .cm-selectionBackground, & .cm-selectionBackground': {\r\n        backgroundColor: 'rgba(21, 170, 147, 0.2) !important',\r\n    },\r\n    '.cm-content': {\r\n        lineHeight: '1.5',\r\n    },\r\n};\r\n\r\n//dark theme for code editor\r\nexport const customDarkTheme = EditorView.theme({\r\n    '&': {\r\n        color: '#ffffff',\r\n        backgroundColor: '#000000',\r\n        fontSize: '13px',\r\n        userSelect: 'none !important',\r\n    },\r\n    '.cm-content': {\r\n        padding: '10px 0',\r\n        lineHeight: '1.5',\r\n        caretColor: customColors.blue,\r\n        backgroundColor: '#000000',\r\n        userSelect: 'text !important',\r\n    },\r\n    '.cm-focused': {\r\n        outline: 'none',\r\n    },\r\n    '&.cm-focused .cm-cursor': {\r\n        borderLeftColor: customColors.blue,\r\n        borderLeftWidth: '2px'\r\n    },\r\n    '&.cm-focused .cm-selectionBackground, ::selection': {\r\n        backgroundColor: 'rgba(63, 164, 255, 0.2)',\r\n    },\r\n    '&.cm-editor.cm-focused .cm-selectionBackground': {\r\n        backgroundColor: `${customColors.green}33 !important`,\r\n    },\r\n    '&.cm-editor .cm-selectionBackground': {\r\n        backgroundColor: `${customColors.green}33 !important`,\r\n    },\r\n    '&.cm-editor .cm-content ::selection': {\r\n        backgroundColor: `${customColors.green}33 !important`,\r\n    },\r\n    '.cm-line ::selection': {\r\n        backgroundColor: `${customColors.green}33 !important`,\r\n    },\r\n    '::selection': {\r\n        backgroundColor: `${customColors.green}33 !important`,\r\n    },\r\n    '.cm-selectionBackground': {\r\n        backgroundColor: 'rgba(63, 164, 255, 0.2)',\r\n    },\r\n    '.cm-gutters': {\r\n        backgroundColor: '#0a0a0a !important',\r\n        color: '#6b7280 !important',\r\n        border: 'none !important',\r\n        borderRight: '1px solid #1f2937 !important'\r\n    },\r\n    '.cm-gutterElement': {\r\n        color: '#6b7280'\r\n    },\r\n    '.cm-lineNumbers .cm-gutterElement': {\r\n        color: '#6b7280',\r\n        fontSize: '12px'\r\n    },\r\n    '.cm-activeLine': {\r\n        backgroundColor: 'rgba(255, 255, 255, 0.02)'\r\n    },\r\n    '.cm-activeLineGutter': {\r\n        backgroundColor: 'rgba(255, 255, 255, 0.05)'\r\n    },\r\n    '.cm-foldPlaceholder': {\r\n        backgroundColor: '#1f2937',\r\n        border: '1px solid #374151',\r\n        color: customColors.blue\r\n    },\r\n    // Scrollbar styling\r\n    '.cm-scroller::-webkit-scrollbar': {\r\n        width: '8px',\r\n        height: '8px'\r\n    },\r\n    '.cm-scroller::-webkit-scrollbar-track': {\r\n        backgroundColor: '#0a0a0a'\r\n    },\r\n    '.cm-scroller::-webkit-scrollbar-thumb': {\r\n        backgroundColor: '#374151',\r\n        borderRadius: '4px'\r\n    },\r\n    '.cm-scroller::-webkit-scrollbar-thumb:hover': {\r\n        backgroundColor: '#4b5563'\r\n    },\r\n}, { dark: true });\r\n\r\n// Custom syntax highlighting with the specified colors\r\nexport const customDarkHighlightStyle = HighlightStyle.define([\r\n    // Keywords (if, for, function, etc.) - Pink \r\n    { tag: tags.keyword, color: customColors.pink, fontWeight: 'bold' },\r\n    { tag: tags.controlKeyword, color: customColors.pink, fontWeight: 'bold' },\r\n    { tag: tags.operatorKeyword, color: customColors.pink },\r\n\r\n    // Strings - Blue\r\n    { tag: tags.string, color: customColors.blue },\r\n    { tag: tags.regexp, color: customColors.blue },\r\n\r\n    // Numbers - Pink, bool purple, null pink\r\n    { tag: tags.number, color: customColors.pink },\r\n    { tag: tags.bool, color: customColors.purple },\r\n    { tag: tags.null, color: customColors.pink },\r\n\r\n    // Functions - purple and methods - pink\r\n    { tag: tags.function(tags.variableName), color: customColors.purple },\r\n    { tag: tags.function(tags.propertyName), color: customColors.pink },\r\n\r\n\r\n    // Variables-purple and properties - Green\r\n    { tag: tags.variableName, color: customColors.purple },\r\n    { tag: tags.propertyName, color: customColors.green },\r\n    { tag: tags.attributeName, color: customColors.green },\r\n\r\n    // Types and classes - Purple (lighter shade)\r\n    { tag: tags.typeName, color: '#E879F9' },\r\n    { tag: tags.className, color: '#E879F9' },\r\n    { tag: tags.namespace, color: '#E879F9' },\r\n\r\n    // Comments - Gray\r\n    { tag: tags.comment, color: '#6b7280', fontStyle: 'italic' },\r\n    { tag: tags.lineComment, color: '#6b7280', fontStyle: 'italic' },\r\n    { tag: tags.blockComment, color: '#6b7280', fontStyle: 'italic' },\r\n\r\n    // Operators - White/Light Gray\r\n    { tag: tags.operator, color: '#d1d5db' },\r\n    { tag: tags.punctuation, color: '#d1d5db' },\r\n    { tag: tags.bracket, color: '#d1d5db' },\r\n\r\n    // Tags (HTML/JSX) - Pink\r\n    { tag: tags.tagName, color: customColors.pink },\r\n    { tag: tags.angleBracket, color: '#d1d5db' },\r\n\r\n    // Special tokens\r\n    { tag: tags.atom, color: customColors.pink },\r\n    { tag: tags.literal, color: customColors.orange },\r\n    { tag: tags.unit, color: customColors.pink },\r\n\r\n    // Invalid/Error\r\n    { tag: tags.invalid, color: '#ef4444', textDecoration: 'underline' }\r\n]);\r\n\r\n// Basic setup for CodeMirror\r\nexport const getBasicSetup = (saveFile: () => void) => {\r\n    const baseExtensions = [\r\n        highlightActiveLine(),\r\n        highlightActiveLineGutter(),\r\n        highlightSpecialChars(),\r\n        drawSelection(),\r\n        bracketMatching(),\r\n        autocompletion(),\r\n        highlightSelectionMatches(),\r\n        lintGutter(),\r\n        lineNumbers(),\r\n        keymap.of([\r\n            {\r\n                key: 'Mod-s',\r\n                run: () => {\r\n                    saveFile();\r\n                    return true;\r\n                },\r\n            },\r\n        ]),\r\n\r\n        customDarkTheme,\r\n        syntaxHighlighting(customDarkHighlightStyle)\r\n    ];\r\n\r\n    return baseExtensions;\r\n};\r\n\r\n// Get language extensions for CodeMirror based on file type\r\nexport function getLanguageFromFileName(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n        case 'js':\r\n            return 'javascript';\r\n        case 'jsx':\r\n            return 'javascript';\r\n        case 'ts':\r\n            return 'typescript';\r\n        case 'tsx':\r\n            return 'typescript';\r\n        case 'css':\r\n            return 'css';\r\n        case 'html':\r\n            return 'html';\r\n        case 'json':\r\n            return 'json';\r\n        case 'md':\r\n            return 'markdown';\r\n        default:\r\n            return 'typescript';\r\n    }\r\n}\r\n\r\n// Get CodeMirror extensions based on file language\r\nexport function getExtensions(language: string): any[] {\r\n    switch (language) {\r\n        case 'javascript':\r\n            return [javascript({ jsx: true })];\r\n        case 'typescript':\r\n            return [javascript({ jsx: true, typescript: true })];\r\n        case 'css':\r\n            return [css()];\r\n        case 'html':\r\n            return [html()];\r\n        case 'json':\r\n            return [json()];\r\n        case 'markdown':\r\n            return [markdown()];\r\n        default:\r\n            return [javascript({ jsx: true, typescript: true })];\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,+BAA+B;AAC/B,MAAM,eAAe;IACjB,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;AACV;AAGO,MAAM,aAAa;IACtB,KAAK;QACD,UAAU;QACV,iBAAiB;IACrB;IACA,mEAAmE;QAC/D,iBAAiB;IACrB;IACA,eAAe;QACX,YAAY;IAChB;AACJ;AAGO,MAAM,kBAAkB,wJAAA,CAAA,aAAU,CAAC,KAAK,CAAC;IAC5C,KAAK;QACD,OAAO;QACP,iBAAiB;QACjB,UAAU;QACV,YAAY;IAChB;IACA,eAAe;QACX,SAAS;QACT,YAAY;QACZ,YAAY,aAAa,IAAI;QAC7B,iBAAiB;QACjB,YAAY;IAChB;IACA,eAAe;QACX,SAAS;IACb;IACA,2BAA2B;QACvB,iBAAiB,aAAa,IAAI;QAClC,iBAAiB;IACrB;IACA,qDAAqD;QACjD,iBAAiB;IACrB;IACA,kDAAkD;QAC9C,iBAAiB,GAAG,aAAa,KAAK,CAAC,aAAa,CAAC;IACzD;IACA,uCAAuC;QACnC,iBAAiB,GAAG,aAAa,KAAK,CAAC,aAAa,CAAC;IACzD;IACA,uCAAuC;QACnC,iBAAiB,GAAG,aAAa,KAAK,CAAC,aAAa,CAAC;IACzD;IACA,wBAAwB;QACpB,iBAAiB,GAAG,aAAa,KAAK,CAAC,aAAa,CAAC;IACzD;IACA,eAAe;QACX,iBAAiB,GAAG,aAAa,KAAK,CAAC,aAAa,CAAC;IACzD;IACA,2BAA2B;QACvB,iBAAiB;IACrB;IACA,eAAe;QACX,iBAAiB;QACjB,OAAO;QACP,QAAQ;QACR,aAAa;IACjB;IACA,qBAAqB;QACjB,OAAO;IACX;IACA,qCAAqC;QACjC,OAAO;QACP,UAAU;IACd;IACA,kBAAkB;QACd,iBAAiB;IACrB;IACA,wBAAwB;QACpB,iBAAiB;IACrB;IACA,uBAAuB;QACnB,iBAAiB;QACjB,QAAQ;QACR,OAAO,aAAa,IAAI;IAC5B;IACA,oBAAoB;IACpB,mCAAmC;QAC/B,OAAO;QACP,QAAQ;IACZ;IACA,yCAAyC;QACrC,iBAAiB;IACrB;IACA,yCAAyC;QACrC,iBAAiB;QACjB,cAAc;IAClB;IACA,+CAA+C;QAC3C,iBAAiB;IACrB;AACJ,GAAG;IAAE,MAAM;AAAK;AAGT,MAAM,2BAA2B,4JAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1D,6CAA6C;IAC7C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,OAAO;QAAE,OAAO,aAAa,IAAI;QAAE,YAAY;IAAO;IAClE;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,cAAc;QAAE,OAAO,aAAa,IAAI;QAAE,YAAY;IAAO;IACzE;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,eAAe;QAAE,OAAO,aAAa,IAAI;IAAC;IAEtD,iBAAiB;IACjB;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,MAAM;QAAE,OAAO,aAAa,IAAI;IAAC;IAC7C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,MAAM;QAAE,OAAO,aAAa,IAAI;IAAC;IAE7C,yCAAyC;IACzC;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,MAAM;QAAE,OAAO,aAAa,IAAI;IAAC;IAC7C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,IAAI;QAAE,OAAO,aAAa,MAAM;IAAC;IAC7C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,IAAI;QAAE,OAAO,aAAa,IAAI;IAAC;IAE3C,wCAAwC;IACxC;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,wJAAA,CAAA,OAAI,CAAC,YAAY;QAAG,OAAO,aAAa,MAAM;IAAC;IACpE;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,wJAAA,CAAA,OAAI,CAAC,YAAY;QAAG,OAAO,aAAa,IAAI;IAAC;IAGlE,0CAA0C;IAC1C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,YAAY;QAAE,OAAO,aAAa,MAAM;IAAC;IACrD;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,YAAY;QAAE,OAAO,aAAa,KAAK;IAAC;IACpD;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,aAAa;QAAE,OAAO,aAAa,KAAK;IAAC;IAErD,6CAA6C;IAC7C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,QAAQ;QAAE,OAAO;IAAU;IACvC;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,SAAS;QAAE,OAAO;IAAU;IACxC;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,SAAS;QAAE,OAAO;IAAU;IAExC,kBAAkB;IAClB;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,OAAO;QAAE,OAAO;QAAW,WAAW;IAAS;IAC3D;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,WAAW;QAAE,OAAO;QAAW,WAAW;IAAS;IAC/D;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,YAAY;QAAE,OAAO;QAAW,WAAW;IAAS;IAEhE,+BAA+B;IAC/B;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,QAAQ;QAAE,OAAO;IAAU;IACvC;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,WAAW;QAAE,OAAO;IAAU;IAC1C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,OAAO;QAAE,OAAO;IAAU;IAEtC,yBAAyB;IACzB;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,OAAO;QAAE,OAAO,aAAa,IAAI;IAAC;IAC9C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,YAAY;QAAE,OAAO;IAAU;IAE3C,iBAAiB;IACjB;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,IAAI;QAAE,OAAO,aAAa,IAAI;IAAC;IAC3C;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,OAAO;QAAE,OAAO,aAAa,MAAM;IAAC;IAChD;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,IAAI;QAAE,OAAO,aAAa,IAAI;IAAC;IAE3C,gBAAgB;IAChB;QAAE,KAAK,wJAAA,CAAA,OAAI,CAAC,OAAO;QAAE,OAAO;QAAW,gBAAgB;IAAY;CACtE;AAGM,MAAM,gBAAgB,CAAC;IAC1B,MAAM,iBAAiB;QACnB,CAAA,GAAA,wJAAA,CAAA,sBAAmB,AAAD;QAClB,CAAA,GAAA,wJAAA,CAAA,4BAAyB,AAAD;QACxB,CAAA,GAAA,wJAAA,CAAA,wBAAqB,AAAD;QACpB,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD;QACZ,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD;QACd,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;QACb,CAAA,GAAA,0JAAA,CAAA,4BAAyB,AAAD;QACxB,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD;QACT,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD;QACV,wJAAA,CAAA,SAAM,CAAC,EAAE,CAAC;YACN;gBACI,KAAK;gBACL,KAAK;oBACD;oBACA,OAAO;gBACX;YACJ;SACH;QAED;QACA,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD,EAAE;KACtB;IAED,OAAO;AACX;AAGO,SAAS,wBAAwB,QAAgB;IACpD,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAC7C,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AAGO,SAAS,cAAc,QAAgB;IAC1C,OAAQ;QACJ,KAAK;YACD,OAAO;gBAAC,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE;oBAAE,KAAK;gBAAK;aAAG;QACtC,KAAK;YACD,OAAO;gBAAC,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE;oBAAE,KAAK;oBAAM,YAAY;gBAAK;aAAG;QACxD,KAAK;YACD,OAAO;gBAAC,CAAA,GAAA,+JAAA,CAAA,MAAG,AAAD;aAAI;QAClB,KAAK;YACD,OAAO;gBAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD;aAAI;QACnB,KAAK;YACD,OAAO;gBAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD;aAAI;QACnB,KAAK;YACD,OAAO;gBAAC,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD;aAAI;QACvB;YACI,OAAO;gBAAC,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE;oBAAE,KAAK;oBAAM,YAAY;gBAAK;aAAG;IAC5D;AACJ", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/_components/edit-app.tsx"], "sourcesContent": ["import { transKeys } from '@/i18n/keys';\r\nimport { sendAnalytics } from '@/utils/analytics';\r\nimport { Routes } from '@/utils/constants';\r\nimport type { Project } from '@onlook/models';\r\nimport { Button } from '@onlook/ui/button';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { motion } from 'motion/react';\r\nimport { useTranslations } from 'next-intl';\r\nimport { redirect } from 'next/navigation';\r\nimport type { ComponentProps } from 'react';\r\nimport { useState } from 'react';\r\n\r\nconst ButtonMotion = motion.create(Button);\r\n\r\ninterface EditAppButtonProps extends ComponentProps<typeof ButtonMotion> {\r\n    project: Project;\r\n}\r\n\r\nexport const EditAppButton = observer(({ project, ...props }: EditAppButtonProps) => {\r\n    const t = useTranslations();\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    const selectProject = (project: Project) => {\r\n        setIsLoading(true);\r\n        sendAnalytics('open project', { id: project.id });\r\n        redirect(`${Routes.PROJECT}/${project.id}`);\r\n    };\r\n\r\n    return (\r\n        <ButtonMotion\r\n            size=\"default\"\r\n            variant={'outline'}\r\n            className=\"gap-2 bg-background-active border-[0.5px] border-border-active w-auto hover:bg-background-onlook cursor-pointer\"\r\n            onClick={() => selectProject(project)}\r\n            disabled={isLoading}\r\n            {...props}\r\n        >\r\n            {isLoading ? (\r\n                <Icons.LoadingSpinner className=\"w-4 h-4 animate-spin\" />\r\n            ) : (\r\n                <Icons.PencilPaper />\r\n            )}\r\n            <p>{t(transKeys.projects.actions.editApp)}</p>\r\n        </ButtonMotion>\r\n    );\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;AAEA,MAAM,eAAe,8OAAA,CAAA,SAAM,CAAC,MAAM,CAAC,iJAAA,CAAA,SAAM;KAAnC;AAMC,MAAM,gBAAgB,GAAA,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,WAAE,CAAC,EAAE,OAAO,EAAE,GAAG,OAA2B;;IAC5E,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB,CAAC;QACnB,aAAa;QACb,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,gBAAgB;YAAE,IAAI,QAAQ,EAAE;QAAC;QAC/C,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,8JAAA,CAAA,SAAM,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE;IAC9C;IAEA,qBACI,6LAAC;QACG,MAAK;QACL,SAAS;QACT,WAAU;QACV,SAAS,IAAM,cAAc;QAC7B,UAAU;QACT,GAAG,KAAK;;YAER,0BACG,6LAAC,yJAAA,CAAA,QAAK,CAAC,cAAc;gBAAC,WAAU;;;;;qCAEhC,6LAAC,yJAAA,CAAA,QAAK,CAAC,WAAW;;;;;0BAEtB,6LAAC;0BAAG,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;AAGpD;;QA1Bc,yMAAA,CAAA,kBAAe;;;;QAAf,yMAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/_components/carousel.tsx"], "sourcesContent": ["import { getFileUrlFromStorage } from '@/utils/supabase/client';\r\nimport { STORAGE_BUCKETS } from '@onlook/constants';\r\nimport type { Project } from '@onlook/models';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport type { EmblaCarouselType, EmblaEventType } from 'embla-carousel';\r\nimport useEmblaCarousel from 'embla-carousel-react';\r\nimport { motion, type Variants } from 'motion/react';\r\nimport type React from 'react';\r\nimport { useCallback, useEffect, useRef, useState } from 'react';\r\nimport { EditAppButton } from './edit-app';\r\n\r\ninterface CarouselProps {\r\n    slides: Project[];\r\n    onSlideChange: (index: number) => void;\r\n}\r\n\r\nconst numberWithinRange = (number: number, min: number, max: number): number =>\r\n    Math.min(Math.max(number, min), max);\r\n\r\nexport const Carousel: React.FC<CarouselProps> = ({ slides, onSlideChange }) => {\r\n    const WHEEL_SENSITIVITY = 13;\r\n    const SCROLL_COOLDOWN = 50;\r\n    const TWEEN_FACTOR_BASE = 0.3;\r\n\r\n    const tweenFactor = useRef(0);\r\n    const tweenNodes = useRef<HTMLElement[]>([]);\r\n    const scrollTimeout = useRef<Timer | null>(null);\r\n\r\n    const [isScrolling, setIsScrolling] = useState(false);\r\n    const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);\r\n    const [nextBtnEnabled, setNextBtnEnabled] = useState(false);\r\n    const [currentIndex, setCurrentIndex] = useState(0);\r\n    const [previewImages, setPreviewImages] = useState<{ [key: string]: string }>({});\r\n\r\n    const containerVariants: Variants = {\r\n        rest: { opacity: 0, transition: { ease: 'easeIn', duration: 0.2 } },\r\n        hover: {\r\n            opacity: 1,\r\n            transition: {\r\n                duration: 0.3,\r\n                ease: 'easeOut',\r\n            },\r\n        },\r\n    };\r\n\r\n    const buttonVariants: Variants = {\r\n        rest: { opacity: 0, y: -5, transition: { ease: 'easeIn', duration: 0.2 } },\r\n        hover: {\r\n            opacity: 1,\r\n            y: 0,\r\n            transition: {\r\n                duration: 0.3,\r\n                type: 'tween',\r\n                ease: 'easeOut',\r\n            },\r\n        },\r\n    };\r\n\r\n    const [emblaRef, emblaApi] = useEmblaCarousel({\r\n        axis: 'y',\r\n        loop: false,\r\n        align: 'center',\r\n        containScroll: 'trimSnaps',\r\n        skipSnaps: false,\r\n        dragFree: false,\r\n    });\r\n\r\n    const scrollPrev = useCallback(() => emblaApi && emblaApi.scrollPrev(), [emblaApi]);\r\n    const scrollNext = useCallback(() => emblaApi && emblaApi.scrollNext(), [emblaApi]);\r\n\r\n    const onSelect = useCallback(() => {\r\n        if (!emblaApi) {\r\n            return;\r\n        }\r\n\r\n        setPrevBtnEnabled(emblaApi.canScrollPrev());\r\n        setNextBtnEnabled(emblaApi.canScrollNext());\r\n        setCurrentIndex(emblaApi.selectedScrollSnap());\r\n        onSlideChange(emblaApi.selectedScrollSnap());\r\n    }, [emblaApi, onSlideChange]);\r\n\r\n    useEffect(() => {\r\n        if (!emblaApi) {\r\n            return;\r\n        }\r\n        onSelect();\r\n        emblaApi.on('select', onSelect);\r\n        emblaApi.on('reInit', onSelect);\r\n    }, [emblaApi, onSelect]);\r\n\r\n    useEffect(() => {\r\n        const handleKeyDown = (event: KeyboardEvent) => {\r\n            if (event.key === 'ArrowUp') {\r\n                event.preventDefault();\r\n                scrollPrev();\r\n            } else if (event.key === 'ArrowDown') {\r\n                event.preventDefault();\r\n                scrollNext();\r\n            }\r\n        };\r\n\r\n        window.addEventListener('keydown', handleKeyDown);\r\n        return () => window.removeEventListener('keydown', handleKeyDown);\r\n    }, [scrollPrev, scrollNext]);\r\n\r\n    useEffect(() => {\r\n        const loadPreviewImages = async () => {\r\n            const images: { [key: string]: string } = {};\r\n            for (const slide of slides) {\r\n                if (slide.metadata.previewImg) {\r\n                    const img = await getFileUrlFromStorage(STORAGE_BUCKETS.PREVIEW_IMAGES, slide.metadata.previewImg.storagePath?.path ?? '');\r\n                    if (img) {\r\n                        images[slide.id] = img;\r\n                    } else {\r\n                        console.error(`Failed to load preview image for slide ${slide.id}`);\r\n                    }\r\n                }\r\n            }\r\n            setPreviewImages(images);\r\n        };\r\n        loadPreviewImages();\r\n    }, [slides]);\r\n\r\n    const setTweenNodes = useCallback((emblaApi: EmblaCarouselType): void => {\r\n        tweenNodes.current = emblaApi.slideNodes().map((slideNode) => {\r\n            return slideNode as HTMLElement;\r\n        });\r\n    }, []);\r\n\r\n    const setTweenFactor = useCallback((emblaApi: EmblaCarouselType) => {\r\n        tweenFactor.current = TWEEN_FACTOR_BASE * emblaApi.scrollSnapList().length;\r\n    }, []);\r\n\r\n    const tweenScale = useCallback((emblaApi: EmblaCarouselType, eventName?: EmblaEventType) => {\r\n        const engine = emblaApi.internalEngine();\r\n        const scrollProgress = emblaApi.scrollProgress();\r\n        const slidesInView = emblaApi.slidesInView();\r\n        const isScrollEvent = eventName === 'scroll';\r\n\r\n        emblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {\r\n            let diffToTarget = scrollSnap - scrollProgress;\r\n            const slidesInSnap = engine.slideRegistry[snapIndex];\r\n\r\n            slidesInSnap?.forEach((slideIndex) => {\r\n                if (isScrollEvent && !slidesInView.includes(slideIndex)) {\r\n                    return;\r\n                }\r\n\r\n                if (engine.options.loop) {\r\n                    engine.slideLooper.loopPoints.forEach((loopItem) => {\r\n                        const target = loopItem.target();\r\n\r\n                        if (slideIndex === loopItem.index && target !== 0) {\r\n                            const sign = Math.sign(target);\r\n\r\n                            if (sign === -1) {\r\n                                diffToTarget = scrollSnap - (1 + scrollProgress);\r\n                            }\r\n                            if (sign === 1) {\r\n                                diffToTarget = scrollSnap + (1 - scrollProgress);\r\n                            }\r\n                        }\r\n                    });\r\n                }\r\n\r\n                const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current);\r\n                const scale = numberWithinRange(tweenValue, 0, 1).toString();\r\n                const tweenNode = tweenNodes.current[slideIndex];\r\n                if (tweenNode) {\r\n                    tweenNode.style.transform = `scale(${scale})`;\r\n                }\r\n            });\r\n        });\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (!emblaApi) {\r\n            return;\r\n        }\r\n\r\n        setTweenNodes(emblaApi);\r\n        setTweenFactor(emblaApi);\r\n        tweenScale(emblaApi);\r\n\r\n        emblaApi\r\n            .on('reInit', setTweenNodes)\r\n            .on('reInit', setTweenFactor)\r\n            .on('reInit', tweenScale)\r\n            .on('scroll', tweenScale)\r\n            .on('slideFocus', tweenScale);\r\n    }, [emblaApi, tweenScale]);\r\n\r\n    const debouncedScroll = useCallback(\r\n        (deltaY: number) => {\r\n            if (scrollTimeout.current) {\r\n                clearTimeout(scrollTimeout.current);\r\n            }\r\n            scrollTimeout.current = setTimeout(() => {\r\n                setIsScrolling(false);\r\n            }, SCROLL_COOLDOWN);\r\n\r\n            if (isScrolling) {\r\n                return;\r\n            }\r\n            setIsScrolling(true);\r\n\r\n            if (deltaY > 0) {\r\n                scrollNext();\r\n            } else {\r\n                scrollPrev();\r\n            }\r\n        },\r\n        [isScrolling, scrollNext, scrollPrev],\r\n    );\r\n\r\n    const handleWheel = useCallback(\r\n        (e: React.WheelEvent) => {\r\n            if (Math.abs(e.deltaY) > WHEEL_SENSITIVITY) {\r\n                debouncedScroll(e.deltaY);\r\n            }\r\n        },\r\n        [debouncedScroll],\r\n    );\r\n\r\n    useEffect(() => {\r\n        return () => {\r\n            if (scrollTimeout.current) {\r\n                clearTimeout(scrollTimeout.current);\r\n            }\r\n        };\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"embla relative h-full overflow-hidden\" style={{ zIndex: 0 }}>\r\n            <div\r\n                className=\"embla__viewport h-full absolute inset-0 overflow-hidden pl-[7.5rem]\"\r\n                ref={emblaRef}\r\n                style={{\r\n                    transition: 'transform 0.2s cubic-bezier(0.25, 1, 0.5, 1)',\r\n                    zIndex: -1,\r\n                }}\r\n            >\r\n                <div\r\n                    className=\"embla__container flex flex-col h-full items-center px-16\"\r\n                    style={{ marginTop: '0' }}\r\n                    onWheel={handleWheel}\r\n                >\r\n                    {slides.map((slide, index) => (\r\n                        <div\r\n                            key={slide.id}\r\n                            className=\"embla__slide flex items-center justify-center select-none max-h-[70vh]\"\r\n                            style={{\r\n                                flex: '0 0 80%',\r\n                                minWidth: 0,\r\n                                transform: 'translate3d(0, 0, 0)',\r\n                                marginTop: index === 0 ? '6rem' : '-3rem',\r\n                                marginBottom: index === slides.length - 1 ? '6rem' : '-3rem',\r\n                                opacity: index === currentIndex ? 1 : 0.6,\r\n                                zIndex: index === currentIndex ? 2 : 1,\r\n                            }}\r\n                        >\r\n                            <div className=\"relative bg-background\">\r\n                                {previewImages[slide.id] ? (\r\n                                    <img\r\n                                        src={previewImages[slide.id] ?? ''}\r\n                                        alt={slide.name}\r\n                                        className=\"rounded-lg object-cover max-w-full max-h-[80%] bg-foreground border-[0.5px]\"\r\n                                    />\r\n                                ) : (\r\n                                    <div className=\"w-[30rem] h-[40rem] rounded-lg bg-gradient-to-t from-gray-800/40 via-gray-500/40 to-gray-400/40 border-gray-500 border-[0.5px]\" />\r\n                                )}\r\n                                <motion.div\r\n                                    initial=\"rest\"\r\n                                    whileHover=\"hover\"\r\n                                    animate=\"rest\"\r\n                                    variants={containerVariants}\r\n                                    className=\"rounded-lg absolute inset-0 flex items-center justify-center z-10 bg-background/30\"\r\n                                >\r\n                                    <EditAppButton variants={buttonVariants} project={slide} />\r\n                                </motion.div>\r\n                            </div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n            <div className=\"bg-secondary/20 backdrop-blur p-2 rounded-lg embla__buttons absolute left-14 top-1/2 transform -translate-y-1/2 flex flex-col gap-4 z-10 items-center\">\r\n                <button\r\n                    className=\"embla__button embla__button--prev\"\r\n                    onClick={scrollPrev}\r\n                    disabled={!prevBtnEnabled}\r\n                >\r\n                    <Icons.ChevronUp\r\n                        className={`w-7 h-7 transition duration-300 ease-in-out ${prevBtnEnabled ? 'text-foreground' : 'text-muted'}`}\r\n                    />\r\n                </button>\r\n                <div className=\"flex flex-row space-x-1 text-foreground items-center justify-center min-w-[50px]\">\r\n                    <span className=\"text-active\">{currentIndex + 1}</span>\r\n                    <span className=\"text-sm text-gray-500\"> of </span>\r\n                    <span className=\"text-active\">{slides.length}</span>\r\n                </div>\r\n                <button\r\n                    className=\"embla__button embla__button--next\"\r\n                    onClick={scrollNext}\r\n                    disabled={!nextBtnEnabled}\r\n                >\r\n                    <Icons.ChevronDown\r\n                        className={`w-7 h-7 transition duration-300 ease-in-out ${nextBtnEnabled ? 'text-foreground' : 'text-muted'}`}\r\n                    />\r\n                </button>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;AAEA;AACA;AAEA;AACA;;;;;;;;;;AAOA,MAAM,oBAAoB,CAAC,QAAgB,KAAa,MACpD,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,QAAQ,MAAM;AAE7B,MAAM,WAAoC,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE;;IACvE,MAAM,oBAAoB;IAC1B,MAAM,kBAAkB;IACxB,MAAM,oBAAoB;IAE1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB,EAAE;IAC3C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAE3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAE/E,MAAM,oBAA8B;QAChC,MAAM;YAAE,SAAS;YAAG,YAAY;gBAAE,MAAM;gBAAU,UAAU;YAAI;QAAE;QAClE,OAAO;YACH,SAAS;YACT,YAAY;gBACR,UAAU;gBACV,MAAM;YACV;QACJ;IACJ;IAEA,MAAM,iBAA2B;QAC7B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;YAAG,YAAY;gBAAE,MAAM;gBAAU,UAAU;YAAI;QAAE;QACzE,OAAO;YACH,SAAS;YACT,GAAG;YACH,YAAY;gBACR,UAAU;gBACV,MAAM;gBACN,MAAM;YACV;QACJ;IACJ;IAEA,MAAM,CAAC,UAAU,SAAS,GAAG,CAAA,GAAA,yLAAA,CAAA,UAAgB,AAAD,EAAE;QAC1C,MAAM;QACN,MAAM;QACN,OAAO;QACP,eAAe;QACf,WAAW;QACX,UAAU;IACd;IAEA,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,IAAM,YAAY,SAAS,UAAU;2CAAI;QAAC;KAAS;IAClF,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,IAAM,YAAY,SAAS,UAAU;2CAAI;QAAC;KAAS;IAElF,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YACzB,IAAI,CAAC,UAAU;gBACX;YACJ;YAEA,kBAAkB,SAAS,aAAa;YACxC,kBAAkB,SAAS,aAAa;YACxC,gBAAgB,SAAS,kBAAkB;YAC3C,cAAc,SAAS,kBAAkB;QAC7C;yCAAG;QAAC;QAAU;KAAc;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,CAAC,UAAU;gBACX;YACJ;YACA;YACA,SAAS,EAAE,CAAC,UAAU;YACtB,SAAS,EAAE,CAAC,UAAU;QAC1B;6BAAG;QAAC;QAAU;KAAS;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,MAAM;oDAAgB,CAAC;oBACnB,IAAI,MAAM,GAAG,KAAK,WAAW;wBACzB,MAAM,cAAc;wBACpB;oBACJ,OAAO,IAAI,MAAM,GAAG,KAAK,aAAa;wBAClC,MAAM,cAAc;wBACpB;oBACJ;gBACJ;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;sCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACvD;6BAAG;QAAC;QAAY;KAAW;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,MAAM;wDAAoB;oBACtB,MAAM,SAAoC,CAAC;oBAC3C,KAAK,MAAM,SAAS,OAAQ;wBACxB,IAAI,MAAM,QAAQ,CAAC,UAAU,EAAE;4BAC3B,MAAM,MAAM,MAAM,CAAA,GAAA,uKAAA,CAAA,wBAAqB,AAAD,EAAE,0IAAA,CAAA,kBAAe,CAAC,cAAc,EAAE,MAAM,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ;4BACvH,IAAI,KAAK;gCACL,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG;4BACvB,OAAO;gCACH,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,EAAE,EAAE;4BACtE;wBACJ;oBACJ;oBACA,iBAAiB;gBACrB;;YACA;QACJ;6BAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAC/B,WAAW,OAAO,GAAG,SAAS,UAAU,GAAG,GAAG;uDAAC,CAAC;oBAC5C,OAAO;gBACX;;QACJ;8CAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAChC,YAAY,OAAO,GAAG,oBAAoB,SAAS,cAAc,GAAG,MAAM;QAC9E;+CAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC,UAA6B;YACzD,MAAM,SAAS,SAAS,cAAc;YACtC,MAAM,iBAAiB,SAAS,cAAc;YAC9C,MAAM,eAAe,SAAS,YAAY;YAC1C,MAAM,gBAAgB,cAAc;YAEpC,SAAS,cAAc,GAAG,OAAO;oDAAC,CAAC,YAAY;oBAC3C,IAAI,eAAe,aAAa;oBAChC,MAAM,eAAe,OAAO,aAAa,CAAC,UAAU;oBAEpD,cAAc;4DAAQ,CAAC;4BACnB,IAAI,iBAAiB,CAAC,aAAa,QAAQ,CAAC,aAAa;gCACrD;4BACJ;4BAEA,IAAI,OAAO,OAAO,CAAC,IAAI,EAAE;gCACrB,OAAO,WAAW,CAAC,UAAU,CAAC,OAAO;wEAAC,CAAC;wCACnC,MAAM,SAAS,SAAS,MAAM;wCAE9B,IAAI,eAAe,SAAS,KAAK,IAAI,WAAW,GAAG;4CAC/C,MAAM,OAAO,KAAK,IAAI,CAAC;4CAEvB,IAAI,SAAS,CAAC,GAAG;gDACb,eAAe,aAAa,CAAC,IAAI,cAAc;4CACnD;4CACA,IAAI,SAAS,GAAG;gDACZ,eAAe,aAAa,CAAC,IAAI,cAAc;4CACnD;wCACJ;oCACJ;;4BACJ;4BAEA,MAAM,aAAa,IAAI,KAAK,GAAG,CAAC,eAAe,YAAY,OAAO;4BAClE,MAAM,QAAQ,kBAAkB,YAAY,GAAG,GAAG,QAAQ;4BAC1D,MAAM,YAAY,WAAW,OAAO,CAAC,WAAW;4BAChD,IAAI,WAAW;gCACX,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;4BACjD;wBACJ;;gBACJ;;QACJ;2CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,CAAC,UAAU;gBACX;YACJ;YAEA,cAAc;YACd,eAAe;YACf,WAAW;YAEX,SACK,EAAE,CAAC,UAAU,eACb,EAAE,CAAC,UAAU,gBACb,EAAE,CAAC,UAAU,YACb,EAAE,CAAC,UAAU,YACb,EAAE,CAAC,cAAc;QAC1B;6BAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAC9B,CAAC;YACG,IAAI,cAAc,OAAO,EAAE;gBACvB,aAAa,cAAc,OAAO;YACtC;YACA,cAAc,OAAO,GAAG;yDAAW;oBAC/B,eAAe;gBACnB;wDAAG;YAEH,IAAI,aAAa;gBACb;YACJ;YACA,eAAe;YAEf,IAAI,SAAS,GAAG;gBACZ;YACJ,OAAO;gBACH;YACJ;QACJ;gDACA;QAAC;QAAa;QAAY;KAAW;IAGzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAC1B,CAAC;YACG,IAAI,KAAK,GAAG,CAAC,EAAE,MAAM,IAAI,mBAAmB;gBACxC,gBAAgB,EAAE,MAAM;YAC5B;QACJ;4CACA;QAAC;KAAgB;IAGrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN;sCAAO;oBACH,IAAI,cAAc,OAAO,EAAE;wBACvB,aAAa,cAAc,OAAO;oBACtC;gBACJ;;QACJ;6BAAG,EAAE;IAEL,qBACI,6LAAC;QAAI,WAAU;QAAwC,OAAO;YAAE,QAAQ;QAAE;;0BACtE,6LAAC;gBACG,WAAU;gBACV,KAAK;gBACL,OAAO;oBACH,YAAY;oBACZ,QAAQ,CAAC;gBACb;0BAEA,cAAA,6LAAC;oBACG,WAAU;oBACV,OAAO;wBAAE,WAAW;oBAAI;oBACxB,SAAS;8BAER,OAAO,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;4BAEG,WAAU;4BACV,OAAO;gCACH,MAAM;gCACN,UAAU;gCACV,WAAW;gCACX,WAAW,UAAU,IAAI,SAAS;gCAClC,cAAc,UAAU,OAAO,MAAM,GAAG,IAAI,SAAS;gCACrD,SAAS,UAAU,eAAe,IAAI;gCACtC,QAAQ,UAAU,eAAe,IAAI;4BACzC;sCAEA,cAAA,6LAAC;gCAAI,WAAU;;oCACV,aAAa,CAAC,MAAM,EAAE,CAAC,iBACpB,6LAAC;wCACG,KAAK,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI;wCAChC,KAAK,MAAM,IAAI;wCACf,WAAU;;;;;6DAGd,6LAAC;wCAAI,WAAU;;;;;;kDAEnB,6LAAC,8OAAA,CAAA,SAAM,CAAC,GAAG;wCACP,SAAQ;wCACR,YAAW;wCACX,SAAQ;wCACR,UAAU;wCACV,WAAU;kDAEV,cAAA,6LAAC,iLAAA,CAAA,gBAAa;4CAAC,UAAU;4CAAgB,SAAS;;;;;;;;;;;;;;;;;2BA7BrD,MAAM,EAAE;;;;;;;;;;;;;;;0BAoC7B,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBACG,WAAU;wBACV,SAAS;wBACT,UAAU,CAAC;kCAEX,cAAA,6LAAC,yJAAA,CAAA,QAAK,CAAC,SAAS;4BACZ,WAAW,CAAC,4CAA4C,EAAE,iBAAiB,oBAAoB,cAAc;;;;;;;;;;;kCAGrH,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAK,WAAU;0CAAe,eAAe;;;;;;0CAC9C,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAK,WAAU;0CAAe,OAAO,MAAM;;;;;;;;;;;;kCAEhD,6LAAC;wBACG,WAAU;wBACV,SAAS;wBACT,UAAU,CAAC;kCAEX,cAAA,6LAAC,yJAAA,CAAA,QAAK,CAAC,WAAW;4BACd,WAAW,CAAC,4CAA4C,EAAE,iBAAiB,oBAAoB,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMrI;GArSa;;QAuCoB,yLAAA,CAAA,UAAgB;;;KAvCpC", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/_components/settings.tsx"], "sourcesContent": ["import { useProjectsManager } from '@/components/store/projects';\r\nimport { transKeys } from '@/i18n/keys';\r\nimport type { Project } from '@onlook/models';\r\nimport {\r\n    AlertDialog,\r\n    AlertDialogContent,\r\n    AlertDialogDescription,\r\n    AlertDialogFooter,\r\n    AlertDialogHeader,\r\n    AlertDialogTitle,\r\n} from '@onlook/ui/alert-dialog';\r\nimport { Button } from '@onlook/ui/button';\r\nimport {\r\n    DropdownMenu,\r\n    DropdownMenuContent,\r\n    DropdownMenuItem,\r\n    DropdownMenuTrigger,\r\n} from '@onlook/ui/dropdown-menu';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { Input } from '@onlook/ui/input';\r\nimport { Label } from '@onlook/ui/label';\r\nimport { cn } from '@onlook/ui/utils';\r\nimport { useTranslations } from 'next-intl';\r\nimport { useEffect, useMemo, useState } from 'react';\r\n\r\nexport function Settings({ project }: { project: Project }) {\r\n    const projectsManager = useProjectsManager();\r\n    const t = useTranslations();\r\n    const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n    const [showRenameDialog, setShowRenameDialog] = useState(false);\r\n    const [projectName, setProjectName] = useState(project.name);\r\n    const isProjectNameEmpty = useMemo(() => projectName.length === 0, [projectName]);\r\n    const [isDirectoryHovered, setIsDirectoryHovered] = useState(false);\r\n\r\n    useEffect(() => {\r\n        setProjectName(project.name);\r\n    }, [project.name]);\r\n\r\n    const handleDeleteProject = () => {\r\n        projectsManager.deleteProject(project);\r\n        setShowDeleteDialog(false);\r\n    };\r\n\r\n    const handleRenameProject = () => {\r\n        projectsManager.updateProject({ ...project, name: projectName });\r\n        setShowRenameDialog(false);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                    <Button size=\"default\" variant=\"ghost\" className=\"w-10 h-10 p-0 flex items-center justify-center hover:bg-background-onlook cursor-pointer\">\r\n                        <Icons.DotsVertical />\r\n                    </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent>\r\n                    <DropdownMenuItem\r\n                        onSelect={() => setShowRenameDialog(true)}\r\n                        className=\"text-foreground-active hover:!bg-background-onlook hover:!text-foreground-active gap-2\"\r\n                    >\r\n                        <Icons.Pencil className=\"w-4 h-4\" />\r\n                        {t(transKeys.projects.actions.renameProject)}\r\n                    </DropdownMenuItem>\r\n                    <DropdownMenuItem\r\n                        onSelect={() => setShowDeleteDialog(true)}\r\n                        className=\"gap-2 text-red-400 hover:!bg-red-200/80 hover:!text-red-700 dark:text-red-200 dark:hover:!bg-red-800 dark:hover:!text-red-100\"\r\n                    >\r\n                        <Icons.Trash className=\"w-4 h-4\" />\r\n                        {t(transKeys.projects.actions.deleteProject)}\r\n                    </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n            </DropdownMenu>\r\n\r\n            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n                <AlertDialogContent>\r\n                    <AlertDialogHeader>\r\n                        <AlertDialogTitle>{t(transKeys.projects.dialogs.delete.title)}</AlertDialogTitle>\r\n                        <AlertDialogDescription>\r\n                            {t(transKeys.projects.dialogs.delete.description)}\r\n                        </AlertDialogDescription>\r\n                    </AlertDialogHeader>\r\n                    <AlertDialogFooter>\r\n                        <Button variant={'ghost'} onClick={() => setShowDeleteDialog(false)}>\r\n                            {t(transKeys.projects.actions.cancel)}\r\n                        </Button>\r\n                        <Button\r\n                            variant={'destructive'}\r\n                            className=\"rounded-md text-sm\"\r\n                            onClick={handleDeleteProject}\r\n                        >\r\n                            {t(transKeys.projects.actions.delete)}\r\n                        </Button>\r\n                    </AlertDialogFooter>\r\n                </AlertDialogContent>\r\n            </AlertDialog>\r\n            <AlertDialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>\r\n                <AlertDialogContent>\r\n                    <AlertDialogHeader>\r\n                        <AlertDialogTitle>{t(transKeys.projects.dialogs.rename.title)}</AlertDialogTitle>\r\n                    </AlertDialogHeader>\r\n                    <div className=\"flex flex-col w-full gap-2\">\r\n                        <Label htmlFor=\"text\">{t(transKeys.projects.dialogs.rename.label)}</Label>\r\n                        <Input\r\n                            minLength={0}\r\n                            type=\"text\"\r\n                            value={projectName || ''}\r\n                            onInput={(e) => setProjectName(e.currentTarget.value)}\r\n                        />\r\n                        <p\r\n                            className={cn(\r\n                                'text-xs text-red-500 transition-opacity',\r\n                                isProjectNameEmpty ? 'opacity-100' : 'opacity-0',\r\n                            )}\r\n                        >\r\n                            {t(transKeys.projects.dialogs.rename.error)}\r\n                        </p>\r\n                    </div>\r\n                    <AlertDialogFooter>\r\n                        <Button variant={'ghost'} onClick={() => setShowRenameDialog(false)}>\r\n                            {t(transKeys.projects.actions.cancel)}\r\n                        </Button>\r\n                        <Button\r\n                            disabled={isProjectNameEmpty}\r\n                            className=\"rounded-md text-sm\"\r\n                            onClick={handleRenameProject}\r\n                        >\r\n                            {t(transKeys.projects.actions.rename)}\r\n                        </Button>\r\n                    </AlertDialogFooter>\r\n                </AlertDialogContent>\r\n            </AlertDialog>\r\n        </>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AAQA;AACA;AAMA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;;;AAEO,SAAS,SAAS,EAAE,OAAO,EAAwB;;IACtD,MAAM,kBAAkB,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI;IAC3D,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM,YAAY,MAAM,KAAK;+CAAG;QAAC;KAAY;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,eAAe,QAAQ,IAAI;QAC/B;6BAAG;QAAC,QAAQ,IAAI;KAAC;IAEjB,MAAM,sBAAsB;QACxB,gBAAgB,aAAa,CAAC;QAC9B,oBAAoB;IACxB;IAEA,MAAM,sBAAsB;QACxB,gBAAgB,aAAa,CAAC;YAAE,GAAG,OAAO;YAAE,MAAM;QAAY;QAC9D,oBAAoB;IACxB;IAEA,qBACI;;0BACI,6LAAC,2JAAA,CAAA,eAAY;;kCACT,6LAAC,2JAAA,CAAA,sBAAmB;wBAAC,OAAO;kCACxB,cAAA,6LAAC,iJAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,SAAQ;4BAAQ,WAAU;sCAC7C,cAAA,6LAAC,yJAAA,CAAA,QAAK,CAAC,YAAY;;;;;;;;;;;;;;;kCAG3B,6LAAC,2JAAA,CAAA,sBAAmB;;0CAChB,6LAAC,2JAAA,CAAA,mBAAgB;gCACb,UAAU,IAAM,oBAAoB;gCACpC,WAAU;;kDAEV,6LAAC,yJAAA,CAAA,QAAK,CAAC,MAAM;wCAAC,WAAU;;;;;;oCACvB,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa;;;;;;;0CAE/C,6LAAC,2JAAA,CAAA,mBAAgB;gCACb,UAAU,IAAM,oBAAoB;gCACpC,WAAU;;kDAEV,6LAAC,yJAAA,CAAA,QAAK,CAAC,KAAK;wCAAC,WAAU;;;;;;oCACtB,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa;;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC,0JAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BAC/C,cAAA,6LAAC,0JAAA,CAAA,qBAAkB;;sCACf,6LAAC,0JAAA,CAAA,oBAAiB;;8CACd,6LAAC,0JAAA,CAAA,mBAAgB;8CAAE,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;;;;;8CAC5D,6LAAC,0JAAA,CAAA,yBAAsB;8CAClB,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW;;;;;;;;;;;;sCAGxD,6LAAC,0JAAA,CAAA,oBAAiB;;8CACd,6LAAC,iJAAA,CAAA,SAAM;oCAAC,SAAS;oCAAS,SAAS,IAAM,oBAAoB;8CACxD,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;8CAExC,6LAAC,iJAAA,CAAA,SAAM;oCACH,SAAS;oCACT,WAAU;oCACV,SAAS;8CAER,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAKpD,6LAAC,0JAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BAC/C,cAAA,6LAAC,0JAAA,CAAA,qBAAkB;;sCACf,6LAAC,0JAAA,CAAA,oBAAiB;sCACd,cAAA,6LAAC,0JAAA,CAAA,mBAAgB;0CAAE,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;sCAEhE,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,gJAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;;;;;8CAChE,6LAAC,gJAAA,CAAA,QAAK;oCACF,WAAW;oCACX,MAAK;oCACL,OAAO,eAAe;oCACtB,SAAS,CAAC,IAAM,eAAe,EAAE,aAAa,CAAC,KAAK;;;;;;8CAExD,6LAAC;oCACG,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACR,2CACA,qBAAqB,gBAAgB;8CAGxC,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAGlD,6LAAC,0JAAA,CAAA,oBAAiB;;8CACd,6LAAC,iJAAA,CAAA,SAAM;oCAAC,SAAS;oCAAS,SAAS,IAAM,oBAAoB;8CACxD,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;8CAExC,6LAAC,iJAAA,CAAA,SAAM;oCACH,UAAU;oCACV,WAAU;oCACV,SAAS;8CAER,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;AAOhE;GA7GgB;;QACY,2KAAA,CAAA,qBAAkB;QAChC,yMAAA,CAAA,kBAAe;;;KAFb", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/_components/info.tsx"], "sourcesContent": ["import { transKeys } from '@/i18n/keys';\r\nimport type { Project } from '@onlook/models';\r\nimport { timeAgo } from '@onlook/utility';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { AnimatePresence, motion } from 'motion/react';\r\nimport { useTranslations } from 'next-intl';\r\nimport { useState } from 'react';\r\nimport { EditAppButton } from './edit-app';\r\nimport { Settings } from './settings';\r\n\r\nexport const ProjectInfo = observer(\r\n    ({ project, direction }: { project: Project; direction: number }) => {\r\n        const [favicon, setFavicon] = useState<string | null>(null);\r\n        const t = useTranslations();\r\n        const variants = {\r\n            enter: (direction: number) => ({\r\n                y: direction > 0 ? 20 : -20,\r\n                opacity: 0,\r\n            }),\r\n            center: {\r\n                y: 0,\r\n                opacity: 1,\r\n            },\r\n            exit: (direction: number) => ({\r\n                y: direction < 0 ? 20 : -20,\r\n                opacity: 0,\r\n            }),\r\n        };\r\n\r\n        // useEffect(() => {\r\n        //     loadFavicon();\r\n        // }, [project.sandbox.url]);\r\n\r\n        // const loadFavicon = async () => {\r\n        //     // How to get a favicon from a url?\r\n        //     const favicon = await getFavicon(project.sandbox.url);\r\n        //     if (favicon) {\r\n        //         setFavicon(favicon);\r\n        //     }\r\n        // }\r\n\r\n        return (\r\n            project && (\r\n                <div className=\"flex flex-col gap-4 max-w-[480px] w-full\">\r\n                    <div className=\"flex items-center gap-3 mb-1\">\r\n                        {/* TODO: This should show the favicon */}\r\n                        {/* {favicon && (\r\n                            <img\r\n                                src={favicon}\r\n                                alt=\"Preview\"\r\n                                className=\"w-8 h-8 rounded-lg bg-white object-cover border border-border\"\r\n                            />\r\n                        )} */}\r\n                        <span className=\"text-foreground-onlook text-regular\">{project.sandbox.url}</span>\r\n                    </div>\r\n                    <AnimatePresence mode=\"wait\" custom={direction}>\r\n                        <motion.p\r\n                            key={project.id}\r\n                            custom={direction}\r\n                            variants={variants}\r\n                            initial=\"enter\"\r\n                            animate=\"center\"\r\n                            exit=\"exit\"\r\n                            transition={{ duration: 0.3 }}\r\n                            className=\"inline-block text-foreground-active text-title1\"\r\n                        >\r\n                            {project.name}\r\n                        </motion.p>\r\n                    </AnimatePresence>\r\n                    <div className=\"flex flex-col gap-1\">\r\n                        <p className=\"text-foreground-tertiary text-regular mb-1 text-balance\">\r\n                            {project.metadata.description ?? 'No description'}\r\n                        </p>\r\n                    </div>\r\n                    <p className=\"text-foreground-tertiary text-mini mb-2\">\r\n                        {t(transKeys.projects.select.lastEdited, {\r\n                            time: timeAgo(new Date(project.metadata.updatedAt).toISOString()),\r\n                        })}\r\n                    </p>\r\n                    <div className=\"border-[0.5px] border-border w-full mb-2\" />\r\n                    <div className=\"flex items-center justify-between w-full gap-3 sm:gap-5\">\r\n                        <EditAppButton project={project} />\r\n                        <div className=\"flex-1\" />\r\n                        <Settings project={project} />\r\n                    </div>\r\n                </div>\r\n            )\r\n        );\r\n    },\r\n);\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,cAAc,GAAA,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,UAC9B,CAAC,EAAE,OAAO,EAAE,SAAS,EAA2C;;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,WAAW;QACb,OAAO,CAAC,YAAsB,CAAC;gBAC3B,GAAG,YAAY,IAAI,KAAK,CAAC;gBACzB,SAAS;YACb,CAAC;QACD,QAAQ;YACJ,GAAG;YACH,SAAS;QACb;QACA,MAAM,CAAC,YAAsB,CAAC;gBAC1B,GAAG,YAAY,IAAI,KAAK,CAAC;gBACzB,SAAS;YACb,CAAC;IACL;IAEA,oBAAoB;IACpB,qBAAqB;IACrB,6BAA6B;IAE7B,oCAAoC;IACpC,0CAA0C;IAC1C,6DAA6D;IAC7D,qBAAqB;IACrB,+BAA+B;IAC/B,QAAQ;IACR,IAAI;IAEJ,OACI,yBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;0BASX,cAAA,6LAAC;oBAAK,WAAU;8BAAuC,QAAQ,OAAO,CAAC,GAAG;;;;;;;;;;;0BAE9E,6LAAC,6OAAA,CAAA,kBAAe;gBAAC,MAAK;gBAAO,QAAQ;0BACjC,cAAA,6LAAC,8OAAA,CAAA,SAAM,CAAC,CAAC;oBAEL,QAAQ;oBACR,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAET,QAAQ,IAAI;mBATR,QAAQ,EAAE;;;;;;;;;;0BAYvB,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAE,WAAU;8BACR,QAAQ,QAAQ,CAAC,WAAW,IAAI;;;;;;;;;;;0BAGzC,6LAAC;gBAAE,WAAU;0BACR,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE;oBACrC,MAAM,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,KAAK,QAAQ,QAAQ,CAAC,SAAS,EAAE,WAAW;gBAClE;;;;;;0BAEJ,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,iLAAA,CAAA,gBAAa;wBAAC,SAAS;;;;;;kCACxB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,8KAAA,CAAA,WAAQ;wBAAC,SAAS;;;;;;;;;;;;;;;;;;AAKvC;;QA3Ec,yMAAA,CAAA,kBAAe;;;;QAAf,yMAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/_components/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useProjectsManager } from '@/components/store/projects';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { observer } from 'mobx-react-lite';\r\nimport Link from 'next/link';\r\nimport { useState } from 'react';\r\nimport { Carousel } from './carousel';\r\nimport { ProjectInfo } from './info';\r\n\r\nexport const SelectProject = observer(() => {\r\n    const projectsManager = useProjectsManager();\r\n    const [currentProjectIndex, setCurrentProjectIndex] = useState(0);\r\n    const [direction, setDirection] = useState(0);\r\n\r\n    const projects = projectsManager.projects\r\n        .toSorted(\r\n            (a, b) =>\r\n                new Date(b.metadata.updatedAt).getTime() -\r\n                new Date(a.metadata.updatedAt).getTime(),\r\n        );\r\n\r\n    const handleProjectChange: (index: number) => void = (index: number) => {\r\n        if (currentProjectIndex === index) {\r\n            return;\r\n        }\r\n        setDirection(index > currentProjectIndex ? 1 : -1);\r\n        setCurrentProjectIndex(index);\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-row w-full\">\r\n            {projects.length === 0 ? (\r\n                <div className=\"w-full h-full flex flex-col items-center justify-center gap-4\">\r\n                    <div className=\"text-xl text-foreground-secondary\">No projects found</div>\r\n                    <div className=\"text-md text-foreground-tertiary\">Create a new project to get started</div>\r\n                    <div className=\"flex justify-center\">\r\n                        <Link\r\n                            href=\"/\"\r\n                            className=\"inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary\"\r\n                        >\r\n                            <Icons.ArrowLeft className=\"h-4 w-4\" />\r\n                            Back to home\r\n                        </Link>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <>\r\n                    <div className=\"w-3/5 h-full\">\r\n                        <Carousel slides={projects} onSlideChange={handleProjectChange} />\r\n                    </div>\r\n                    <div className=\"w-2/5 flex flex-col justify-center items-start p-4 mr-10 gap-6\">\r\n                        {projects[currentProjectIndex] && (\r\n                            <ProjectInfo project={projects[currentProjectIndex]} direction={direction} />\r\n                        )}\r\n                    </div>\r\n                </>\r\n            )}\r\n        </div>\r\n    );\r\n});\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUO,MAAM,gBAAgB,GAAA,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,UAAE;;IAClC,MAAM,kBAAkB,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,WAAW,gBAAgB,QAAQ,CACpC,QAAQ,CACL,CAAC,GAAG,IACA,IAAI,KAAK,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO,KACtC,IAAI,KAAK,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO;IAGlD,MAAM,sBAA+C,CAAC;QAClD,IAAI,wBAAwB,OAAO;YAC/B;QACJ;QACA,aAAa,QAAQ,sBAAsB,IAAI,CAAC;QAChD,uBAAuB;IAC3B;IAEA,qBACI,6LAAC;QAAI,WAAU;kBACV,SAAS,MAAM,KAAK,kBACjB,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BAAoC;;;;;;8BACnD,6LAAC;oBAAI,WAAU;8BAAmC;;;;;;8BAClD,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACD,MAAK;wBACL,WAAU;;0CAEV,6LAAC,yJAAA,CAAA,QAAK,CAAC,SAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;iCAMnD;;8BACI,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,8KAAA,CAAA,WAAQ;wBAAC,QAAQ;wBAAU,eAAe;;;;;;;;;;;8BAE/C,6LAAC;oBAAI,WAAU;8BACV,QAAQ,CAAC,oBAAoB,kBAC1B,6LAAC,0KAAA,CAAA,cAAW;wBAAC,SAAS,QAAQ,CAAC,oBAAoB;wBAAE,WAAW;;;;;;;;;;;;;;;;;;AAO5F;;QAjD4B,2KAAA,CAAA,qBAAkB;;;;QAAlB,2KAAA,CAAA,qBAAkB", "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/_components/top-bar.tsx"], "sourcesContent": ["import { CurrentUserAvatar } from '@/components/ui/avatar-dropdown';\r\nimport { transKeys } from '@/i18n/keys';\r\nimport { Routes } from '@/utils/constants';\r\nimport { Button } from '@onlook/ui/button';\r\nimport {\r\n    DropdownMenu,\r\n    DropdownMenuContent,\r\n    DropdownMenuItem,\r\n    DropdownMenuTrigger,\r\n} from '@onlook/ui/dropdown-menu';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { cn } from '@onlook/ui/utils';\r\nimport { useTranslations } from 'next-intl';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nexport const TopBar = () => {\r\n    const t = useTranslations();\r\n    const router = useRouter();\r\n\r\n    return (\r\n        <div className=\"w-full max-w-6xl mx-auto flex items-center justify-between p-4 h-12 text-small text-foreground-secondary select-none\">\r\n            <Link href={Routes.HOME} className=\"flex-1 flex items-center justify-start mt-0 py-3\">\r\n                <Icons.OnlookTextLogo className=\"w-24\" viewBox=\"0 0 139 17\" />\r\n            </Link>\r\n            <div className=\"flex-1 flex justify-end space-x-2 mt-0 items-center\">\r\n                <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                        <Button\r\n                            className=\"text-sm text-foreground-onlook focus:outline-none cursor-pointer\"\r\n                            variant=\"ghost\"\r\n                        >\r\n                            <Icons.Plus className=\"w-5 h-5 mr-1\" />\r\n                            New Project\r\n                        </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent>\r\n                        <DropdownMenuItem\r\n                            className={cn(\r\n                                'focus:bg-blue-100 focus:text-blue-900',\r\n                                'hover:bg-blue-100 hover:text-blue-900',\r\n                                'dark:focus:bg-blue-900 dark:focus:text-blue-100',\r\n                                'dark:hover:bg-blue-900 dark:hover:text-blue-100',\r\n                                'cursor-pointer select-none group',\r\n                            )}\r\n                            onSelect={() => {\r\n                                router.push(Routes.HOME);\r\n                            }}\r\n                        >\r\n                            <Icons.Plus className=\"w-4 h-4 mr-1 text-foreground-secondary group-hover:text-blue-100\" />\r\n                            {t(transKeys.projects.actions.newProject)}\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem\r\n                            className={cn(\r\n                                'focus:bg-teal-100 focus:text-teal-900',\r\n                                'hover:bg-teal-100 hover:text-teal-900',\r\n                                'dark:focus:bg-teal-900 dark:focus:text-teal-100',\r\n                                'dark:hover:bg-teal-900 dark:hover:text-teal-100',\r\n                                'cursor-pointer select-none group',\r\n                            )}\r\n                            onSelect={() => {\r\n                                router.push(Routes.IMPORT_PROJECT);\r\n                            }}\r\n                        >\r\n                            <Icons.Upload className=\"w-4 h-4 mr-1 text-foreground-secondary group-hover:text-teal-100\" />\r\n                            <p className=\"text-microPlus\">{t(transKeys.projects.actions.import)}</p>\r\n                        </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                </DropdownMenu>\r\n                <CurrentUserAvatar className=\"w-8 h-8\" />\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;AAEO,MAAM,SAAS;;IAClB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,8JAAA,CAAA,SAAM,CAAC,IAAI;gBAAE,WAAU;0BAC/B,cAAA,6LAAC,yJAAA,CAAA,QAAK,CAAC,cAAc;oBAAC,WAAU;oBAAO,SAAQ;;;;;;;;;;;0BAEnD,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,2JAAA,CAAA,eAAY;;0CACT,6LAAC,2JAAA,CAAA,sBAAmB;gCAAC,OAAO;0CACxB,cAAA,6LAAC,iJAAA,CAAA,SAAM;oCACH,WAAU;oCACV,SAAQ;;sDAER,6LAAC,yJAAA,CAAA,QAAK,CAAC,IAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI/C,6LAAC,2JAAA,CAAA,sBAAmB;;kDAChB,6LAAC,2JAAA,CAAA,mBAAgB;wCACb,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACR,yCACA,yCACA,mDACA,mDACA;wCAEJ,UAAU;4CACN,OAAO,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,IAAI;wCAC3B;;0DAEA,6LAAC,yJAAA,CAAA,QAAK,CAAC,IAAI;gDAAC,WAAU;;;;;;4CACrB,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU;;;;;;;kDAE5C,6LAAC,2JAAA,CAAA,mBAAgB;wCACb,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACR,yCACA,yCACA,mDACA,mDACA;wCAEJ,UAAU;4CACN,OAAO,IAAI,CAAC,8JAAA,CAAA,SAAM,CAAC,cAAc;wCACrC;;0DAEA,6LAAC,yJAAA,CAAA,QAAK,CAAC,MAAM;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAE,WAAU;0DAAkB,EAAE,+IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAI9E,6LAAC,mLAAA,CAAA,oBAAiB;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7C;GAzDa;;QACC,yMAAA,CAAA,kBAAe;QACV,qIAAA,CAAA,YAAS;;;KAFf", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useProjectsManager } from '@/components/store/projects';\r\nimport { SubscriptionModal } from '@/components/ui/pricing-modal.tsx';\r\nimport { SettingsModal } from '@/components/ui/settings-modal';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { useEffect } from 'react';\r\nimport { SelectProject } from './_components/select';\r\nimport { TopBar } from './_components/top-bar';\r\n\r\nconst Page = observer(() => {\r\n    const projectsManager = useProjectsManager();\r\n\r\n    useEffect(() => {\r\n        projectsManager.fetchProjects();\r\n    }, []);\r\n\r\n    if (projectsManager.isFetching) {\r\n        return (\r\n            <div className=\"w-screen h-screen flex flex-col items-center justify-center\">\r\n                <div className=\"flex flex-col items-center gap-2\">\r\n                    <Icons.LoadingSpinner className=\"h-6 w-6 animate-spin text-foreground-primary\" />\r\n                    <div className=\"text-lg text-foreground-secondary\">Loading projects...</div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"w-screen h-screen flex flex-col\">\r\n            <TopBar />\r\n            <div className=\"flex justify-center overflow-hidden w-full h-full\">\r\n                <SelectProject />\r\n            </div>\r\n            <SettingsModal showProjectTabs={false} />\r\n            <SubscriptionModal />\r\n        </div>\r\n    );\r\n});\r\n\r\nexport default Page;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,MAAM,OAAO,GAAA,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,UAAE;;IAClB,MAAM,kBAAkB,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACN,gBAAgB,aAAa;QACjC;yBAAG,EAAE;IAEL,IAAI,gBAAgB,UAAU,EAAE;QAC5B,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,yJAAA,CAAA,QAAK,CAAC,cAAc;wBAAC,WAAU;;;;;;kCAChC,6LAAC;wBAAI,WAAU;kCAAoC;;;;;;;;;;;;;;;;;IAInE;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,gLAAA,CAAA,SAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,4KAAA,CAAA,gBAAa;;;;;;;;;;0BAElB,6LAAC,kLAAA,CAAA,gBAAa;gBAAC,iBAAiB;;;;;;0BAChC,6LAAC,wLAAA,CAAA,oBAAiB;;;;;;;;;;;AAG9B;;QA3B4B,2KAAA,CAAA,qBAAkB;;;;QAAlB,2KAAA,CAAA,qBAAkB;;;;uCA6B/B", "debugId": null}}]}