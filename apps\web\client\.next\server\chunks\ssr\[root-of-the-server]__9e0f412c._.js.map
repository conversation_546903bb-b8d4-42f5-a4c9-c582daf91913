{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/env.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\r\nimport { z } from 'zod';\r\n\r\nexport const env = createEnv({\r\n    /**\r\n     * Specify your server-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars.\r\n     */\r\n    server: {\r\n        NODE_ENV: z.enum(['development', 'test', 'production']),\r\n        ANTHROPIC_API_KEY: z.string(),\r\n        CSB_API_KEY: z.string(),\r\n        SUPABASE_DATABASE_URL: z.string().url(),\r\n        RESEND_API_KEY: z.string().optional(),\r\n        MORPH_API_KEY: z.string().optional(),\r\n        RELACE_API_KEY: z.string().optional(),\r\n        FREESTYLE_API_KEY: z.string().optional(),\r\n        STRIPE_WEBHOOK_SECRET: z.string().optional(),\r\n        STRIPE_SECRET_KEY: z.string().optional(),\r\n        AWS_ACCESS_KEY_ID: z.string().optional(),\r\n        AWS_SECRET_ACCESS_KEY: z.string().optional(),\r\n        AWS_REGION: z.string().optional(),\r\n    },\r\n    /**\r\n     * Specify your client-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n     * `NEXT_PUBLIC_`.\r\n     */\r\n    client: {\r\n        NEXT_PUBLIC_SITE_URL: z.string().url().default('http://localhost:3000'),\r\n        NEXT_PUBLIC_SUPABASE_URL: z.string(),\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\r\n        NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),\r\n        NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: z.boolean().default(false),\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: z.string().optional(),\r\n    },\r\n\r\n    /**\r\n     * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n     * middlewares) or client-side so we need to destruct manually.\r\n     */\r\n    runtimeEnv: {\r\n        NODE_ENV: process.env.NODE_ENV,\r\n        ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,\r\n        CSB_API_KEY: process.env.CSB_API_KEY,\r\n        RESEND_API_KEY: process.env.RESEND_API_KEY,\r\n        SUPABASE_DATABASE_URL: process.env.SUPABASE_DATABASE_URL,\r\n        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,\r\n        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\r\n        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\r\n        MORPH_API_KEY: process.env.MORPH_API_KEY,\r\n        RELACE_API_KEY: process.env.RELACE_API_KEY,\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: process.env.NEXT_PUBLIC_FEATURE_COLLABORATION,\r\n        FREESTYLE_API_KEY: process.env.FREESTYLE_API_KEY,\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: process.env.NEXT_PUBLIC_HOSTING_DOMAIN,\r\n        STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,\r\n        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,\r\n        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,\r\n        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,\r\n        AWS_REGION: process.env.AWS_REGION,\r\n    },\r\n    /**\r\n     * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n     * useful for Docker builds.\r\n     */\r\n    skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n    /**\r\n     * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n     * `SOME_VAR=''` will throw an error.\r\n     */\r\n    emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;IACzB;;;KAGC,GACD,QAAQ;QACJ,UAAU,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM;QAC3B,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;QACrB,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACrC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC;IACA;;;;KAIC,GACD,QAAQ;QACJ,sBAAsB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;QAC/C,0BAA0B,iLAAA,CAAA,IAAC,CAAC,MAAM;QAClC,+BAA+B,iLAAA,CAAA,IAAC,CAAC,MAAM;QACvC,yBAAyB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,0BAA0B,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7C,mCAAmC,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACvD,4BAA4B,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnD;IAEA;;;KAGC,GACD,YAAY;QACR,QAAQ;QACR,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,wBAAwB;QACxB,6BAA6B;QAC7B,uBAAuB;QACvB,wBAAwB;QACxB,eAAe,QAAQ,GAAG,CAAC,aAAa;QACxC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,mCAAmC,QAAQ,GAAG,CAAC,iCAAiC;QAChF,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;QAClE,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,YAAY,QAAQ,GAAG,CAAC,UAAU;IACtC;IACA;;;KAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;KAGC,GACD,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/supabase/client/index.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { createBrowserClient } from '@supabase/ssr';\r\n\r\nexport function createClient() {\r\n    // Create a supabase client on the browser with project's credentials\r\n    return createBrowserClient(\r\n        env.NEXT_PUBLIC_SUPABASE_URL,\r\n        env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n    );\r\n}\r\n\r\nexport const getFileUrlFromStorage = (bucket: string, path: string) => {\r\n    const supabase = createClient();\r\n    const { data } = supabase.storage\r\n        .from(bucket)\r\n        .getPublicUrl(path);\r\n\r\n    return data.publicUrl;\r\n};\r\n\r\nexport const uploadBlobToStorage = async (bucket: string, path: string, file: Blob, options: {\r\n    upsert?: boolean;\r\n    contentType?: string;\r\n    cacheControl?: string;\r\n}) => {\r\n    const supabase = createClient();\r\n    const { data, error } = await supabase.storage\r\n        .from(bucket)\r\n        .upload(path, file, options);\r\n\r\n    if (error) {\r\n        console.error('Error uploading file:', error);\r\n        return null;\r\n    }\r\n\r\n    return data;\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;;;AAEO,SAAS;IACZ,qEAAqE;IACrE,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EACrB,mIAAA,CAAA,MAAG,CAAC,wBAAwB,EAC5B,mIAAA,CAAA,MAAG,CAAC,6BAA6B;AAEzC;AAEO,MAAM,wBAAwB,CAAC,QAAgB;IAClD,MAAM,WAAW;IACjB,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,OAAO,CAC5B,IAAI,CAAC,QACL,YAAY,CAAC;IAElB,OAAO,KAAK,SAAS;AACzB;AAEO,MAAM,sBAAsB,OAAO,QAAgB,MAAc,MAAY;IAKhF,MAAM,WAAW;IACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CACzC,IAAI,CAAC,QACL,MAAM,CAAC,MAAM,MAAM;IAExB,IAAI,OAAO;QACP,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACX;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/user/language.ts"], "sourcesContent": ["import { Language } from '@onlook/constants';\r\nimport localforage from 'localforage';\r\n\r\nexport class LanguageManager {\r\n    constructor() {}\r\n\r\n    update(language: Language) {\r\n        localforage.setItem('app-language', language);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACT,aAAc,CAAC;IAEf,OAAO,QAAkB,EAAE;QACvB,kJAAA,CAAA,UAAW,CAAC,OAAO,CAAC,gBAAgB;IACxC;AACJ", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/trpc/helpers.ts"], "sourcesContent": ["import { httpBatchStreamLink, loggerLink } from '@trpc/client';\r\nimport <PERSON>J<PERSON><PERSON> from 'superjson';\r\n\r\nexport function getBaseUrl() {\r\n    if (typeof window !== 'undefined') return window.location.origin;\r\n    if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\r\n    return `http://localhost:${process.env.PORT ?? 3000}`;\r\n}\r\n\r\nexport const links = [\r\n    loggerLink({\r\n        enabled: (op) =>\r\n            process.env.NODE_ENV === 'development' ||\r\n            (op.direction === 'down' && op.result instanceof Error),\r\n    }),\r\n    httpBatchStreamLink({\r\n        transformer: SuperJSON,\r\n        url: getBaseUrl() + '/api/trpc',\r\n        headers: () => {\r\n            const headers = new Headers();\r\n            headers.set('x-trpc-source', 'vanilla-client');\r\n            return headers;\r\n        },\r\n    }),\r\n];\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;;;AAEO,SAAS;IACZ,uCAAmC;;IAA6B;IAChE,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;IACtE,OAAO,CAAC,iBAAiB,EAAE,QAAQ,GAAG,CAAC,IAAI,IAAI,MAAM;AACzD;AAEO,MAAM,QAAQ;IACjB,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE;QACP,SAAS,CAAC,KACN,oDAAyB,iBACxB,GAAG,SAAS,KAAK,UAAU,GAAG,MAAM,YAAY;IACzD;IACA,CAAA,GAAA,kKAAA,CAAA,sBAAmB,AAAD,EAAE;QAChB,aAAa,0IAAA,CAAA,UAAS;QACtB,KAAK,eAAe;QACpB,SAAS;YACL,MAAM,UAAU,IAAI;YACpB,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,OAAO;QACX;IACJ;CACH", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/trpc/client.ts"], "sourcesContent": ["import { createTRPCClient } from '@trpc/client';\r\nimport { type inferRouterInputs, type inferRouterOutputs } from '@trpc/server';\r\nimport { type AppRouter } from '~/server/api/root';\r\nimport { links } from './helpers';\r\n\r\n/**\r\n * Inference helper for inputs.\r\n *\r\n * @example type HelloInput = RouterInputs['example']['hello']\r\n */\r\nexport type RouterInputs = inferRouterInputs<AppRouter>;\r\n\r\n/**\r\n * Inference helper for outputs.\r\n *\r\n * @example type HelloOutput = RouterOutputs['example']['hello']\r\n */\r\nexport type RouterOutputs = inferRouterOutputs<AppRouter>;\r\n\r\nexport const api = createTRPCClient<AppRouter>({\r\n    links,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAGA;;;AAgBO,MAAM,MAAM,CAAA,GAAA,kKAAA,CAAA,mBAAgB,AAAD,EAAa;IAC3C,OAAA,+IAAA,CAAA,QAAK;AACT", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/canvas.ts"], "sourcesContent": ["import type { Canvas as DbCanvas } from '@onlook/db';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nexport const createDefaultCanvas = (projectId: string): DbCanvas => {\r\n    return {\r\n        id: uuidv4(),\r\n        projectId: projectId,\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM,sBAAsB,CAAC;IAChC,OAAO;QACH,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QACT,WAAW;IACf;AACJ", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/conversation.ts"], "sourcesContent": ["import type { Conversation as DbConversation } from '@onlook/db';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nexport const createDefaultConversation = (projectId: string): DbConversation => {\r\n    return {\r\n        id: uuidv4(),\r\n        projectId,\r\n        createdAt: new Date(),\r\n        updatedAt: new Date(),\r\n        displayName: 'New Conversation',\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM,4BAA4B,CAAC;IACtC,OAAO;QACH,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QACT;QACA,WAAW,IAAI;QACf,WAAW,IAAI;QACf,aAAa;IACjB;AACJ", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/colors.ts"], "sourcesContent": ["export const TAILWIND_WEB_COLORS = {\n    inherit: 'inherit',\n    current: 'currentColor',\n    transparent: 'transparent',\n    black: '#000',\n    white: '#fff',\n    slate: {\n        '50': '#f8fafc',\n        '100': '#f1f5f9',\n        '200': '#e2e8f0',\n        '300': '#cbd5e1',\n        '400': '#94a3b8',\n        '500': '#64748b',\n        '600': '#475569',\n        '700': '#334155',\n        '800': '#1e293b',\n        '900': '#0f172a',\n        '950': '#020617',\n    },\n    gray: {\n        '50': '#f9fafb',\n        '100': '#f3f4f6',\n        '200': '#e5e7eb',\n        '300': '#d1d5db',\n        '400': '#9ca3af',\n        '500': '#6b7280',\n        '600': '#4b5563',\n        '700': '#374151',\n        '800': '#1f2937',\n        '900': '#111827',\n        '950': '#030712',\n    },\n    zinc: {\n        '50': '#fafafa',\n        '100': '#f4f4f5',\n        '200': '#e4e4e7',\n        '300': '#d4d4d8',\n        '400': '#a1a1aa',\n        '500': '#71717a',\n        '600': '#52525b',\n        '700': '#3f3f46',\n        '800': '#27272a',\n        '900': '#18181b',\n        '950': '#09090b',\n    },\n    neutral: {\n        '50': '#fafafa',\n        '100': '#f5f5f5',\n        '200': '#e5e5e5',\n        '300': '#d4d4d4',\n        '400': '#a3a3a3',\n        '500': '#737373',\n        '600': '#525252',\n        '700': '#404040',\n        '800': '#262626',\n        '900': '#171717',\n        '950': '#0a0a0a',\n    },\n    stone: {\n        '50': '#fafaf9',\n        '100': '#f5f5f4',\n        '200': '#e7e5e4',\n        '300': '#d6d3d1',\n        '400': '#a8a29e',\n        '500': '#78716c',\n        '600': '#57534e',\n        '700': '#44403c',\n        '800': '#292524',\n        '900': '#1c1917',\n        '950': '#0c0a09',\n    },\n    red: {\n        '50': '#fef2f2',\n        '100': '#fee2e2',\n        '200': '#fecaca',\n        '300': '#fca5a5',\n        '400': '#f87171',\n        '500': '#ef4444',\n        '600': '#dc2626',\n        '700': '#b91c1c',\n        '800': '#991b1b',\n        '900': '#7f1d1d',\n        '950': '#450a0a',\n    },\n    orange: {\n        '50': '#fff7ed',\n        '100': '#ffedd5',\n        '200': '#fed7aa',\n        '300': '#fdba74',\n        '400': '#fb923c',\n        '500': '#f97316',\n        '600': '#ea580c',\n        '700': '#c2410c',\n        '800': '#9a3412',\n        '900': '#7c2d12',\n        '950': '#431407',\n    },\n    amber: {\n        '50': '#fffbeb',\n        '100': '#fef3c7',\n        '200': '#fde68a',\n        '300': '#fcd34d',\n        '400': '#fbbf24',\n        '500': '#f59e0b',\n        '600': '#d97706',\n        '700': '#b45309',\n        '800': '#92400e',\n        '900': '#78350f',\n        '950': '#451a03',\n    },\n    yellow: {\n        '50': '#fefce8',\n        '100': '#fef9c3',\n        '200': '#fef08a',\n        '300': '#fde047',\n        '400': '#facc15',\n        '500': '#eab308',\n        '600': '#ca8a04',\n        '700': '#a16207',\n        '800': '#854d0e',\n        '900': '#713f12',\n        '950': '#422006',\n    },\n    lime: {\n        '50': '#f7fee7',\n        '100': '#ecfccb',\n        '200': '#d9f99d',\n        '300': '#bef264',\n        '400': '#a3e635',\n        '500': '#84cc16',\n        '600': '#65a30d',\n        '700': '#4d7c0f',\n        '800': '#3f6212',\n        '900': '#365314',\n        '950': '#1a2e05',\n    },\n    green: {\n        '50': '#f0fdf4',\n        '100': '#dcfce7',\n        '200': '#bbf7d0',\n        '300': '#86efac',\n        '400': '#4ade80',\n        '500': '#22c55e',\n        '600': '#16a34a',\n        '700': '#15803d',\n        '800': '#166534',\n        '900': '#14532d',\n        '950': '#052e16',\n    },\n    emerald: {\n        '50': '#ecfdf5',\n        '100': '#d1fae5',\n        '200': '#a7f3d0',\n        '300': '#6ee7b7',\n        '400': '#34d399',\n        '500': '#10b981',\n        '600': '#059669',\n        '700': '#047857',\n        '800': '#065f46',\n        '900': '#064e3b',\n        '950': '#022c22',\n    },\n    teal: {\n        '50': '#f0fdfa',\n        '100': '#ccfbf1',\n        '200': '#99f6e4',\n        '300': '#5eead4',\n        '400': '#2dd4bf',\n        '500': '#14b8a6',\n        '600': '#0d9488',\n        '700': '#0f766e',\n        '800': '#115e59',\n        '900': '#134e4a',\n        '950': '#042f2e',\n    },\n    cyan: {\n        '50': '#ecfeff',\n        '100': '#cffafe',\n        '200': '#a5f3fc',\n        '300': '#67e8f9',\n        '400': '#22d3ee',\n        '500': '#06b6d4',\n        '600': '#0891b2',\n        '700': '#0e7490',\n        '800': '#155e75',\n        '900': '#164e63',\n        '950': '#083344',\n    },\n    sky: {\n        '50': '#f0f9ff',\n        '100': '#e0f2fe',\n        '200': '#bae6fd',\n        '300': '#7dd3fc',\n        '400': '#38bdf8',\n        '500': '#0ea5e9',\n        '600': '#0284c7',\n        '700': '#0369a1',\n        '800': '#075985',\n        '900': '#0c4a6e',\n        '950': '#082f49',\n    },\n    blue: {\n        '50': '#eff6ff',\n        '100': '#dbeafe',\n        '200': '#bfdbfe',\n        '300': '#93c5fd',\n        '400': '#60a5fa',\n        '500': '#3b82f6',\n        '600': '#2563eb',\n        '700': '#1d4ed8',\n        '800': '#1e40af',\n        '900': '#1e3a8a',\n        '950': '#172554',\n    },\n    indigo: {\n        '50': '#eef2ff',\n        '100': '#e0e7ff',\n        '200': '#c7d2fe',\n        '300': '#a5b4fc',\n        '400': '#818cf8',\n        '500': '#6366f1',\n        '600': '#4f46e5',\n        '700': '#4338ca',\n        '800': '#3730a3',\n        '900': '#312e81',\n        '950': '#1e1b4b',\n    },\n    violet: {\n        '50': '#f5f3ff',\n        '100': '#ede9fe',\n        '200': '#ddd6fe',\n        '300': '#c4b5fd',\n        '400': '#a78bfa',\n        '500': '#8b5cf6',\n        '600': '#7c3aed',\n        '700': '#6d28d9',\n        '800': '#5b21b6',\n        '900': '#4c1d95',\n        '950': '#2e1065',\n    },\n    purple: {\n        '50': '#faf5ff',\n        '100': '#f3e8ff',\n        '200': '#e9d5ff',\n        '300': '#d8b4fe',\n        '400': '#c084fc',\n        '500': '#a855f7',\n        '600': '#9333ea',\n        '700': '#7e22ce',\n        '800': '#6b21a8',\n        '900': '#581c87',\n        '950': '#3b0764',\n    },\n    fuchsia: {\n        '50': '#fdf4ff',\n        '100': '#fae8ff',\n        '200': '#f5d0fe',\n        '300': '#f0abfc',\n        '400': '#e879f9',\n        '500': '#d946ef',\n        '600': '#c026d3',\n        '700': '#a21caf',\n        '800': '#86198f',\n        '900': '#701a75',\n        '950': '#4a044e',\n    },\n    pink: {\n        '50': '#fdf2f8',\n        '100': '#fce7f3',\n        '200': '#fbcfe8',\n        '300': '#f9a8d4',\n        '400': '#f472b6',\n        '500': '#ec4899',\n        '600': '#db2777',\n        '700': '#be185d',\n        '800': '#9d174d',\n        '900': '#831843',\n        '950': '#500724',\n    },\n    rose: {\n        '50': '#fff1f2',\n        '100': '#ffe4e6',\n        '200': '#fecdd3',\n        '300': '#fda4af',\n        '400': '#fb7185',\n        '500': '#f43f5e',\n        '600': '#e11d48',\n        '700': '#be123c',\n        '800': '#9f1239',\n        '900': '#881337',\n        '950': '#4c0519',\n    },\n    lightBlue: {\n        '50': '#f0f9ff',\n        '100': '#e0f2fe',\n        '200': '#bae6fd',\n        '300': '#7dd3fc',\n        '400': '#38bdf8',\n        '500': '#0ea5e9',\n        '600': '#0284c7',\n        '700': '#0369a1',\n        '800': '#075985',\n        '900': '#0c4a6e',\n        '950': '#082f49',\n    },\n    warmGray: {\n        '50': '#fafaf9',\n        '100': '#f5f5f4',\n        '200': '#e7e5e4',\n        '300': '#d6d3d1',\n        '400': '#a8a29e',\n        '500': '#78716c',\n        '600': '#57534e',\n        '700': '#44403c',\n        '800': '#292524',\n        '900': '#1c1917',\n        '950': '#0c0a09',\n    },\n    trueGray: {\n        '50': '#fafafa',\n        '100': '#f5f5f5',\n        '200': '#e5e5e5',\n        '300': '#d4d4d4',\n        '400': '#a3a3a3',\n        '500': '#737373',\n        '600': '#525252',\n        '700': '#404040',\n        '800': '#262626',\n        '900': '#171717',\n        '950': '#0a0a0a',\n    },\n    coolGray: {\n        '50': '#f9fafb',\n        '100': '#f3f4f6',\n        '200': '#e5e7eb',\n        '300': '#d1d5db',\n        '400': '#9ca3af',\n        '500': '#6b7280',\n        '600': '#4b5563',\n        '700': '#374151',\n        '800': '#1f2937',\n        '900': '#111827',\n        '950': '#030712',\n    },\n    blueGray: {\n        '50': '#f8fafc',\n        '100': '#f1f5f9',\n        '200': '#e2e8f0',\n        '300': '#cbd5e1',\n        '400': '#94a3b8',\n        '500': '#64748b',\n        '600': '#475569',\n        '700': '#334155',\n        '800': '#1e293b',\n        '900': '#0f172a',\n        '950': '#020617',\n    },\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,sBAAsB;IAC/B,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,OAAO;IACP,OAAO;QACH,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,OAAO;QACH,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,KAAK;QACD,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,QAAQ;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,OAAO;QACH,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,QAAQ;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,OAAO;QACH,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,KAAK;QACD,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,QAAQ;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,QAAQ;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,QAAQ;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,WAAW;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,UAAU;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,UAAU;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,UAAU;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,UAAU;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/csb.ts"], "sourcesContent": ["import type { SandboxTemplate } from '@onlook/models';\n\nexport enum Templates {\n    BLANK = 'BLANK',\n    EMPTY_NEXTJS = 'EMPTY_NEXTJS',\n}\n\nexport const SandboxTemplates: Record<Templates, SandboxTemplate> = {\n    BLANK: {\n        id: 'xzsy8c',\n        port: 3000,\n    },\n    EMPTY_NEXTJS: {\n        id: 'hj3hgt',\n        port: 3000,\n    },\n};\n\nexport const CSB_PREVIEW_TASK_NAME = 'dev';\nexport const CSB_DOMAIN = 'csb.app';\n\nexport function getSandboxPreviewUrl(sandboxId: string, port: number) {\n    return `https://${sandboxId}-${port}.${CSB_DOMAIN}`;\n}\n"], "names": [], "mappings": ";;;;;;;AAEO,IAAA,AAAK,mCAAA;;;WAAA;;AAKL,MAAM,mBAAuD;IAChE,OAAO;QACH,IAAI;QACJ,MAAM;IACV;IACA,cAAc;QACV,IAAI;QACJ,MAAM;IACV;AACJ;AAEO,MAAM,wBAAwB;AAC9B,MAAM,aAAa;AAEnB,SAAS,qBAAqB,SAAiB,EAAE,IAAY;IAChE,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACvD", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/dom.ts"], "sourcesContent": ["export const DOM_IGNORE_TAGS = ['SCRIPT', 'STYLE', 'LINK', 'META', 'NOSCRIPT'];\nexport const INLINE_ONLY_CONTAINERS = new Set([\n    'a',\n    'abbr',\n    'area',\n    'audio',\n    'b',\n    'bdi',\n    'bdo',\n    'br',\n    'button',\n    'canvas',\n    'cite',\n    'code',\n    'data',\n    'datalist',\n    'del',\n    'dfn',\n    'em',\n    'embed',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'i',\n    'iframe',\n    'img',\n    'input',\n    'ins',\n    'kbd',\n    'label',\n    'li',\n    'map',\n    'mark',\n    'meter',\n    'noscript',\n    'object',\n    'output',\n    'p',\n    'picture',\n    'progress',\n    'q',\n    'ruby',\n    's',\n    'samp',\n    'script',\n    'select',\n    'slot',\n    'small',\n    'span',\n    'strong',\n    'sub',\n    'sup',\n    'svg',\n    'template',\n    'textarea',\n    'time',\n    'u',\n    'var',\n    'video',\n    'wbr',\n]);\n"], "names": [], "mappings": ";;;;AAAO,MAAM,kBAAkB;IAAC;IAAU;IAAS;IAAQ;IAAQ;CAAW;AACvE,MAAM,yBAAyB,IAAI,IAAI;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/frame.ts"], "sourcesContent": ["export enum Orientation {\n    Portrait = 'Portrait',\n    Landscape = 'Landscape',\n}\n\nexport enum Theme {\n    Light = 'light',\n    Dark = 'dark',\n    System = 'system',\n}\n\ntype DeviceOptions = Record<string, Record<string, string>>;\n\nexport const DEVICE_OPTIONS: DeviceOptions = {\n    Custom: {\n        Custom: 'Custom',\n    },\n    Phone: {\n        'Android Compact': '412x917',\n        'Android Medium': '700x840',\n        'Android Small': '360x640',\n        'Android Large': '360x800',\n        'iPhone 16': '393x852',\n        'iPhone 16 Pro': '402x874',\n        'iPhone 16 Pro Max': '440x956',\n        'iPhone 16 Plus': '430x932',\n        'iPhone 14 & 15 Pro': '430x932',\n        'iPhone 14 & 15': '393x852',\n        'iPhone 13 & 14': '390x844',\n        'iPhone 13 Pro Max': '428x926',\n        'iPhone 13 / 13 Pro': '390x844',\n        'iPhone 11 Pro Max': '414x896',\n        'iPhone 11 Pro / X': '375x812',\n        'iPhone 8 Plus': '414x736',\n        'iPhone 8': '375x667',\n        'iPhone SE': '320x568',\n    },\n    Tablet: {\n        'Android Expanded': '1280x800',\n        'Surface Pro 8': '1440x960',\n        'Surface Pro 4': '1368x912',\n        'iPad Mini 8.3': '744x1133',\n        'iPad Mini 5': '768x1024',\n        'iPad Pro 11': '834x1194',\n        'iPad Pro 12.9': '1024x1366',\n    },\n    Laptop: {\n        'MacBook Air': '1280x832',\n        MacBook: '1152x700',\n        'MacBook Pro 14': '1512x982',\n        'MacBook Pro 16': '1728x1117',\n        'MacBook Pro': '1440x900',\n        'Surface Book': '1500x1000',\n    },\n    Desktop: {\n        Desktop: '1440x1024',\n        Wireframe: '1440x1024',\n        TV: '1280x720',\n        iMac: '1280x720',\n    },\n};\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,IAAA,AAAK,+BAAA;;;;WAAA;;AAQL,MAAM,iBAAgC;IACzC,QAAQ;QACJ,QAAQ;IACZ;IACA,OAAO;QACH,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,kBAAkB;QAClB,kBAAkB;QAClB,qBAAqB;QACrB,sBAAsB;QACtB,qBAAqB;QACrB,qBAAqB;QACrB,iBAAiB;QACjB,YAAY;QACZ,aAAa;IACjB;IACA,QAAQ;QACJ,oBAAoB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,iBAAiB;IACrB;IACA,QAAQ;QACJ,eAAe;QACf,SAAS;QACT,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,gBAAgB;IACpB;IACA,SAAS;QACL,SAAS;QACT,WAAW;QACX,IAAI;QACJ,MAAM;IACV;AACJ", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/editor.ts"], "sourcesContent": ["import { Orientation, Theme } from './frame';\nexport const APP_NAME = 'Onlook';\nexport const APP_SCHEMA = 'onlook';\nexport const HOSTING_DOMAIN = 'onlook.live';\nexport const CUSTOM_OUTPUT_DIR = '.next-prod';\nexport const MAX_NAME_LENGTH = 50;\n\nexport enum EditorAttributes {\n    // DOM attributes\n    ONLOOK_TOOLBAR = 'onlook-toolbar',\n    ONLOOK_RECT_ID = 'onlook-rect',\n    ONLOOK_STYLESHEET_ID = 'onlook-stylesheet',\n    ONLOOK_STUB_ID = 'onlook-drag-stub',\n    ONLOOK_MOVE_KEY_PREFIX = 'olk-',\n    OVERLAY_CONTAINER_ID = 'overlay-container',\n    CANVAS_CONTAINER_ID = 'canvas-container',\n    STYLESHEET_ID = 'onlook-default-stylesheet',\n\n    // IDs\n    DATA_ONLOOK_ID = 'data-oid',\n    DATA_ONLOOK_INSTANCE_ID = 'data-oiid',\n    DATA_ONLOOK_DOM_ID = 'data-odid',\n    DATA_ONLOOK_COMPONENT_NAME = 'data-ocname',\n\n    // Data attributes\n    DATA_ONLOOK_IGNORE = 'data-onlook-ignore',\n    DATA_ONLOOK_INSERTED = 'data-onlook-inserted',\n    DATA_ONLOOK_DRAG_SAVED_STYLE = 'data-onlook-drag-saved-style',\n    DATA_ONLOOK_DRAGGING = 'data-onlook-dragging',\n    DATA_ONLOOK_DRAG_DIRECTION = 'data-onlook-drag-direction',\n    DATA_ONLOOK_DRAG_START_POSITION = 'data-onlook-drag-start-position',\n    DATA_ONLOOK_NEW_INDEX = 'data-onlook-new-index',\n    DATA_ONLOOK_EDITING_TEXT = 'data-onlook-editing-text',\n    DATA_ONLOOK_DYNAMIC_TYPE = 'data-onlook-dynamic-type',\n    DATA_ONLOOK_CORE_ELEMENT_TYPE = 'data-onlook-core-element-type',\n}\n\nexport const DefaultSettings = {\n    SCALE: 0.7,\n    PAN_POSITION: { x: 175, y: 100 },\n    URL: 'http://localhost:3000/',\n    FRAME_POSITION: { x: 0, y: 0 },\n    FRAME_DIMENSION: { width: 1536, height: 960 },\n    ASPECT_RATIO_LOCKED: false,\n    DEVICE: 'Custom:Custom',\n    THEME: Theme.System,\n    ORIENTATION: Orientation.Portrait,\n    MIN_DIMENSIONS: { width: '280px', height: '360px' },\n    COMMANDS: {\n        run: 'bun run dev',\n        build: 'bun run build',\n        install: 'bun install',\n    },\n    IMAGE_FOLDER: 'public/images',\n    IMAGE_DIMENSION: { width: '100px', height: '100px' },\n    FONT_FOLDER: 'public/fonts',\n    FONT_CONFIG: 'app/fonts.ts',\n    TAILWIND_CONFIG: 'tailwind.config.ts',\n    CHAT_SETTINGS: {\n        showSuggestions: true,\n        autoApplyCode: true,\n        expandCodeBlocks: true,\n        showMiniChat: true,\n    },\n    EDITOR_SETTINGS: {\n        shouldWarnDelete: false,\n        enableBunReplace: true,\n        buildFlags: '--no-lint',\n    },\n};\n\nexport const DEFAULT_COLOR_NAME = 'DEFAULT';\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM,kBAAkB;AAExB,IAAA,AAAK,0CAAA;IACR,iBAAiB;;;;;;;;;IAUjB,MAAM;;;;;IAMN,kBAAkB;;;;;;;;;;;WAjBV;;AA8BL,MAAM,kBAAkB;IAC3B,OAAO;IACP,cAAc;QAAE,GAAG;QAAK,GAAG;IAAI;IAC/B,KAAK;IACL,gBAAgB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC7B,iBAAiB;QAAE,OAAO;QAAM,QAAQ;IAAI;IAC5C,qBAAqB;IACrB,QAAQ;IACR,OAAO,qIAAA,CAAA,QAAK,CAAC,MAAM;IACnB,aAAa,qIAAA,CAAA,cAAW,CAAC,QAAQ;IACjC,gBAAgB;QAAE,OAAO;QAAS,QAAQ;IAAQ;IAClD,UAAU;QACN,KAAK;QACL,OAAO;QACP,SAAS;IACb;IACA,cAAc;IACd,iBAAiB;QAAE,OAAO;QAAS,QAAQ;IAAQ;IACnD,aAAa;IACb,aAAa;IACb,iBAAiB;IACjB,eAAe;QACX,iBAAiB;QACjB,eAAe;QACf,kBAAkB;QAClB,cAAc;IAClB;IACA,iBAAiB;QACb,kBAAkB;QAClB,kBAAkB;QAClB,YAAY;IAChB;AACJ;AAEO,MAAM,qBAAqB", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/files.ts"], "sourcesContent": ["import { CUSTOM_OUTPUT_DIR } from './editor';\r\n\r\nconst BASE_EXCLUDED_DIRECTORIES = ['node_modules', 'dist', 'build', '.git', '.next'] as const;\r\n\r\nexport const EXCLUDED_SYNC_DIRECTORIES = [\r\n    ...BASE_EXCLUDED_DIRECTORIES,\r\n    'static',\r\n    CUSTOM_OUTPUT_DIR,\r\n];\r\n\r\nexport const IGNORED_UPLOAD_DIRECTORIES = [...BASE_EXCLUDED_DIRECTORIES, CUSTOM_OUTPUT_DIR];\r\n\r\nexport const EXCLUDED_PUBLISH_DIRECTORIES = [...BASE_EXCLUDED_DIRECTORIES, 'coverage'];\r\n\r\nexport const JSX_FILE_EXTENSIONS = ['.jsx', '.tsx'];\r\n\r\nexport const JS_FILE_EXTENSIONS = ['.js', '.ts', '.mjs', '.cjs'];\r\n\r\nexport const SUPPORTED_LOCK_FILES = [\r\n    'bun.lock',\r\n    'package-lock.json',\r\n    'yarn.lock',\r\n    'pnpm-lock.yaml',\r\n];\r\n\r\nexport const BINARY_EXTENSIONS = [\r\n    '.jpg',\r\n    '.jpeg',\r\n    '.png',\r\n    '.gif',\r\n    '.bmp',\r\n    '.svg',\r\n    '.ico',\r\n    '.webp',\r\n    '.pdf',\r\n    '.zip',\r\n    '.tar',\r\n    '.gz',\r\n    '.rar',\r\n    '.7z',\r\n    '.mp3',\r\n    '.mp4',\r\n    '.wav',\r\n    '.avi',\r\n    '.mov',\r\n    '.wmv',\r\n    '.exe',\r\n    '.bin',\r\n    '.dll',\r\n    '.so',\r\n    '.dylib',\r\n    '.woff',\r\n    '.woff2',\r\n    '.ttf',\r\n    '.eot',\r\n    '.otf',\r\n];\r\n\r\nexport const IGNORED_UPLOAD_FILES = [\r\n    '.DS_Store',\r\n    'Thumbs.db',\r\n    'yarn.lock',\r\n    'package-lock.json',\r\n    'pnpm-lock.yaml',\r\n    'bun.lockb',\r\n    '.env.local',\r\n    '.env.development.local',\r\n    '.env.production.local',\r\n    '.env.test.local',\r\n];\r\n\r\nexport const IMAGE_EXTENSIONS = [\r\n    'image/jpeg',\r\n    'image/png',\r\n    'image/gif',\r\n    'image/webp',\r\n    'image/svg+xml',\r\n    'image/bmp',\r\n    'image/ico',\r\n    'image/avif',\r\n];\r\n\r\n/**\r\n * Compression presets for common use cases\r\n */\r\nexport const COMPRESSION_IMAGE_PRESETS = {\r\n    web: {\r\n        quality: 80,\r\n        format: 'webp' as const,\r\n        progressive: true,\r\n        effort: 4,\r\n    },\r\n    thumbnail: {\r\n        quality: 70,\r\n        width: 300,\r\n        height: 300,\r\n        format: 'webp' as const,\r\n        keepAspectRatio: true,\r\n    },\r\n    highQuality: {\r\n        quality: 95,\r\n        format: 'jpeg' as const,\r\n        progressive: true,\r\n        mozjpeg: true,\r\n    },\r\n    lowFileSize: {\r\n        quality: 60,\r\n        format: 'webp' as const,\r\n        effort: 6,\r\n    },\r\n} as const;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAEA,MAAM,4BAA4B;IAAC;IAAgB;IAAQ;IAAS;IAAQ;CAAQ;AAE7E,MAAM,4BAA4B;OAClC;IACH;IACA,sIAAA,CAAA,oBAAiB;CACpB;AAEM,MAAM,6BAA6B;OAAI;IAA2B,sIAAA,CAAA,oBAAiB;CAAC;AAEpF,MAAM,+BAA+B;OAAI;IAA2B;CAAW;AAE/E,MAAM,sBAAsB;IAAC;IAAQ;CAAO;AAE5C,MAAM,qBAAqB;IAAC;IAAO;IAAO;IAAQ;CAAO;AAEzD,MAAM,uBAAuB;IAChC;IACA;IACA;IACA;CACH;AAEM,MAAM,oBAAoB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAEM,MAAM,uBAAuB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAEM,MAAM,mBAAmB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAKM,MAAM,4BAA4B;IACrC,KAAK;QACD,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;IACZ;IACA,WAAW;QACP,SAAS;QACT,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,iBAAiB;IACrB;IACA,aAAa;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,SAAS;IACb;IACA,aAAa;QACT,SAAS;QACT,QAAQ;QACR,QAAQ;IACZ;AACJ", "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/freestyle.ts"], "sourcesContent": ["export const FRESTYLE_CUSTOM_HOSTNAME = '_freestyle_custom_hostname';\nexport const FREESTYLE_IP_ADDRESS = '*************';\n"], "names": [], "mappings": ";;;;AAAO,MAAM,2BAA2B;AACjC,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/language.ts"], "sourcesContent": ["export enum Language {\n    English = 'en',\n    Japanese = 'ja',\n    Chinese = 'zh',\n    Korean = 'ko',\n}\n\nexport const LANGUAGE_DISPLAY_NAMES: Record<Language, string> = {\n    [Language.English]: 'English',\n    [Language.Japanese]: '日本語',\n    [Language.Chinese]: '中文',\n    [Language.Korean]: '한국어',\n} as const;\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,kCAAA;;;;;WAAA;;AAOL,MAAM,yBAAmD;IAC5D,MAAkB,EAAE;IACpB,MAAmB,EAAE;IACrB,MAAkB,EAAE;IACpB,MAAiB,EAAE;AACvB", "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/links.ts"], "sourcesContent": ["export enum Links {\n    DISCORD = 'https://discord.gg/hERDfFZCsH',\n    GITHUB = 'https://github.com/onlook-dev/onlook',\n    USAGE_DOCS = 'https://github.com/onlook-dev/onlook/wiki/How-to-set-up-my-project%3F',\n    WIKI = 'https://github.com/onlook-dev/onlook/wiki',\n    OPEN_ISSUE = 'https://github.com/onlook-dev/onlook/issues/new/choose',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,+BAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/storage.ts"], "sourcesContent": ["export const STORAGE_BUCKETS = {\n    PREVIEW_IMAGES: 'preview_images',\n} as const;\n"], "names": [], "mappings": ";;;AAAO,MAAM,kBAAkB;IAC3B,gBAAgB;AACpB", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/index.ts"], "sourcesContent": ["export * from './colors';\nexport * from './csb';\nexport * from './dom';\nexport * from './editor';\nexport * from './files';\nexport * from './frame';\nexport * from './freestyle';\nexport * from './language';\nexport * from './links';\nexport * from './storage';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/actions/code.ts"], "sourcesContent": ["import {\n    type GroupContainer,\n    type InsertImageAction,\n    type PasteParams,\n    type RemoveImageAction,\n} from './action';\nimport { type ActionLocation, type IndexActionLocation } from './location';\nimport { type ActionTarget } from './target';\n\nexport enum CodeActionType {\n    MOVE = 'move',\n    INSERT = 'insert',\n    REMOVE = 'remove',\n    GROUP = 'group',\n    UNGROUP = 'ungroup',\n    INSERT_IMAGE = 'insert-image',\n    REMOVE_IMAGE = 'remove-image',\n}\n\nexport interface BaseCodeAction {\n    type: CodeActionType;\n    location: ActionLocation;\n    oid: string;\n}\n\nexport interface BaseCodeInsert extends BaseCodeAction {\n    type: CodeActionType.INSERT;\n    tagName: string;\n    attributes: Record<string, string>;\n    textContent: string | null;\n    pasteParams: PasteParams | null;\n    codeBlock: string | null;\n}\n\nexport interface CodeInsert extends BaseCodeInsert {\n    children: CodeInsert[];\n}\n\nexport interface CodeRemove {\n    type: CodeActionType.REMOVE;\n    oid: string;\n    codeBlock: string | null;\n}\n\nexport interface CodeStyle {\n    oid: string;\n    styles: Record<string, string>;\n}\n\nexport interface CodeEditText {\n    oid: string;\n    content: string;\n}\n\nexport interface CodeMove extends BaseCodeAction {\n    type: CodeActionType.MOVE;\n    location: IndexActionLocation;\n}\n\nexport interface BaseCodeGroup {\n    oid: string;\n    container: GroupContainer;\n    children: ActionTarget[];\n}\n\nexport interface CodeGroup extends BaseCodeGroup {\n    type: CodeActionType.GROUP;\n}\n\nexport interface CodeUngroup extends BaseCodeGroup {\n    type: CodeActionType.UNGROUP;\n}\n\nexport interface CodeInsertImage extends InsertImageAction {\n    type: CodeActionType.INSERT_IMAGE;\n    folderPath: string;\n}\n\nexport interface CodeRemoveImage extends RemoveImageAction {\n    type: CodeActionType.REMOVE_IMAGE;\n}\n\nexport type CodeAction =\n    | CodeMove\n    | CodeInsert\n    | CodeRemove\n    | CodeGroup\n    | CodeUngroup\n    | CodeInsertImage\n    | CodeRemoveImage;\n"], "names": [], "mappings": ";;;AASO,IAAA,AAAK,wCAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/actions/location.ts"], "sourcesContent": ["import { z } from 'zod';\n\nconst BaseActionLocationSchema = z.object({\n    type: z.enum(['prepend', 'append']),\n    targetDomId: z.string(),\n    targetOid: z.string().nullable(),\n});\n\nexport const IndexActionLocationSchema = BaseActionLocationSchema.extend({\n    type: z.literal('index'),\n    index: z.number(),\n    originalIndex: z.number(),\n});\n\nexport const ActionLocationSchema = z.discriminatedUnion('type', [\n    IndexActionLocationSchema,\n    BaseActionLocationSchema,\n]);\n\nexport type ActionLocation = z.infer<typeof ActionLocationSchema>;\nexport type IndexActionLocation = z.infer<typeof IndexActionLocationSchema>;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,MAAM,2BAA2B,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;KAAS;IAClC,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAClC;AAEO,MAAM,4BAA4B,yBAAyB,MAAM,CAAC;IACrE,MAAM,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM;IACf,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B;AAEO,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,kBAAkB,CAAC,QAAQ;IAC7D;IACA;CACH", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/actions/index.ts"], "sourcesContent": ["export * from './action.ts';\nexport * from './code.ts';\nexport * from './location.ts';\nexport * from './target.ts';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/assets/index.ts"], "sourcesContent": ["interface UpdateResult {\n    success: boolean;\n    error?: string;\n}\n\ninterface ColorUpdate {\n    configPath: string;\n    cssPath: string;\n    configContent: string;\n    cssContent: string;\n}\n\ninterface ConfigUpdateResult {\n    keyUpdated: boolean;\n    valueUpdated: boolean;\n    output: string;\n}\n\ninterface ClassReplacement {\n    oldClass: string;\n    newClass: string;\n}\n\ninterface ThemeColors {\n    [key: string]: {\n        value: string;\n        line?: number;\n    };\n}\n\ninterface ColorValue {\n    name: string;\n    lightMode: string;\n    darkMode: string;\n    line?: {\n        config?: number;\n        css?: {\n            lightMode?: number;\n            darkMode?: number;\n        };\n    };\n}\n\ninterface ParsedColors {\n    [key: string]: ColorValue;\n}\n\ninterface ConfigResult {\n    cssContent: string;\n    cssPath: string;\n    configPath: string;\n    configContent: any;\n}\n\ninterface Font {\n    id: string;\n    family: string;\n    subsets: string[];\n    variable: string;\n    weight?: string[];\n    styles?: string[];\n    type: string;\n}\n\nexport enum SystemTheme {\n    LIGHT = 'light',\n    DARK = 'dark',\n    SYSTEM = 'system',\n}\n\nexport type {\n    ClassReplacement,\n    ColorUpdate,\n    ColorValue,\n    ConfigResult,\n    ConfigUpdateResult,\n    Font,\n    ParsedColors,\n    ThemeColors,\n    UpdateResult,\n};\n"], "names": [], "mappings": ";;;AAgEO,IAAA,AAAK,qCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/auth/index.ts"], "sourcesContent": ["export enum SignInMethod {\n    GITHUB = 'github',\n    GOOGLE = 'google',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,sCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1361, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/message/context.ts"], "sourcesContent": ["export enum MessageContextType {\n    FILE = 'file',\n    HIGHLIGHT = 'highlight',\n    IMAGE = 'image',\n    ERROR = 'error',\n    PROJECT = 'project',\n}\n\ntype BaseMessageContext = {\n    type: MessageContextType;\n    content: string;\n    displayName: string;\n};\n\nexport type FileMessageContext = BaseMessageContext & {\n    type: MessageContextType.FILE;\n    path: string;\n};\n\nexport type HighlightMessageContext = BaseMessageContext & {\n    type: MessageContextType.HIGHLIGHT;\n    path: string;\n    start: number;\n    end: number;\n};\n\nexport type ImageMessageContext = BaseMessageContext & {\n    type: MessageContextType.IMAGE;\n    mimeType: string;\n};\n\nexport type ErrorMessageContext = BaseMessageContext & {\n    type: MessageContextType.ERROR;\n};\n\nexport type ProjectMessageContext = BaseMessageContext & {\n    type: MessageContextType.PROJECT;\n    path: string;\n};\n\nexport type ChatMessageContext =\n    | FileMessageContext\n    | HighlightMessageContext\n    | ImageMessageContext\n    | ErrorMessageContext\n    | ProjectMessageContext;\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,4CAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/message/message.ts"], "sourcesContent": ["import type { Message } from '@ai-sdk/react';\nimport type { TextPart } from 'ai';\nimport type { CodeDiff } from '../../code/index.ts';\nimport { type ChatMessageContext } from './context.ts';\n\nexport enum ChatMessageRole {\n    USER = 'user',\n    ASSISTANT = 'assistant',\n    SYSTEM = 'system',\n}\n\nexport interface UserChatMessage extends Message {\n    role: ChatMessageRole.USER;\n    context: ChatMessageContext[];\n    parts: TextPart[];\n    content: string;\n}\n\nexport interface AssistantChatMessage extends Message {\n    role: ChatMessageRole.ASSISTANT;\n    applied: boolean;\n    snapshots: ChatSnapshot;\n    parts: Message['parts'];\n    content: string;\n}\n\nexport type ChatSnapshot = Record<string, CodeDiff>;\n\nexport interface SystemChatMessage extends Message {\n    role: ChatMessageRole.SYSTEM;\n}\n\nexport type ChatMessage = UserChatMessage | AssistantChatMessage | SystemChatMessage;\n"], "names": [], "mappings": ";;;AAKO,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1401, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/message/index.ts"], "sourcesContent": ["export * from '../response.ts';\nexport * from './code.ts';\nexport * from './context.ts';\nexport * from './message.ts';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/request.ts"], "sourcesContent": ["import type { CoreMessage } from 'ai';\n\nexport enum StreamRequestType {\n    CHAT = 'chat',\n    CREATE = 'create',\n    ERROR_FIX = 'error-fix',\n    SUGGESTIONS = 'suggestions',\n    SUMMARY = 'summary',\n}\n\nexport type StreamRequest = {\n    messages: CoreMessage[];\n    systemPrompt: string;\n    requestType: StreamRequestType;\n    useAnalytics: boolean;\n};\n\nexport type StreamRequestV2 = {\n    messages: CoreMessage[];\n    requestType: StreamRequestType;\n    useAnalytics: boolean;\n};\n"], "names": [], "mappings": ";;;AAEO,IAAA,AAAK,2CAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/suggestion.ts"], "sourcesContent": ["import { z } from 'zod';\n\nexport interface ProjectSuggestions {\n    id: string;\n    projectId: string;\n    suggestions: ChatSuggestion[];\n}\n\nexport interface ChatSuggestion {\n    title: string;\n    prompt: string;\n}\n\nexport const ChatSuggestionSchema = z.object({\n    title: z\n        .string()\n        .describe(\n            'The display title of the suggestion. This will be shown to the user. Keep it concise but descriptive.',\n        ),\n    prompt: z\n        .string()\n        .describe(\n            'The prompt for the suggestion. This will be used to generate the suggestion. Make this as detailed and specific as possible.',\n        ),\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAaO,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,OAAO,iLAAA,CAAA,IAAC,CACH,MAAM,GACN,QAAQ,CACL;IAER,QAAQ,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,QAAQ,CACL;AAEZ", "debugId": null}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/summary.ts"], "sourcesContent": ["import { z } from 'zod';\n\nexport const ChatSummarySchema = z.object({\n    filesDiscussed: z\n        .array(z.string())\n        .describe('List of file paths mentioned in the conversation'),\n    projectContext: z\n        .string()\n        .describe('Summary of what the user is building and their overall goals'),\n    implementationDetails: z\n        .string()\n        .describe('Summary of key code decisions, patterns, and important implementation details'),\n    userPreferences: z\n        .string()\n        .describe('Specific preferences the user has expressed about implementation, design, etc.'),\n    currentStatus: z.string().describe('Current state of the project and any pending work'),\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,MAAM,oBAAoB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,gBAAgB,iLAAA,CAAA,IAAC,CACZ,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IACd,QAAQ,CAAC;IACd,gBAAgB,iLAAA,CAAA,IAAC,CACZ,MAAM,GACN,QAAQ,CAAC;IACd,uBAAuB,iLAAA,CAAA,IAAC,CACnB,MAAM,GACN,QAAQ,CAAC;IACd,iBAAiB,iLAAA,CAAA,IAAC,CACb,MAAM,GACN,QAAQ,CAAC;IACd,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/type.ts"], "sourcesContent": ["export enum ChatType {\n    ASK = 'ask',\n    CREATE = 'create',\n    EDIT = 'edit',\n    FIX = 'fix',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,kCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/index.ts"], "sourcesContent": ["export * from './conversation/';\nexport * from './message/';\nexport * from './request.ts';\nexport * from './response.ts';\nexport * from './suggestion.ts';\nexport * from './summary.ts';\nexport * from './type.ts';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1540, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/create/index.ts"], "sourcesContent": ["export enum CreateStage {\n    CLONING = 'cloning',\n    GIT_INIT = 'git_init',\n    INSTALLING = 'installing',\n    COMPLETE = 'complete',\n    ERROR = 'error',\n}\n\nexport enum VerifyStage {\n    CHECKING = 'checking',\n    NOT_INSTALLED = 'not_installed',\n    INSTALLED = 'installed',\n    ERROR = 'error',\n}\n\nexport enum SetupStage {\n    INSTALLING = 'installing',\n    CONFIGURING = 'configuring',\n    COMPLETE = 'complete',\n    ERROR = 'error',\n}\n\nexport interface CreateProjectResponse {\n    success: boolean;\n    error?: string;\n    response?: {\n        projectPath: string;\n        content: string;\n    };\n    cancelled?: boolean;\n}\n\nexport type CreateCallback = (stage: CreateStage, message: string) => void;\nexport type VerifyCallback = (stage: VerifyStage, message: string) => void;\nexport type SetupCallback = (stage: SetupStage, message: string) => void;\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,qCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,oCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1573, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/domain/index.ts"], "sourcesContent": ["export enum DomainType {\n    PREVIEW = 'preview',\n    CUSTOM = 'custom',\n}\n\nexport enum VerificationRequestStatus {\n    ACTIVE = 'active',\n    EXPIRED = 'expired',\n    USED = 'used',\n}\n\nexport interface DomainInfo {\n    url: string;\n    type: DomainType;\n    publishedAt?: string;\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,mDAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/editor/index.ts"], "sourcesContent": ["export interface WebviewMetadata {\n    id: string;\n    title: string;\n    src: string;\n}\n\nexport enum EditorMode {\n    DESIGN = 'design',\n    PREVIEW = 'preview',\n    PAN = 'pan',\n    INSERT_TEXT = 'insert-text',\n    INSERT_DIV = 'insert-div',\n    INSERT_IMAGE = 'insert-image',\n}\n\nexport enum EditorTabValue {\n    CHAT = 'chat',\n    DEV = 'dev',\n}\n\nexport enum SettingsTabValue {\n    SITE = 'site',\n    DOMAIN = 'domain',\n    PROJECT = 'project',\n    PREFERENCES = 'preferences',\n    VERSIONS = 'versions',\n    ADVANCED = 'advanced',\n}\n\nexport enum LeftPanelTabValue {\n    PAGES = 'pages',\n    LAYERS = 'layers',\n    COMPONENTS = 'components',\n    IMAGES = 'images',\n    WINDOWS = 'windows',\n    BRAND = 'brand',\n    APPS = 'apps',\n}\n\nexport enum BrandTabValue {\n    COLORS = 'colors',\n    FONTS = 'fonts',\n}\n\nexport enum MouseAction {\n    MOVE = 'move',\n    MOUSE_DOWN = 'click',\n    DOUBLE_CLICK = 'double-click',\n}\n"], "names": [], "mappings": ";;;;;;;;AAMO,IAAA,AAAK,oCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,wCAAA;;;WAAA;;AAKL,IAAA,AAAK,0CAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,2CAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,uCAAA;;;WAAA;;AAKL,IAAA,AAAK,qCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/element/layers.ts"], "sourcesContent": ["export enum DynamicType {\n    ARRAY = 'array',\n    CONDITIONAL = 'conditional',\n    UNKNOWN = 'unknown',\n}\n\nexport enum CoreElementType {\n    COMPONENT_ROOT = 'component-root',\n    BODY_TAG = 'body-tag',\n}\n\nexport interface LayerNode {\n    domId: string;\n    frameId: string;\n    instanceId: string | null;\n    oid: string | null;\n    textContent: string;\n    tagName: string;\n    isVisible: boolean;\n    dynamicType: DynamicType | null;\n    coreElementType: CoreElementType | null;\n    component: string | null;\n    children: string[] | null;\n    parent: string | null;\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,yCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 1689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/element/props.ts"], "sourcesContent": ["interface ParsedProps {\n    type: 'props';\n    props: NodeProps[];\n}\n\nexport enum PropsType {\n    String = 'string',\n    Number = 'number',\n    Boolean = 'boolean',\n    Object = 'object',\n    Array = 'array',\n    Code = 'code',\n}\n\nexport interface NodeProps {\n    key: any;\n    value: any;\n    type: PropsType;\n}\n\ninterface PropsParsingError {\n    type: 'error';\n    reason: string;\n}\n\nexport type PropsParsingResult = ParsedProps | PropsParsingError;\n"], "names": [], "mappings": ";;;AAKO,IAAA,AAAK,mCAAA;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/element/index.ts"], "sourcesContent": ["export * from './classes';\nexport * from './element';\nexport * from './layers';\nexport * from './templateNode';\nexport * from './props';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1745, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/hosting/index.ts"], "sourcesContent": ["export enum PublishStatus {\n    UNPUBLISHED = 'unpublished',\n    LOADING = 'loading',\n    PUBLISHED = 'published',\n    ERROR = 'error',\n}\n\nexport interface PublishState {\n    status: PublishStatus;\n    message: string | null;\n    buildLog: string | null;\n    error: string | null;\n    progress: number | null;\n}\n\nexport interface CustomDomain {\n    id: string;\n    user_id: string;\n    domain: string;\n    subdomains: string[];\n    created_at: string;\n    updated_at: string;\n}\n\nexport interface CreateDomainVerificationResponse {\n    success: boolean;\n    message?: string;\n    verificationCode?: string;\n}\n\nexport interface VerifyDomainResponse {\n    success: boolean;\n    message?: string;\n}\n\nexport interface PublishOptions {\n    skipBadge?: boolean;\n    skipBuild?: boolean;\n    buildFlags?: string;\n    envVars?: Record<string, string>;\n}\n\nexport interface PublishRequest {\n    buildScript: string;\n    urls: string[];\n    options?: PublishOptions;\n}\n\nexport interface PublishResponse {\n    success: boolean;\n    message: string;\n}\n\nexport enum HostingProvider {\n    FREESTYLE = 'freestyle',\n}\n\nexport interface DeploymentFile {\n    content: string;\n    encoding?: 'utf-8' | 'base64';\n}\n\nexport interface DeploymentConfig {\n    domains: string[];\n    entrypoint?: string;\n    envVars?: Record<string, string>;\n}\n\nexport interface DeploymentRequest {\n    files: Record<string, DeploymentFile>;\n    config: DeploymentConfig;\n}\n\nexport interface DeploymentResponse {\n    deploymentId: string;\n    success: boolean;\n    message?: string;\n}\n\nexport interface HostingProviderAdapter {\n    deploy(request: DeploymentRequest): Promise<DeploymentResponse>;\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,uCAAA;;;;;WAAA;;AAqDL,IAAA,AAAK,yCAAA;;WAAA", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/ide/index.ts"], "sourcesContent": ["export enum IdeType {\n    VS_CODE = 'VSCode',\n    CURSOR = 'Cursor',\n    ZED = 'Zed',\n    WINDSURF = 'Windsurf',\n    ONLOOK = 'Onlook',\n}\n\nexport const DEFAULT_IDE = IdeType.ONLOOK;\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,iCAAA;;;;;;WAAA;;AAQL,MAAM", "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/llm/index.ts"], "sourcesContent": ["export enum LLMProvider {\r\n    ANTHROPIC = 'anthropic',\r\n    BEDROCK = 'bedrock',\r\n}\r\n\r\nexport enum CLAUDE_MODELS {\r\n    SONNET_4 = 'claude-sonnet-4-20250514',\r\n    SONNET_3_7 = 'claude-3-7-sonnet-20250219',\r\n    HAIKU = 'claude-3-5-haiku-20241022',\r\n}\r\n\r\nexport const BEDROCK_MODEL_MAP = {\r\n    [CLAUDE_MODELS.SONNET_4]: 'us.anthropic.claude-sonnet-4-20250514-v1:0',\r\n    [CLAUDE_MODELS.SONNET_3_7]: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',\r\n    [CLAUDE_MODELS.HAIKU]: 'us.anthropic.claude-3-5-haiku-20241022-v1:0',\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,IAAA,AAAK,uCAAA;;;;WAAA;;AAML,MAAM,oBAAoB;IAC7B,4BAAwB,EAAE;IAC1B,8BAA0B,EAAE;IAC5B,6BAAqB,EAAE;AAC3B", "debugId": null}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/frame.ts"], "sourcesContent": ["import { Orientation, Theme } from '@onlook/constants';\nimport type { RectDimension, RectPosition } from './rect';\n\nexport enum FrameType {\n    WEB = 'web',\n}\n\nexport interface Frame {\n    id: string;\n    position: RectPosition;\n    type: FrameType;\n    dimension: RectDimension;\n}\n\nexport interface WebFrame extends Frame {\n    url: string;\n    type: FrameType.WEB;\n}\n\nexport interface WindowMetadata {\n    orientation: Orientation;\n    aspectRatioLocked: boolean;\n    device: string;\n    theme: Theme;\n    width: number;\n    height: number;\n}\n"], "names": [], "mappings": ";;;AAGO,IAAA,AAAK,mCAAA;;WAAA", "debugId": null}}, {"offset": {"line": 1849, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/role.ts"], "sourcesContent": ["export enum ProjectRole {\n    OWNER = 'owner',\n    ADMIN = 'admin',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,qCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 1887, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/settings.ts"], "sourcesContent": ["import type { Commands } from './command';\n\nexport interface ProjectSettings {\n    commands: Commands;\n}\n\nexport const DEFAULT_PROJECT_SETTINGS: ProjectSettings = {\n    commands: {\n        build: '',\n        run: '',\n        install: '',\n    },\n};\n"], "names": [], "mappings": ";;;AAMO,MAAM,2BAA4C;IACrD,UAAU;QACN,OAAO;QACP,KAAK;QACL,SAAS;IACb;AACJ", "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/index.ts"], "sourcesContent": ["export * from './canvas';\nexport * from './command';\nexport * from './frame';\nexport * from './invitation';\nexport * from './project';\nexport * from './rect';\nexport * from './role';\nexport * from './settings';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/run/index.ts"], "sourcesContent": ["export enum RunState {\n    STOPPED = 'stopped',\n    SETTING_UP = 'setting-up',\n    RUNNING = 'running',\n    STOPPING = 'stopping',\n    ERROR = 'error',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,kCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/style/index.ts"], "sourcesContent": ["export interface StyleChange {\n    value: string;\n    type: StyleChangeType;\n}\n\nexport enum StyleChangeType {\n    Value = 'value',\n    Custom = 'custom',\n    Remove = 'remove',\n}\n\nexport interface TailwindColor {\n    name: string;\n    originalKey: string;\n    lightColor: string;\n    darkColor?: string;\n    line?: {\n        config?: number;\n        css?: {\n            lightMode?: number;\n            darkMode?: number;\n        };\n    };\n    override?: boolean;\n}\n"], "names": [], "mappings": ";;;AAKO,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/usage/index.ts"], "sourcesContent": ["export enum UsageType {\n    MESSAGE = 'message',\n    DEPLOYMENT = 'deployment',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,mCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/user/index.ts"], "sourcesContent": ["export * from './settings';\nexport * from './user';\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/index.ts"], "sourcesContent": ["export * from './actions/';\nexport * from './assets/';\nexport * from './auth/';\nexport * from './chat/';\nexport * from './code/';\nexport * from './create/';\nexport * from './domain/';\nexport * from './editor/';\nexport * from './element/';\nexport * from './hosting/';\nexport * from './ide/';\nexport * from './llm/';\nexport * from './pages/';\nexport * from './project/';\nexport * from './run/';\nexport * from './sandbox/';\nexport * from './style/';\nexport * from './usage/';\nexport * from './user/';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/frame.ts"], "sourcesContent": ["import { DefaultSettings } from '@onlook/constants';\r\nimport type { Frame as DbFrame } from '@onlook/db';\r\nimport { FrameType } from '@onlook/models';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nexport const createDefaultFrame = (canvasId: string, url: string): DbFrame => {\r\n    return {\r\n        id: uuidv4(),\r\n        canvasId: canvasId,\r\n        type: FrameType.WEB,\r\n        url: url,\r\n        x: DefaultSettings.FRAME_POSITION.x.toString(),\r\n        y: DefaultSettings.FRAME_POSITION.y.toString(),\r\n        width: DefaultSettings.FRAME_DIMENSION.width.toString(),\r\n        height: DefaultSettings.FRAME_DIMENSION.height.toString(),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAEA;AAAA;AACA;;;;AAEO,MAAM,qBAAqB,CAAC,UAAkB;IACjD,OAAO;QACH,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG;QACnB,KAAK;QACL,GAAG,sIAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ;QAC5C,GAAG,sIAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ;QAC5C,OAAO,sIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ;QACrD,QAAQ,sIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ;IAC3D;AACJ", "debugId": null}}, {"offset": {"line": 2134, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/user-canvas.ts"], "sourcesContent": ["import { DefaultSettings } from '@onlook/constants';\r\nimport type { UserCanvas as DbUserCanvas } from '@onlook/db';\r\n\r\nexport const createDefaultUserCanvas = (userId: string, canvasId: string): DbUserCanvas => {\r\n    return {\r\n        userId,\r\n        canvasId,\r\n        scale: DefaultSettings.SCALE.toString(),\r\n        x: DefaultSettings.PAN_POSITION.x.toString(),\r\n        y: DefaultSettings.PAN_POSITION.y.toString(),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,MAAM,0BAA0B,CAAC,QAAgB;IACpD,OAAO;QACH;QACA;QACA,OAAO,sIAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,QAAQ;QACrC,GAAG,sIAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ;QAC1C,GAAG,sIAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ;IAC9C;AACJ", "debugId": null}}, {"offset": {"line": 2155, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/user-settings.ts"], "sourcesContent": ["import { DefaultSettings } from '@onlook/constants';\r\nimport type { UserSettings as DbUserSettings } from '@onlook/db';\r\nimport { v4 as uuid } from 'uuid';\r\n\r\nexport const createDefaultUserSettings = (userId: string): DbUserSettings => {\r\n    return {\r\n        id: uuid(),\r\n        userId,\r\n        autoApplyCode: DefaultSettings.CHAT_SETTINGS.autoApplyCode,\r\n        expandCodeBlocks: DefaultSettings.CHAT_SETTINGS.expandCodeBlocks,\r\n        showSuggestions: DefaultSettings.CHAT_SETTINGS.showSuggestions,\r\n        showMiniChat: DefaultSettings.CHAT_SETTINGS.showMiniChat,\r\n        shouldWarnDelete: DefaultSettings.EDITOR_SETTINGS.shouldWarnDelete,\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAEA;;;AAEO,MAAM,4BAA4B,CAAC;IACtC,OAAO;QACH,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAI,AAAD;QACP;QACA,eAAe,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,aAAa;QAC1D,kBAAkB,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,gBAAgB;QAChE,iBAAiB,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,eAAe;QAC9D,cAAc,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,YAAY;QACxD,kBAAkB,sIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,gBAAgB;IACtE;AACJ", "debugId": null}}, {"offset": {"line": 2180, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/project-settings.ts"], "sourcesContent": ["import { DefaultSettings } from '@onlook/constants';\r\nimport type { ProjectSettings as DbProjectSettings } from '@onlook/db';\r\n\r\nexport const createDefaultProjectSettings = (projectId: string): DbProjectSettings => {\r\n    return {\r\n        projectId,\r\n        buildCommand: DefaultSettings.COMMANDS.build,\r\n        runCommand: DefaultSettings.COMMANDS.run,\r\n        installCommand: DefaultSettings.COMMANDS.install,\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,MAAM,+BAA+B,CAAC;IACzC,OAAO;QACH;QACA,cAAc,sIAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,KAAK;QAC5C,YAAY,sIAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;QACxC,gBAAgB,sIAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;IACpD;AACJ", "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/defaults/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './conversation';\r\nexport * from './frame';\r\nexport * from './user-canvas';\r\nexport * from './user-settings';\r\nexport * from './project-settings';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/canvas.ts"], "sourcesContent": ["import type { Canvas } from '@onlook/models';\r\nimport type { UserCanvas as DbUserCanvas } from '../schema';\r\n\r\nexport const toCanvas = (dbUserCanvas: DbUserCanvas): Canvas => {\r\n    return {\r\n        id: dbUserCanvas.canvasId,\r\n        scale: Number(dbUserCanvas.scale),\r\n        position: {\r\n            x: Number(dbUserCanvas.x),\r\n            y: Number(dbUserCanvas.y),\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromCanvas = (canvas: Canvas): Omit<DbUserCanvas, 'userId'> => {\r\n    return {\r\n        scale: canvas.scale.toString(),\r\n        x: canvas.position.x.toString(),\r\n        y: canvas.position.y.toString(),\r\n        canvasId: canvas.id,\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,WAAW,CAAC;IACrB,OAAO;QACH,IAAI,aAAa,QAAQ;QACzB,OAAO,OAAO,aAAa,KAAK;QAChC,UAAU;YACN,GAAG,OAAO,aAAa,CAAC;YACxB,GAAG,OAAO,aAAa,CAAC;QAC5B;IACJ;AACJ;AAEO,MAAM,aAAa,CAAC;IACvB,OAAO;QACH,OAAO,OAAO,KAAK,CAAC,QAAQ;QAC5B,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC7B,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC7B,UAAU,OAAO,EAAE;IACvB;AACJ", "debugId": null}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/conversation.ts"], "sourcesContent": ["import { type ChatConversation } from \"@onlook/models\";\r\nimport type { Conversation as DbConversation } from \"../schema\";\r\n\r\nexport const toConversation = (dbConversation: DbConversation): ChatConversation => {\r\n    return {\r\n        id: dbConversation.id,\r\n        displayName: dbConversation.displayName,\r\n        createdAt: dbConversation.createdAt.toISOString(),\r\n        updatedAt: dbConversation.updatedAt.toISOString(),\r\n        projectId: dbConversation.projectId,\r\n    }\r\n}\r\n\r\nexport const fromConversation = (conversation: ChatConversation): DbConversation => {\r\n    return {\r\n        id: conversation.id,\r\n        displayName: conversation.displayName,\r\n        createdAt: new Date(conversation.createdAt),\r\n        updatedAt: new Date(conversation.updatedAt),\r\n        projectId: conversation.projectId,\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,iBAAiB,CAAC;IAC3B,OAAO;QACH,IAAI,eAAe,EAAE;QACrB,aAAa,eAAe,WAAW;QACvC,WAAW,eAAe,SAAS,CAAC,WAAW;QAC/C,WAAW,eAAe,SAAS,CAAC,WAAW;QAC/C,WAAW,eAAe,SAAS;IACvC;AACJ;AAEO,MAAM,mBAAmB,CAAC;IAC7B,OAAO;QACH,IAAI,aAAa,EAAE;QACnB,aAAa,aAAa,WAAW;QACrC,WAAW,IAAI,KAAK,aAAa,SAAS;QAC1C,WAAW,IAAI,KAAK,aAAa,SAAS;QAC1C,WAAW,aAAa,SAAS;IACrC;AACJ", "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/domain.ts"], "sourcesContent": ["import { type DomainInfo, DomainType } from '@onlook/models';\r\nimport type { PreviewDomain, PublishedDomain } from '../schema';\r\n\r\nexport const toDomainInfoFromPreview = (previewDomain: PreviewDomain): DomainInfo => {\r\n    return {\r\n        url: previewDomain.fullDomain,\r\n        type: DomainType.PREVIEW,\r\n        publishedAt: previewDomain.updatedAt.toISOString(),\r\n    };\r\n};\r\n\r\nexport const toDomainInfoFromPublished = (publishedDomain: PublishedDomain): DomainInfo => {\r\n    return {\r\n        url: publishedDomain.fullDomain,\r\n        type: DomainType.CUSTOM,\r\n        publishedAt: publishedDomain.updatedAt.toISOString(),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,MAAM,0BAA0B,CAAC;IACpC,OAAO;QACH,KAAK,cAAc,UAAU;QAC7B,MAAM,4IAAA,CAAA,aAAU,CAAC,OAAO;QACxB,aAAa,cAAc,SAAS,CAAC,WAAW;IACpD;AACJ;AAEO,MAAM,4BAA4B,CAAC;IACtC,OAAO;QACH,KAAK,gBAAgB,UAAU;QAC/B,MAAM,4IAAA,CAAA,aAAU,CAAC,MAAM;QACvB,aAAa,gBAAgB,SAAS,CAAC,WAAW;IACtD;AACJ", "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/frame.ts"], "sourcesContent": ["import { FrameType, type WebFrame } from '@onlook/models';\r\nimport type { Frame as DbFrame } from '../schema';\r\n\r\nexport const toFrame = (dbFrame: DbFrame): WebFrame => {\r\n    return {\r\n        id: dbFrame.id,\r\n        url: dbFrame.url,\r\n        type: dbFrame.type as FrameType,\r\n        position: {\r\n            x: Number(dbFrame.x),\r\n            y: Number(dbFrame.y),\r\n        },\r\n        dimension: {\r\n            width: Number(dbFrame.width),\r\n            height: Number(dbFrame.height),\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromFrame = (canvasId: string, frame: WebFrame): DbFrame => {\r\n    return {\r\n        id: frame.id,\r\n        url: frame.url,\r\n        type: frame.type as FrameType,\r\n        x: frame.position.x.toString(),\r\n        y: frame.position.y.toString(),\r\n        canvasId: canvasId,\r\n        width: frame.dimension.width.toString(),\r\n        height: frame.dimension.height.toString(),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,UAAU,CAAC;IACpB,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,KAAK,QAAQ,GAAG;QAChB,MAAM,QAAQ,IAAI;QAClB,UAAU;YACN,GAAG,OAAO,QAAQ,CAAC;YACnB,GAAG,OAAO,QAAQ,CAAC;QACvB;QACA,WAAW;YACP,OAAO,OAAO,QAAQ,KAAK;YAC3B,QAAQ,OAAO,QAAQ,MAAM;QACjC;IACJ;AACJ;AAEO,MAAM,YAAY,CAAC,UAAkB;IACxC,OAAO;QACH,IAAI,MAAM,EAAE;QACZ,KAAK,MAAM,GAAG;QACd,MAAM,MAAM,IAAI;QAChB,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC5B,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC5B,UAAU;QACV,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,QAAQ;QACrC,QAAQ,MAAM,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC3C;AACJ", "debugId": null}}, {"offset": {"line": 2353, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/message.ts"], "sourcesContent": ["import { ChatMessageRole, type ChatMessage, type ChatMessageContext, type ChatSnapshot } from \"@onlook/models\";\r\nimport type { TextPart } from \"ai\";\r\nimport type { Message as DbMessage } from \"../schema\";\r\n\r\nexport const toMessage = (dbMessage: DbMessage): ChatMessage => {\r\n    if (dbMessage.role === ChatMessageRole.ASSISTANT) {\r\n        return {\r\n            id: dbMessage.id,\r\n            content: dbMessage.content,\r\n            role: dbMessage.role,\r\n            createdAt: dbMessage.createdAt,\r\n            applied: dbMessage.applied,\r\n            snapshots: dbMessage.snapshots,\r\n            parts: dbMessage.parts,\r\n        }\r\n    } else if (dbMessage.role === ChatMessageRole.USER) {\r\n        return {\r\n            id: dbMessage.id,\r\n            content: dbMessage.content,\r\n            role: dbMessage.role,\r\n            createdAt: dbMessage.createdAt,\r\n            context: dbMessage.context,\r\n            parts: dbMessage.parts as TextPart[],\r\n        }\r\n    } else {\r\n        return {\r\n            id: dbMessage.id,\r\n            content: dbMessage.content,\r\n            role: dbMessage.role as ChatMessageRole.SYSTEM,\r\n            createdAt: dbMessage.createdAt,\r\n        }\r\n    }\r\n}\r\n\r\nexport const fromMessage = (conversationId: string, message: ChatMessage): DbMessage => {\r\n    let snapshots: ChatSnapshot = {};\r\n    let context: ChatMessageContext[] = [];\r\n\r\n    if (message.role === ChatMessageRole.ASSISTANT) {\r\n        snapshots = message.snapshots;\r\n    }\r\n\r\n    if (message.role === ChatMessageRole.USER) {\r\n        context = message.context;\r\n    }\r\n\r\n    return {\r\n        id: message.id,\r\n        content: message.content,\r\n        role: message.role,\r\n        createdAt: message.createdAt ?? new Date(),\r\n        conversationId,\r\n        applied: message.role === ChatMessageRole.ASSISTANT ? message.applied ?? false : false,\r\n        snapshots,\r\n        context,\r\n        parts: message.parts\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAIO,MAAM,YAAY,CAAC;IACtB,IAAI,UAAU,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,SAAS,EAAE;QAC9C,OAAO;YACH,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO;YAC1B,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,SAAS;YAC9B,SAAS,UAAU,OAAO;YAC1B,WAAW,UAAU,SAAS;YAC9B,OAAO,UAAU,KAAK;QAC1B;IACJ,OAAO,IAAI,UAAU,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,IAAI,EAAE;QAChD,OAAO;YACH,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO;YAC1B,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,SAAS;YAC9B,SAAS,UAAU,OAAO;YAC1B,OAAO,UAAU,KAAK;QAC1B;IACJ,OAAO;QACH,OAAO;YACH,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO;YAC1B,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,SAAS;QAClC;IACJ;AACJ;AAEO,MAAM,cAAc,CAAC,gBAAwB;IAChD,IAAI,YAA0B,CAAC;IAC/B,IAAI,UAAgC,EAAE;IAEtC,IAAI,QAAQ,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,SAAS,EAAE;QAC5C,YAAY,QAAQ,SAAS;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,IAAI,EAAE;QACvC,UAAU,QAAQ,OAAO;IAC7B;IAEA,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,SAAS,QAAQ,OAAO;QACxB,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI,IAAI;QACpC;QACA,SAAS,QAAQ,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,SAAS,GAAG,QAAQ,OAAO,IAAI,QAAQ;QACjF;QACA;QACA,OAAO,QAAQ,KAAK;IACxB;AACJ", "debugId": null}}, {"offset": {"line": 2416, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/project.ts"], "sourcesContent": ["import type { PreviewImg, Project } from '@onlook/models';\r\nimport type { Project as DbProject } from '../schema';\r\n\r\nexport const toProject = (\r\n    dbProject: DbProject,\r\n): Project => {\r\n    return {\r\n        id: dbProject.id,\r\n        name: dbProject.name,\r\n        sandbox: {\r\n            id: dbProject.sandboxId,\r\n            url: dbProject.sandboxUrl,\r\n        },\r\n        metadata: {\r\n            createdAt: dbProject.createdAt.toISOString(),\r\n            updatedAt: dbProject.updatedAt.toISOString(),\r\n            previewImg: getPreviewImgFromDb(dbProject),\r\n            description: dbProject.description,\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromProject = (project: Project): DbProject => {\r\n    const { previewImgUrl, previewImgPath, previewImgBucket } = getPreviewImgFromModel(project.metadata.previewImg);\r\n    return {\r\n        id: project.id,\r\n        name: project.name,\r\n        sandboxId: project.sandbox.id,\r\n        sandboxUrl: project.sandbox.url,\r\n        createdAt: new Date(project.metadata.createdAt),\r\n        updatedAt: new Date(project.metadata.updatedAt),\r\n        description: project.metadata.description,\r\n        previewImgUrl,\r\n        previewImgPath,\r\n        previewImgBucket,\r\n    };\r\n};\r\n\r\nfunction getPreviewImgFromDb(dbProject: DbProject): PreviewImg | null {\r\n    let previewImg: PreviewImg | null = null;\r\n    if (dbProject.previewImgUrl) {\r\n        previewImg = {\r\n            type: 'url',\r\n            url: dbProject.previewImgUrl,\r\n        };\r\n    } else if (dbProject.previewImgPath && dbProject.previewImgBucket) {\r\n        previewImg = {\r\n            type: 'storage',\r\n            storagePath: {\r\n                bucket: dbProject.previewImgBucket,\r\n                path: dbProject.previewImgPath,\r\n            },\r\n        };\r\n    }\r\n    return previewImg;\r\n}\r\n\r\nfunction getPreviewImgFromModel(previewImg: PreviewImg | null): { previewImgUrl: string | null, previewImgPath: string | null, previewImgBucket: string | null } {\r\n    let res: {\r\n        previewImgUrl: string | null,\r\n        previewImgPath: string | null,\r\n        previewImgBucket: string | null,\r\n    } = {\r\n        previewImgUrl: null,\r\n        previewImgPath: null,\r\n        previewImgBucket: null,\r\n    };\r\n\r\n    if (!previewImg) {\r\n        return res;\r\n    }\r\n\r\n    if (previewImg.type === 'url' && previewImg.url) {\r\n        res.previewImgUrl = previewImg.url;\r\n    } else if (previewImg.type === 'storage' && previewImg.storagePath && previewImg.storagePath.path && previewImg.storagePath.bucket) {\r\n        res.previewImgPath = previewImg.storagePath.path;\r\n        res.previewImgBucket = previewImg.storagePath.bucket;\r\n    }\r\n    return res;\r\n}"], "names": [], "mappings": ";;;;AAGO,MAAM,YAAY,CACrB;IAEA,OAAO;QACH,IAAI,UAAU,EAAE;QAChB,MAAM,UAAU,IAAI;QACpB,SAAS;YACL,IAAI,UAAU,SAAS;YACvB,KAAK,UAAU,UAAU;QAC7B;QACA,UAAU;YACN,WAAW,UAAU,SAAS,CAAC,WAAW;YAC1C,WAAW,UAAU,SAAS,CAAC,WAAW;YAC1C,YAAY,oBAAoB;YAChC,aAAa,UAAU,WAAW;QACtC;IACJ;AACJ;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG,uBAAuB,QAAQ,QAAQ,CAAC,UAAU;IAC9G,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,OAAO,CAAC,EAAE;QAC7B,YAAY,QAAQ,OAAO,CAAC,GAAG;QAC/B,WAAW,IAAI,KAAK,QAAQ,QAAQ,CAAC,SAAS;QAC9C,WAAW,IAAI,KAAK,QAAQ,QAAQ,CAAC,SAAS;QAC9C,aAAa,QAAQ,QAAQ,CAAC,WAAW;QACzC;QACA;QACA;IACJ;AACJ;AAEA,SAAS,oBAAoB,SAAoB;IAC7C,IAAI,aAAgC;IACpC,IAAI,UAAU,aAAa,EAAE;QACzB,aAAa;YACT,MAAM;YACN,KAAK,UAAU,aAAa;QAChC;IACJ,OAAO,IAAI,UAAU,cAAc,IAAI,UAAU,gBAAgB,EAAE;QAC/D,aAAa;YACT,MAAM;YACN,aAAa;gBACT,QAAQ,UAAU,gBAAgB;gBAClC,MAAM,UAAU,cAAc;YAClC;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,uBAAuB,UAA6B;IACzD,IAAI,MAIA;QACA,eAAe;QACf,gBAAgB;QAChB,kBAAkB;IACtB;IAEA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IAEA,IAAI,WAAW,IAAI,KAAK,SAAS,WAAW,GAAG,EAAE;QAC7C,IAAI,aAAa,GAAG,WAAW,GAAG;IACtC,OAAO,IAAI,WAAW,IAAI,KAAK,aAAa,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,IAAI,IAAI,WAAW,WAAW,CAAC,MAAM,EAAE;QAChI,IAAI,cAAc,GAAG,WAAW,WAAW,CAAC,IAAI;QAChD,IAAI,gBAAgB,GAAG,WAAW,WAAW,CAAC,MAAM;IACxD;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 2492, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/user.ts"], "sourcesContent": ["import { DefaultSettings } from '@onlook/constants';\r\nimport type { UserMetadata, UserSettings } from '@onlook/models';\r\nimport { get } from 'lodash';\r\nimport type { AuthUser, UserSettings as DbUserSettings } from '../schema';\r\n\r\nexport const toUserSettings = (settings: DbUserSettings): UserSettings => {\r\n    return {\r\n        id: settings.id,\r\n        chat: {\r\n            autoApplyCode: settings.autoApplyCode ?? DefaultSettings.CHAT_SETTINGS.autoApplyCode,\r\n            expandCodeBlocks:\r\n                settings.expandCodeBlocks ?? DefaultSettings.CHAT_SETTINGS.expandCodeBlocks,\r\n            showSuggestions:\r\n                settings.showSuggestions ?? DefaultSettings.CHAT_SETTINGS.showSuggestions,\r\n            showMiniChat: settings.showMiniChat ?? DefaultSettings.CHAT_SETTINGS.showMiniChat,\r\n        },\r\n        editor: {\r\n            shouldWarnDelete: settings.shouldWarnDelete ?? DefaultSettings.EDITOR_SETTINGS.shouldWarnDelete,\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromUserSettings = (userId: string, settings: UserSettings): DbUserSettings => {\r\n    return {\r\n        id: settings.id,\r\n        userId,\r\n        autoApplyCode: settings.chat.autoApplyCode,\r\n        expandCodeBlocks: settings.chat.expandCodeBlocks,\r\n        showSuggestions: settings.chat.showSuggestions,\r\n        showMiniChat: settings.chat.showMiniChat,\r\n        shouldWarnDelete: settings.editor.shouldWarnDelete,\r\n    };\r\n};\r\n\r\nexport const fromAuthUser = (authUser: AuthUser): UserMetadata => {\r\n    return {\r\n        id: authUser.id,\r\n        name: get(authUser.rawUserMetaData, 'full_name'),\r\n        email: authUser.email,\r\n        avatarUrl: get(authUser.rawUserMetaData, 'avatar_url'),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAEA;;;AAGO,MAAM,iBAAiB,CAAC;IAC3B,OAAO;QACH,IAAI,SAAS,EAAE;QACf,MAAM;YACF,eAAe,SAAS,aAAa,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,aAAa;YACpF,kBACI,SAAS,gBAAgB,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,gBAAgB;YAC/E,iBACI,SAAS,eAAe,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,eAAe;YAC7E,cAAc,SAAS,YAAY,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,YAAY;QACrF;QACA,QAAQ;YACJ,kBAAkB,SAAS,gBAAgB,IAAI,sIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,gBAAgB;QACnG;IACJ;AACJ;AAEO,MAAM,mBAAmB,CAAC,QAAgB;IAC7C,OAAO;QACH,IAAI,SAAS,EAAE;QACf;QACA,eAAe,SAAS,IAAI,CAAC,aAAa;QAC1C,kBAAkB,SAAS,IAAI,CAAC,gBAAgB;QAChD,iBAAiB,SAAS,IAAI,CAAC,eAAe;QAC9C,cAAc,SAAS,IAAI,CAAC,YAAY;QACxC,kBAAkB,SAAS,MAAM,CAAC,gBAAgB;IACtD;AACJ;AAEO,MAAM,eAAe,CAAC;IACzB,OAAO;QACH,IAAI,SAAS,EAAE;QACf,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAG,AAAD,EAAE,SAAS,eAAe,EAAE;QACpC,OAAO,SAAS,KAAK;QACrB,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAG,AAAD,EAAE,SAAS,eAAe,EAAE;IAC7C;AACJ", "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/subscription.ts"], "sourcesContent": ["import type { Price, Product, ScheduledChange, ScheduledSubscriptionAction, Subscription } from '@onlook/stripe';\r\nimport type { Price as DbPrice, Product as DbProduct, Subscription as DbSubscription } from '../schema';\r\n\r\nexport function toSubscription(\r\n    subscription: DbSubscription & {\r\n        product: DbProduct;\r\n        price: DbPrice;\r\n    },\r\n    scheduledPrice: DbPrice | null,\r\n): Subscription {\r\n    return {\r\n        id: subscription.id,\r\n        status: subscription.status,\r\n        startedAt: subscription.startedAt,\r\n        endedAt: subscription.endedAt,\r\n        product: toProduct(subscription.product),\r\n        price: toPrice(subscription.price),\r\n        scheduledChange: toScheduledChange(scheduledPrice, subscription.scheduledAction, subscription.scheduledChangeAt, subscription.stripeSubscriptionScheduleId),\r\n\r\n        stripeSubscriptionId: subscription.stripeSubscriptionId,\r\n        stripeCustomerId: subscription.stripeCustomerId,\r\n        stripeSubscriptionItemId: subscription.stripeSubscriptionItemId,\r\n    };\r\n}\r\n\r\nexport function toProduct(product: DbProduct): Product {\r\n    return {\r\n        name: product.name,\r\n        type: product.type,\r\n        stripeProductId: product.stripeProductId,\r\n    };\r\n}\r\n\r\nexport function toPrice(price: DbPrice): Price {\r\n    return {\r\n        id: price.id,\r\n        productId: price.productId,\r\n        monthlyMessageLimit: price.monthlyMessageLimit,\r\n        stripePriceId: price.stripePriceId,\r\n        key: price.key,\r\n    };\r\n}\r\n\r\nexport function toScheduledChange(\r\n    price: DbPrice | null,\r\n    scheduledAction: ScheduledSubscriptionAction | null,\r\n    scheduledChangeAt: Date | null,\r\n    stripeSubscriptionScheduleId: string | null,\r\n): ScheduledChange | null {\r\n\r\n    if (!scheduledAction || !scheduledChangeAt) {\r\n        return null;\r\n    }\r\n\r\n    return {\r\n        price: price ? toPrice(price) : null,\r\n        scheduledAction,\r\n        scheduledChangeAt,\r\n        stripeSubscriptionScheduleId,\r\n    };\r\n}"], "names": [], "mappings": ";;;;;;AAGO,SAAS,eACZ,YAGC,EACD,cAA8B;IAE9B,OAAO;QACH,IAAI,aAAa,EAAE;QACnB,QAAQ,aAAa,MAAM;QAC3B,WAAW,aAAa,SAAS;QACjC,SAAS,aAAa,OAAO;QAC7B,SAAS,UAAU,aAAa,OAAO;QACvC,OAAO,QAAQ,aAAa,KAAK;QACjC,iBAAiB,kBAAkB,gBAAgB,aAAa,eAAe,EAAE,aAAa,iBAAiB,EAAE,aAAa,4BAA4B;QAE1J,sBAAsB,aAAa,oBAAoB;QACvD,kBAAkB,aAAa,gBAAgB;QAC/C,0BAA0B,aAAa,wBAAwB;IACnE;AACJ;AAEO,SAAS,UAAU,OAAkB;IACxC,OAAO;QACH,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,IAAI;QAClB,iBAAiB,QAAQ,eAAe;IAC5C;AACJ;AAEO,SAAS,QAAQ,KAAc;IAClC,OAAO;QACH,IAAI,MAAM,EAAE;QACZ,WAAW,MAAM,SAAS;QAC1B,qBAAqB,MAAM,mBAAmB;QAC9C,eAAe,MAAM,aAAa;QAClC,KAAK,MAAM,GAAG;IAClB;AACJ;AAEO,SAAS,kBACZ,KAAqB,EACrB,eAAmD,EACnD,iBAA8B,EAC9B,4BAA2C;IAG3C,IAAI,CAAC,mBAAmB,CAAC,mBAAmB;QACxC,OAAO;IACX;IAEA,OAAO;QACH,OAAO,QAAQ,QAAQ,SAAS;QAChC;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/setting.ts"], "sourcesContent": ["import type { ProjectSettings } from '@onlook/models';\r\nimport type { ProjectSettings as DbProjectSettings } from '../schema';\r\n\r\nexport const toProjectSettings = (dbProjectSettings: DbProjectSettings): ProjectSettings => {\r\n    return {\r\n        commands: {\r\n            build: dbProjectSettings.buildCommand,\r\n            run: dbProjectSettings.runCommand,\r\n            install: dbProjectSettings.installCommand,\r\n        }\r\n    };\r\n};\r\n\r\nexport const fromProjectSettings = (projectId: string, projectSettings: ProjectSettings): DbProjectSettings => {\r\n    return {\r\n        projectId,\r\n        buildCommand: projectSettings.commands.build ?? '',\r\n        runCommand: projectSettings.commands.run ?? '',\r\n        installCommand: projectSettings.commands.install ?? ''\r\n    };\r\n};"], "names": [], "mappings": ";;;;AAGO,MAAM,oBAAoB,CAAC;IAC9B,OAAO;QACH,UAAU;YACN,OAAO,kBAAkB,YAAY;YACrC,KAAK,kBAAkB,UAAU;YACjC,SAAS,kBAAkB,cAAc;QAC7C;IACJ;AACJ;AAEO,MAAM,sBAAsB,CAAC,WAAmB;IACnD,OAAO;QACH;QACA,cAAc,gBAAgB,QAAQ,CAAC,KAAK,IAAI;QAChD,YAAY,gBAAgB,QAAQ,CAAC,GAAG,IAAI;QAC5C,gBAAgB,gBAAgB,QAAQ,CAAC,OAAO,IAAI;IACxD;AACJ", "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './conversation';\r\nexport * from './domain';\r\nexport * from './frame';\r\nexport * from './message';\r\nexport * from './project';\r\nexport * from './user';\r\nexport * from './subscription';\r\nexport * from './setting';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2703, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/stripe/src/client.ts"], "sourcesContent": ["import { config } from 'dotenv';\r\nimport Stripe from 'stripe';\r\n\r\nconfig({ path: '../.env' });\r\n\r\nexport const createStripeClient = (secretKey?: string) => {\r\n    const apiKey = secretKey || process.env.STRIPE_SECRET_KEY;\r\n    if (!apiKey) {\r\n        throw new Error('STRIPE_SECRET_KEY is not set');\r\n    }\r\n    return new Stripe(apiKey, { apiVersion: '2025-05-28.basil' });\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,CAAA,GAAA,qIAAA,CAAA,SAAM,AAAD,EAAE;IAAE,MAAM;AAAU;AAElB,MAAM,qBAAqB,CAAC;IAC/B,MAAM,SAAS,aAAa,QAAQ,GAAG,CAAC,iBAAiB;IACzD,IAAI,CAAC,QAAQ;QACT,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,IAAI,sJAAA,CAAA,UAAM,CAAC,QAAQ;QAAE,YAAY;IAAmB;AAC/D", "debugId": null}}, {"offset": {"line": 2728, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/stripe/src/types.ts"], "sourcesContent": ["import type { PriceKey } from \"./constants\";\r\n\r\nexport enum ProductType {\r\n    FREE = 'free',\r\n    PRO = 'pro',\r\n}\r\n\r\nexport interface Product {\r\n    name: string;\r\n    type: ProductType;\r\n    stripeProductId: string;\r\n}\r\n\r\nexport interface Price {\r\n    id: string;\r\n    productId: string;\r\n    key: PriceKey;\r\n    monthlyMessageLimit: number;\r\n    stripePriceId: string;\r\n}\r\n\r\nexport interface Subscription {\r\n    id: string;\r\n    status: string;\r\n    startedAt: Date;\r\n    endedAt: Date | null;\r\n    product: Product;\r\n    price: Price;\r\n    scheduledChange: ScheduledChange | null;\r\n\r\n    // Stripe\r\n    stripeSubscriptionId: string;\r\n    stripeSubscriptionItemId: string;\r\n    stripeCustomerId: string;\r\n}\r\n\r\nexport enum ScheduledSubscriptionAction {\r\n    PRICE_CHANGE = 'price_change',\r\n    CANCELLATION = 'cancellation',\r\n}\r\n\r\nexport interface ScheduledChange {\r\n    scheduledAction: ScheduledSubscriptionAction;\r\n    scheduledChangeAt: Date;\r\n\r\n    // Only present for price changes\r\n    price: Price | null;\r\n    stripeSubscriptionScheduleId: string | null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEO,IAAA,AAAK,qCAAA;;;WAAA;;AAkCL,IAAA,AAAK,qDAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/stripe/src/constants.ts"], "sourcesContent": ["import { ProductType } from \"./types\";\r\n\r\nexport enum PriceKey {\r\n    PRO_MONTHLY_TIER_1 = 'PRO_MONTHLY_TIER_1',\r\n    PRO_MONTHLY_TIER_2 = 'PRO_MONTHLY_TIER_2',\r\n    PRO_MONTHLY_TIER_3 = 'PRO_MONTHLY_TIER_3',\r\n    PRO_MONTHLY_TIER_4 = 'PRO_MONTHLY_TIER_4',\r\n    PRO_MONTHLY_TIER_5 = 'PRO_MONTHLY_TIER_5',\r\n    PRO_MONTHLY_TIER_6 = 'PRO_MONTHLY_TIER_6',\r\n    PRO_MONTHLY_TIER_7 = 'PRO_MONTHLY_TIER_7',\r\n    PRO_MONTHLY_TIER_8 = 'PRO_MONTHLY_TIER_8',\r\n    PRO_MONTHLY_TIER_9 = 'PRO_MONTHLY_TIER_9',\r\n    PRO_MONTHLY_TIER_10 = 'PRO_MONTHLY_TIER_10',\r\n    PRO_MONTHLY_TIER_11 = 'PRO_MONTHLY_TIER_11',\r\n}\r\n\r\nexport interface PriceConfig {\r\n    key: PriceKey,\r\n    name: string,\r\n    product: ProductType,\r\n    description: string,\r\n    monthlyMessageLimit: number\r\n    cost: number\r\n    paymentInterval: 'month' | 'year'\r\n}\r\n\r\nexport const PRO_PRICES: PriceConfig[] = [\r\n    { description: '100 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_1, name: 'Tier 1', product: ProductType.PRO, monthlyMessageLimit: 100, cost: 2500, paymentInterval: 'month' },\r\n    { description: '200 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_2, name: 'Tier 2', product: ProductType.PRO, monthlyMessageLimit: 200, cost: 5000, paymentInterval: 'month' },\r\n    { description: '400 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_3, name: 'Tier 3', product: ProductType.PRO, monthlyMessageLimit: 400, cost: 10000, paymentInterval: 'month' },\r\n    { description: '800 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_4, name: 'Tier 4', product: ProductType.PRO, monthlyMessageLimit: 800, cost: 20000, paymentInterval: 'month' },\r\n    { description: '1,200 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_5, name: 'Tier 5', product: ProductType.PRO, monthlyMessageLimit: 1200, cost: 29400, paymentInterval: 'month' },\r\n    { description: '2,000 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_6, name: 'Tier 6', product: ProductType.PRO, monthlyMessageLimit: 2000, cost: 48000, paymentInterval: 'month' },\r\n    { description: '3,000 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_7, name: 'Tier 7', product: ProductType.PRO, monthlyMessageLimit: 3000, cost: 70500, paymentInterval: 'month' },\r\n    { description: '4,000 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_8, name: 'Tier 8', product: ProductType.PRO, monthlyMessageLimit: 4000, cost: 92000, paymentInterval: 'month' },\r\n    { description: '5,000 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_9, name: 'Tier 9', product: ProductType.PRO, monthlyMessageLimit: 5000, cost: 112500, paymentInterval: 'month' },\r\n    { description: '7,500 Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_10, name: 'Tier 10', product: ProductType.PRO, monthlyMessageLimit: 7500, cost: 187500, paymentInterval: 'month' },\r\n    { description: 'Unlimited Messages per Month', key: PriceKey.PRO_MONTHLY_TIER_11, name: 'Tier 11', product: ProductType.PRO, monthlyMessageLimit: 99999, cost: 375000, paymentInterval: 'month' },\r\n]\r\n\r\nexport const PRO_PRODUCT_CONFIG = {\r\n    name: 'Onlook Pro',\r\n    prices: PRO_PRICES,\r\n}\r\n\r\nexport const FREE_PRODUCT_CONFIG = {\r\n    name: 'Free',\r\n    type: ProductType.FREE,\r\n    stripeProductId: '',\r\n    dailyLimit: 10,\r\n    monthlyLimit: 50,\r\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,IAAA,AAAK,kCAAA;;;;;;;;;;;;WAAA;;AAwBL,MAAM,aAA4B;IACrC;QAAE,aAAa;QAA0B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAK,MAAM;QAAM,iBAAiB;IAAQ;IACpL;QAAE,aAAa;QAA0B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAK,MAAM;QAAM,iBAAiB;IAAQ;IACpL;QAAE,aAAa;QAA0B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAK,MAAM;QAAO,iBAAiB;IAAQ;IACrL;QAAE,aAAa;QAA0B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAK,MAAM;QAAO,iBAAiB;IAAQ;IACrL;QAAE,aAAa;QAA4B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAM,MAAM;QAAO,iBAAiB;IAAQ;IACxL;QAAE,aAAa;QAA4B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAM,MAAM;QAAO,iBAAiB;IAAQ;IACxL;QAAE,aAAa;QAA4B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAM,MAAM;QAAO,iBAAiB;IAAQ;IACxL;QAAE,aAAa;QAA4B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAM,MAAM;QAAO,iBAAiB;IAAQ;IACxL;QAAE,aAAa;QAA4B,GAAG;QAA+B,MAAM;QAAU,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAM,MAAM;QAAQ,iBAAiB;IAAQ;IACzL;QAAE,aAAa;QAA4B,GAAG;QAAgC,MAAM;QAAW,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAM,MAAM;QAAQ,iBAAiB;IAAQ;IAC3L;QAAE,aAAa;QAAgC,GAAG;QAAgC,MAAM;QAAW,SAAS,kIAAA,CAAA,cAAW,CAAC,GAAG;QAAE,qBAAqB;QAAO,MAAM;QAAQ,iBAAiB;IAAQ;CACnM;AAEM,MAAM,qBAAqB;IAC9B,MAAM;IACN,QAAQ;AACZ;AAEO,MAAM,sBAAsB;IAC/B,MAAM;IACN,MAAM,kIAAA,CAAA,cAAW,CAAC,IAAI;IACtB,iBAAiB;IACjB,YAAY;IACZ,cAAc;AAClB", "debugId": null}}, {"offset": {"line": 2888, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/stripe/src/functions.ts"], "sourcesContent": ["import Stripe from 'stripe';\r\nimport { createStripeClient } from './client';\r\n\r\nexport const createCustomer = async ({ name, email }: { name: string; email: string }) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.customers.create({ name, email });\r\n};\r\n\r\nexport const createMeterEvent = async ({\r\n    eventName,\r\n    value,\r\n    customerId,\r\n}: {\r\n    eventName: string;\r\n    value: number;\r\n    customerId: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.billing.meterEvents.create({\r\n        event_name: eventName,\r\n        payload: {\r\n            value: value.toString(),\r\n            stripe_customer_id: customerId,\r\n        },\r\n    });\r\n};\r\n\r\nexport const createPrice = async ({\r\n    currency,\r\n    amount,\r\n    meterId,\r\n    productName,\r\n}: {\r\n    currency: string;\r\n    amount: number;\r\n    meterId: string;\r\n    productName: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.prices.create({\r\n        currency,\r\n        unit_amount: amount,\r\n        recurring: {\r\n            interval: 'month',\r\n            meter: meterId,\r\n            usage_type: 'metered',\r\n        },\r\n        product_data: { name: productName },\r\n    });\r\n};\r\n\r\nexport const createSubscription = async ({\r\n    customerId,\r\n    priceId,\r\n}: {\r\n    customerId: string;\r\n    priceId: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.subscriptions.create({\r\n        customer: customerId,\r\n        items: [{ price: priceId }],\r\n        expand: ['pending_setup_intent'],\r\n    });\r\n};\r\n\r\nexport const createCheckoutSession = async ({\r\n    priceId,\r\n    userId,\r\n    successUrl,\r\n    cancelUrl,\r\n    existing,\r\n}: {\r\n    priceId: string;\r\n    userId: string;\r\n    existing?: {\r\n        subscriptionId: string;\r\n        customerId: string;\r\n    }\r\n    successUrl: string;\r\n    cancelUrl: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    let session: Stripe.Checkout.Session;\r\n    if (existing) {\r\n        session = await stripe.checkout.sessions.create({\r\n            mode: 'subscription',\r\n            customer: existing.customerId,\r\n            line_items: [{\r\n                price: priceId,\r\n                quantity: 1,\r\n            }],\r\n            payment_method_types: ['card'],\r\n            metadata: {\r\n                user_id: userId,\r\n            },\r\n            allow_promotion_codes: true,\r\n            success_url: successUrl,\r\n            cancel_url: cancelUrl,\r\n            subscription_data: {\r\n                proration_behavior: 'create_prorations',\r\n            }\r\n        });\r\n    } else {\r\n        session = await stripe.checkout.sessions.create({\r\n            mode: 'subscription',\r\n            line_items: [{\r\n                price: priceId,\r\n                quantity: 1,\r\n            }],\r\n            payment_method_types: ['card'],\r\n            metadata: {\r\n                user_id: userId,\r\n            },\r\n            allow_promotion_codes: true,\r\n            success_url: successUrl,\r\n            cancel_url: cancelUrl,\r\n        });\r\n    }\r\n    return session;\r\n};\r\n\r\nexport const createBillingPortalSession = async ({\r\n    customerId,\r\n    returnUrl,\r\n}: {\r\n    customerId: string;\r\n    returnUrl: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.billingPortal.sessions.create({\r\n        customer: customerId,\r\n        return_url: returnUrl,\r\n    });\r\n}\r\n\r\nexport const updateSubscription = async ({\r\n    subscriptionId,\r\n    subscriptionItemId,\r\n    priceId,\r\n}: {\r\n    subscriptionId: string;\r\n    subscriptionItemId: string;\r\n    priceId: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.subscriptions.update(subscriptionId, {\r\n        items: [{\r\n            id: subscriptionItemId,\r\n            price: priceId,\r\n        }],\r\n        proration_behavior: 'always_invoice'\r\n    });\r\n}\r\n\r\nexport const updateSubscriptionNextPeriod = async ({\r\n    subscriptionId,\r\n    priceId,\r\n}: {\r\n    subscriptionId: string;\r\n    priceId: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n\r\n    // Step 1: Create a subscription schedule from the current subscription\r\n    const schedule = await stripe.subscriptionSchedules.create({\r\n        from_subscription: subscriptionId,\r\n    });\r\n\r\n    const currentPhase = schedule.phases[0];\r\n    if (!currentPhase) {\r\n        throw new Error('No current phase found');\r\n    }\r\n    const currentItem = currentPhase.items[0];\r\n    if (!currentItem) {\r\n        throw new Error('No current item found');\r\n    }\r\n\r\n    const currentPrice = currentItem.price.toString();\r\n    if (!currentPrice) {\r\n        throw new Error('No current price found');\r\n    }\r\n\r\n    // Step 2: Add a new phase that updates the price starting next billing period\r\n    const updatedSchedule = await stripe.subscriptionSchedules.update(schedule.id, {\r\n        phases: [\r\n            {\r\n                items: [\r\n                    {\r\n                        price: currentPrice,\r\n                        quantity: currentItem.quantity,\r\n                    },\r\n                ],\r\n                start_date: currentPhase.start_date,\r\n                end_date: currentPhase.end_date,\r\n            },\r\n            {\r\n                items: [\r\n                    {\r\n                        price: priceId,\r\n                        quantity: 1,\r\n                    },\r\n                ],\r\n                iterations: 1,\r\n            },\r\n        ],\r\n    });\r\n\r\n    return updatedSchedule;\r\n};\r\n\r\nexport const releaseSubscriptionSchedule = async ({\r\n    subscriptionScheduleId,\r\n}: {\r\n    subscriptionScheduleId: string;\r\n}) => {\r\n    const stripe = createStripeClient();\r\n    return await stripe.subscriptionSchedules.release(subscriptionScheduleId);\r\n}"], "names": [], "mappings": ";;;;;;;;;;;AACA;;AAEO,MAAM,iBAAiB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAmC;IACjF,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,SAAS,CAAC,MAAM,CAAC;QAAE;QAAM;IAAM;AACvD;AAEO,MAAM,mBAAmB,OAAO,EACnC,SAAS,EACT,KAAK,EACL,UAAU,EAKb;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC;QAC3C,YAAY;QACZ,SAAS;YACL,OAAO,MAAM,QAAQ;YACrB,oBAAoB;QACxB;IACJ;AACJ;AAEO,MAAM,cAAc,OAAO,EAC9B,QAAQ,EACR,MAAM,EACN,OAAO,EACP,WAAW,EAMd;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC;QAC9B;QACA,aAAa;QACb,WAAW;YACP,UAAU;YACV,OAAO;YACP,YAAY;QAChB;QACA,cAAc;YAAE,MAAM;QAAY;IACtC;AACJ;AAEO,MAAM,qBAAqB,OAAO,EACrC,UAAU,EACV,OAAO,EAIV;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,aAAa,CAAC,MAAM,CAAC;QACrC,UAAU;QACV,OAAO;YAAC;gBAAE,OAAO;YAAQ;SAAE;QAC3B,QAAQ;YAAC;SAAuB;IACpC;AACJ;AAEO,MAAM,wBAAwB,OAAO,EACxC,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,QAAQ,EAUX;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,IAAI;IACJ,IAAI,UAAU;QACV,UAAU,MAAM,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;YACN,UAAU,SAAS,UAAU;YAC7B,YAAY;gBAAC;oBACT,OAAO;oBACP,UAAU;gBACd;aAAE;YACF,sBAAsB;gBAAC;aAAO;YAC9B,UAAU;gBACN,SAAS;YACb;YACA,uBAAuB;YACvB,aAAa;YACb,YAAY;YACZ,mBAAmB;gBACf,oBAAoB;YACxB;QACJ;IACJ,OAAO;QACH,UAAU,MAAM,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;YACN,YAAY;gBAAC;oBACT,OAAO;oBACP,UAAU;gBACd;aAAE;YACF,sBAAsB;gBAAC;aAAO;YAC9B,UAAU;gBACN,SAAS;YACb;YACA,uBAAuB;YACvB,aAAa;YACb,YAAY;QAChB;IACJ;IACA,OAAO;AACX;AAEO,MAAM,6BAA6B,OAAO,EAC7C,UAAU,EACV,SAAS,EAIZ;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC9C,UAAU;QACV,YAAY;IAChB;AACJ;AAEO,MAAM,qBAAqB,OAAO,EACrC,cAAc,EACd,kBAAkB,EAClB,OAAO,EAKV;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,aAAa,CAAC,MAAM,CAAC,gBAAgB;QACrD,OAAO;YAAC;gBACJ,IAAI;gBACJ,OAAO;YACX;SAAE;QACF,oBAAoB;IACxB;AACJ;AAEO,MAAM,+BAA+B,OAAO,EAC/C,cAAc,EACd,OAAO,EAIV;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAEhC,uEAAuE;IACvE,MAAM,WAAW,MAAM,OAAO,qBAAqB,CAAC,MAAM,CAAC;QACvD,mBAAmB;IACvB;IAEA,MAAM,eAAe,SAAS,MAAM,CAAC,EAAE;IACvC,IAAI,CAAC,cAAc;QACf,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,cAAc,aAAa,KAAK,CAAC,EAAE;IACzC,IAAI,CAAC,aAAa;QACd,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,eAAe,YAAY,KAAK,CAAC,QAAQ;IAC/C,IAAI,CAAC,cAAc;QACf,MAAM,IAAI,MAAM;IACpB;IAEA,8EAA8E;IAC9E,MAAM,kBAAkB,MAAM,OAAO,qBAAqB,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;QAC3E,QAAQ;YACJ;gBACI,OAAO;oBACH;wBACI,OAAO;wBACP,UAAU,YAAY,QAAQ;oBAClC;iBACH;gBACD,YAAY,aAAa,UAAU;gBACnC,UAAU,aAAa,QAAQ;YACnC;YACA;gBACI,OAAO;oBACH;wBACI,OAAO;wBACP,UAAU;oBACd;iBACH;gBACD,YAAY;YAChB;SACH;IACL;IAEA,OAAO;AACX;AAEO,MAAM,8BAA8B,OAAO,EAC9C,sBAAsB,EAGzB;IACG,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IAChC,OAAO,MAAM,OAAO,qBAAqB,CAAC,OAAO,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 3068, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/stripe/src/index.ts"], "sourcesContent": ["export * from './client';\r\nexport * from './constants';\r\nexport * from './functions';\r\nexport * from './types';\r\n\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3095, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/subscription/product.ts"], "sourcesContent": ["import { ProductType } from '@onlook/stripe';\r\nimport { pgEnum, pgTable, text, uuid } from 'drizzle-orm/pg-core';\r\n\r\nexport const productType = pgEnum('product_type', ProductType)\r\n\r\nexport const products = pgTable('products', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    name: text('name').notNull(),\r\n    type: productType('type').notNull(),\r\n\r\n    // Stripe\r\n    stripeProductId: text('stripe_product_id').notNull().unique(),\r\n}).enableRLS();\r\n\r\nexport type Product = typeof products.$inferSelect;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAEO,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB,kIAAA,CAAA,cAAW;AAEtD,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACxC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,MAAM,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,MAAM,YAAY,QAAQ,OAAO;IAEjC,SAAS;IACT,iBAAiB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO,GAAG,MAAM;AAC/D,GAAG,SAAS", "debugId": null}}, {"offset": {"line": 3121, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/subscription/price.ts"], "sourcesContent": ["import { PriceKey } from '@onlook/stripe';\r\nimport { relations } from 'drizzle-orm';\r\nimport { integer, pgEnum, pgTable, text, uuid } from 'drizzle-orm/pg-core';\r\nimport { products } from './product';\r\n\r\nexport const priceKeys = pgEnum('price_keys', PriceKey);\r\n\r\nexport const prices = pgTable('prices', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n\r\n    // Relationships\r\n    productId: uuid('product_id')\r\n        .notNull()\r\n        .references(() => products.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n\r\n    // Metadata\r\n    key: priceKeys('price_key').notNull(),\r\n    monthlyMessageLimit: integer('monthly_message_limit').notNull(),\r\n\r\n    // Stripe\r\n    stripePriceId: text('stripe_price_id').notNull().unique(),\r\n}).enableRLS();\r\n\r\nexport const priceRelations = relations(prices, ({ one }) => ({\r\n    product: one(products, {\r\n        fields: [prices.productId],\r\n        references: [products.id],\r\n    }),\r\n}));\r\n\r\nexport type Price = typeof prices.$inferSelect;\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAEO,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,cAAc,sIAAA,CAAA,WAAQ;AAE/C,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACpC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IAEzC,gBAAgB;IAChB,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,0JAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAE9E,WAAW;IACX,KAAK,UAAU,aAAa,OAAO;IACnC,qBAAqB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,yBAAyB,OAAO;IAE7D,SAAS;IACT,eAAe,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,OAAO,GAAG,MAAM;AAC3D,GAAG,SAAS;AAEL,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC1D,SAAS,IAAI,0JAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,OAAO,SAAS;aAAC;YAC1B,YAAY;gBAAC,0JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3169, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/subscription/subscription.ts"], "sourcesContent": ["import { ScheduledSubscriptionAction } from '@onlook/stripe';\r\nimport { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { users } from '../user/user';\r\nimport { prices } from './price';\r\nimport { products } from './product';\r\n\r\nexport const scheduledSubscriptionAction = pgEnum('scheduled_subscription_action', ScheduledSubscriptionAction);\r\n\r\nexport const subscriptions = pgTable('subscriptions', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n\r\n    // Relationships\r\n    userId: uuid('user_id').notNull().references(() => users.id),\r\n    productId: uuid('product_id').notNull().references(() => products.id),\r\n    priceId: uuid('price_id').notNull().references(() => prices.id),\r\n\r\n    // Metadata\r\n    startedAt: timestamp('started_at', { withTimezone: true }).notNull().defaultNow(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),\r\n    endedAt: timestamp('ended_at', { withTimezone: true }),\r\n    status: text('status', { enum: ['active', 'canceled'] }).notNull(),\r\n\r\n    // Stripe\r\n    stripeCustomerId: text('stripe_customer_id').notNull(),\r\n    stripeSubscriptionId: text('stripe_subscription_id').notNull().unique(),\r\n    stripeSubscriptionItemId: text('stripe_subscription_item_id').notNull().unique(),\r\n\r\n    // Scheduled price change\r\n    scheduledAction: scheduledSubscriptionAction('scheduled_action'),\r\n    scheduledPriceId: uuid('scheduled_price_id').references(() => prices.id),\r\n    scheduledChangeAt: timestamp('scheduled_change_at', { withTimezone: true }),\r\n    stripeSubscriptionScheduleId: text('stripe_subscription_schedule_id'),\r\n}).enableRLS();\r\n\r\nexport const subscriptionRelations = relations(subscriptions, ({ one, many }) => ({\r\n    product: one(products, {\r\n        fields: [subscriptions.productId],\r\n        references: [products.id],\r\n    }),\r\n    price: one(prices, {\r\n        fields: [subscriptions.priceId],\r\n        references: [prices.id],\r\n    }),\r\n}));\r\n\r\nexport type NewSubscription = typeof subscriptions.$inferInsert;\r\nexport type Subscription = typeof subscriptions.$inferSelect;"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,8BAA8B,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,iCAAiC,kIAAA,CAAA,8BAA2B;AAEvG,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IAClD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IAEzC,gBAAgB;IAChB,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE;IAC3D,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,GAAG,UAAU,CAAC,IAAM,0JAAA,CAAA,WAAQ,CAAC,EAAE;IACpE,SAAS,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,GAAG,UAAU,CAAC,IAAM,wJAAA,CAAA,SAAM,CAAC,EAAE;IAE9D,WAAW;IACX,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,OAAO,GAAG,UAAU;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,OAAO,GAAG,UAAU;IAC/E,SAAS,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,cAAc;IAAK;IACpD,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAAE,MAAM;YAAC;YAAU;SAAW;IAAC,GAAG,OAAO;IAEhE,SAAS;IACT,kBAAkB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,sBAAsB,OAAO;IACpD,sBAAsB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,0BAA0B,OAAO,GAAG,MAAM;IACrE,0BAA0B,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,+BAA+B,OAAO,GAAG,MAAM;IAE9E,yBAAyB;IACzB,iBAAiB,4BAA4B;IAC7C,kBAAkB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,sBAAsB,UAAU,CAAC,IAAM,wJAAA,CAAA,SAAM,CAAC,EAAE;IACvE,mBAAmB,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB;QAAE,cAAc;IAAK;IACzE,8BAA8B,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;AACvC,GAAG,SAAS;AAEL,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QAC9E,SAAS,IAAI,0JAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,cAAc,SAAS;aAAC;YACjC,YAAY;gBAAC,0JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;QACA,OAAO,IAAI,wJAAA,CAAA,SAAM,EAAE;YACf,QAAQ;gBAAC,cAAc,OAAO;aAAC;YAC/B,YAAY;gBAAC,wJAAA,CAAA,SAAM,CAAC,EAAE;aAAC;QAC3B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3250, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/subscription/usage.ts"], "sourcesContent": ["import { UsageType } from '@onlook/models';\r\nimport { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { users } from '../user';\r\n\r\nexport const usageTypes = pgEnum('usage_types', UsageType);\r\n\r\nexport const usageRecords = pgTable('usage_records', {\r\n    id: uuid('id').defaultRandom().primaryKey(),\r\n\r\n    // Relationships\r\n    userId: uuid('user_id').notNull().references(() => users.id),\r\n\r\n    // Metadata\r\n    type: usageTypes('type').default(UsageType.MESSAGE).notNull(),\r\n    timestamp: timestamp('timestamp', { withTimezone: true }).notNull(),\r\n}).enableRLS();\r\n\r\nexport const usageRelations = relations(usageRecords, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [usageRecords.userId],\r\n        references: [users.id],\r\n    })\r\n}))\r\n\r\nexport type UsageRecord = typeof usageRecords.$inferSelect;"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;;;AAEO,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,eAAe,2IAAA,CAAA,YAAS;AAElD,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IACjD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,aAAa,GAAG,UAAU;IAEzC,gBAAgB;IAChB,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE;IAE3D,WAAW;IACX,MAAM,WAAW,QAAQ,OAAO,CAAC,2IAAA,CAAA,YAAS,CAAC,OAAO,EAAE,OAAO;IAC3D,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,cAAc;IAAK,GAAG,OAAO;AACrE,GAAG,SAAS;AAEL,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAChE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3295, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/subscription/index.ts"], "sourcesContent": ["export * from './price';\r\nexport * from './product';\r\nexport * from './subscription';\r\nexport * from './usage';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/supabase/user.ts"], "sourcesContent": ["import { jsonb, pgSchema, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\n\r\nconst authSchema = pgSchema('auth');\r\n\r\nexport const authUsers = authSchema.table('users', {\r\n    id: uuid('id').primaryKey(),\r\n    email: text('email').notNull(),\r\n    emailConfirmedAt: timestamp('email_confirmed_at'),\r\n    rawUserMetaData: jsonb('raw_user_meta_data'),\r\n}).enableRLS();\r\n\r\nexport type AuthUser = typeof authUsers.$inferSelect;\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEA,MAAM,aAAa,CAAA,GAAA,sJAAA,CAAA,WAAQ,AAAD,EAAE;AAErB,MAAM,YAAY,WAAW,KAAK,CAAC,SAAS;IAC/C,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,kBAAkB,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;IAC5B,iBAAiB,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE;AAC3B,GAAG,SAAS", "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/supabase/index.ts"], "sourcesContent": ["export * from './user';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 3362, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/user-canvas.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { numeric, pgTable, primaryKey, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema, createUpdateSchema } from 'drizzle-zod';\r\nimport { canvases } from '../../schema';\r\nimport { users } from './user';\r\n\r\nexport const userCanvases = pgTable(\r\n    'user_canvases',\r\n    {\r\n        userId: uuid('user_id')\r\n            .notNull()\r\n            .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        canvasId: uuid('canvas_id')\r\n            .notNull()\r\n            .references(() => canvases.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        scale: numeric('scale').notNull(),\r\n        x: numeric('x').notNull(),\r\n        y: numeric('y').notNull(),\r\n    },\r\n    (table) => [primaryKey({ columns: [table.userId, table.canvasId] })],\r\n).enableRLS();\r\n\r\nexport const userCanvasInsertSchema = createInsertSchema(userCanvases);\r\nexport const userCanvasUpdateSchema = createUpdateSchema(userCanvases);\r\n\r\nexport type UserCanvas = typeof userCanvases.$inferSelect;\r\nexport type NewUserCanvas = typeof userCanvases.$inferInsert;\r\n\r\nexport const userCanvasesRelations = relations(userCanvases, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [userCanvases.userId],\r\n        references: [users.id],\r\n    }),\r\n    canvas: one(canvases, {\r\n        fields: [userCanvases.canvasId],\r\n        references: [canvases.id],\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAC9B,iBACA;IACI,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACR,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC3E,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,aACV,OAAO,GACP,UAAU,CAAC,IAAM,8JAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO;IAC/B,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;IACvB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;AAC3B,GACA,CAAC,QAAU;QAAC,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,QAAQ;aAAC;QAAC;KAAG,EACtE,SAAS;AAEJ,MAAM,yBAAyB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAClD,MAAM,yBAAyB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAKlD,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACvE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;QACA,QAAQ,IAAI,8JAAA,CAAA,WAAQ,EAAE;YAClB,QAAQ;gBAAC,aAAa,QAAQ;aAAC;YAC/B,YAAY;gBAAC,8JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3428, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/user-project.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, primaryKey, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { projects } from '../project';\r\nimport { users } from './user';\r\nimport { ProjectRole } from '@onlook/models';\r\n\r\nexport const projectRole = pgEnum('project_role', ProjectRole);\r\n\r\nexport const userProjects = pgTable(\r\n    'user_projects',\r\n    {\r\n        userId: uuid('user_id')\r\n            .notNull()\r\n            .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        projectId: uuid('project_id')\r\n            .notNull()\r\n            .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),\r\n        role: projectRole('role').notNull(),\r\n    },\r\n    (table) => [primaryKey({ columns: [table.userId, table.projectId] })],\r\n).enableRLS();\r\n\r\nexport const userProjectsRelations = relations(userProjects, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [userProjects.userId],\r\n        references: [users.id],\r\n    }),\r\n    project: one(projects, {\r\n        fields: [userProjects.projectId],\r\n        references: [projects.id],\r\n    }),\r\n}));\r\n\r\nexport const userProjectInsertSchema = createInsertSchema(userProjects);\r\nexport type UserProject = typeof userProjects.$inferSelect;\r\nexport type NewUserProject = typeof userProjects.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;;;;;;;AAEO,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB,4IAAA,CAAA,cAAW;AAEtD,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAC9B,iBACA;IACI,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACR,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC3E,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU;IACrE,MAAM,YAAY,QAAQ,OAAO;AACrC,GACA,CAAC,QAAU;QAAC,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,SAAS;aAAC;QAAC;KAAG,EACvE,SAAS;AAEJ,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACvE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;QACA,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,aAAa,SAAS;aAAC;YAChC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC;AAEM,MAAM,0BAA0B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3499, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/user.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { usageRecords } from '../subscription';\r\nimport { subscriptions } from '../subscription/subscription';\r\nimport { authUsers } from '../supabase';\r\nimport { userSettings } from './settings';\r\nimport { userCanvases } from './user-canvas';\r\nimport { userProjects } from './user-project';\r\n\r\nexport const users = pgTable('users', {\r\n    id: uuid('id')\r\n        .primaryKey()\r\n        .references(() => authUsers.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n}).enableRLS();\r\n\r\nexport const usersRelations = relations(users, ({ many, one }) => ({\r\n    userCanvases: many(userCanvases),\r\n    userProjects: many(userProjects),\r\n    userSettings: one(userSettings),\r\n    authUser: one(authUsers),\r\n    subscriptions: many(subscriptions),\r\n    usageRecords: many(usageRecords),\r\n}));\r\n\r\nexport const userInsertSchema = createInsertSchema(users);\r\nexport type User = typeof users.$inferSelect;\r\nexport type NewUser = typeof users.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IAClC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MACJ,UAAU,GACV,UAAU,CAAC,IAAM,mJAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;AACnF,GAAG,SAAS;AAEL,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/D,cAAc,KAAK,yJAAA,CAAA,eAAY;QAC/B,cAAc,KAAK,0JAAA,CAAA,eAAY;QAC/B,cAAc,IAAI,mJAAA,CAAA,eAAY;QAC9B,UAAU,IAAI,mJAAA,CAAA,YAAS;QACvB,eAAe,KAAK,+JAAA,CAAA,gBAAa;QACjC,cAAc,KAAK,wJAAA,CAAA,eAAY;IACnC,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/settings.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { boolean, pgTable, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema, createUpdateSchema } from 'drizzle-zod';\r\nimport { users } from './user';\r\n\r\nexport const userSettings = pgTable(\"user_settings\", {\r\n    id: uuid(\"id\")\r\n        .primaryKey(),\r\n    userId: uuid(\"user_id\")\r\n        .notNull()\r\n        .references(() => users.id, { onDelete: \"cascade\", onUpdate: \"cascade\" })\r\n        .unique(),\r\n    autoApplyCode: boolean(\"auto_apply_code\").notNull().default(true),\r\n    expandCodeBlocks: boolean(\"expand_code_blocks\").notNull().default(true),\r\n    showSuggestions: boolean(\"show_suggestions\").notNull().default(true),\r\n    showMiniChat: boolean(\"show_mini_chat\").notNull().default(true),\r\n    shouldWarnDelete: boolean(\"should_warn_delete\").notNull().default(true),\r\n}).enableRLS();\r\n\r\nexport const userSettingsRelations = relations(userSettings, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [userSettings.userId],\r\n        references: [users.id],\r\n    }),\r\n}));\r\n\r\nexport const userSettingsInsertSchema = createInsertSchema(userSettings);\r\nexport const userSettingsUpdateSchema = createUpdateSchema(userSettings);\r\nexport type UserSettings = typeof userSettings.$inferSelect;\r\nexport type NewUserSettings = typeof userSettings.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IACjD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MACJ,UAAU;IACf,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACR,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU,GACtE,MAAM;IACX,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,OAAO,GAAG,OAAO,CAAC;IAC5D,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,GAAG,OAAO,CAAC;IAClE,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB,OAAO,GAAG,OAAO,CAAC;IAC/D,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,GAAG,OAAO,CAAC;IAC1D,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,GAAG,OAAO,CAAC;AACtE,GAAG,SAAS;AAEL,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACvE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;IACJ,CAAC;AAEM,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AACpD,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3592, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/index.ts"], "sourcesContent": ["export * from './settings';\r\nexport * from './user';\r\nexport * from './user-canvas';\r\nexport * from './user-project';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3619, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/chat/message.ts"], "sourcesContent": ["import { ChatMessageRole, type ChatMessageContext, type ChatSnapshot } from \"@onlook/models\";\r\nimport type { Message as AiMessage } from \"ai\";\r\nimport { relations } from \"drizzle-orm\";\r\nimport { boolean, jsonb, pgEnum, pgTable, text, timestamp, uuid } from \"drizzle-orm/pg-core\";\r\nimport { createInsertSchema } from \"drizzle-zod\";\r\nimport { conversations } from \"./conversation\";\r\n\r\nexport const CONVERSATION_MESSAGe_RELATION_NAME = 'conversation_messages';\r\nexport const messageRole = pgEnum(\"role\", ChatMessageRole);\r\n\r\nexport const messages = pgTable(\"messages\", {\r\n    id: uuid(\"id\").primaryKey().defaultRandom(),\r\n    conversationId: uuid(\"conversation_id\")\r\n        .notNull()\r\n        .references(() => conversations.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    content: text(\"content\").notNull(),\r\n    createdAt: timestamp(\"created_at\", { withTimezone: true }).defaultNow().notNull(),\r\n    role: messageRole(\"role\").notNull(),\r\n    applied: boolean(\"applied\").default(false).notNull(),\r\n    snapshots: jsonb(\"snapshots\").$type<ChatSnapshot>().default({}).notNull(),\r\n    context: jsonb(\"context\").$type<ChatMessageContext[]>().default([]).notNull(),\r\n    parts: jsonb(\"parts\").$type<AiMessage['parts']>().default([]).notNull(),\r\n}).enableRLS();\r\n\r\nexport const messageInsertSchema = createInsertSchema(messages);\r\n\r\nexport const messageRelations = relations(messages, ({ one }) => ({\r\n    conversation: one(conversations, {\r\n        fields: [messages.conversationId],\r\n        references: [conversations.id],\r\n        relationName: CONVERSATION_MESSAGe_RELATION_NAME,\r\n    }),\r\n}));\r\n\r\nexport type Message = typeof messages.$inferSelect;\r\nexport type NewMessage = typeof messages.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEO,MAAM,qCAAqC;AAC3C,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,uJAAA,CAAA,kBAAe;AAElD,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACxC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,gBAAgB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,mBAChB,OAAO,GACP,UAAU,CAAC,IAAM,kKAAA,CAAA,gBAAa,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IACnF,SAAS,CAAA,GAAA,+JAA<PERSON>,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,MAAM,YAAY,QAAQ,OAAO;IACjC,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC,OAAO,OAAO;IAClD,WAAW,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE,aAAa,KAAK,GAAiB,OAAO,CAAC,CAAC,GAAG,OAAO;IACvE,SAAS,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,KAAK,GAAyB,OAAO,CAAC,EAAE,EAAE,OAAO;IAC3E,OAAO,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE,SAAS,KAAK,GAAuB,OAAO,CAAC,EAAE,EAAE,OAAO;AACzE,GAAG,SAAS;AAEL,MAAM,sBAAsB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAE/C,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC9D,cAAc,IAAI,kKAAA,CAAA,gBAAa,EAAE;YAC7B,QAAQ;gBAAC,SAAS,cAAc;aAAC;YACjC,YAAY;gBAAC,kKAAA,CAAA,gBAAa,CAAC,EAAE;aAAC;YAC9B,cAAc;QAClB;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3679, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/chat/conversation.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\r\nimport { pgTable, timestamp, uuid, varchar } from \"drizzle-orm/pg-core\";\r\nimport { createInsertSchema } from \"drizzle-zod\";\r\nimport { projects } from \"../project\";\r\nimport { CONVERSATION_MESSAGe_RELATION_NAME, messages } from \"./message\";\r\n\r\nexport const PROJECT_CONVERSATION_RELATION_NAME = \"project_conversations\";\r\n\r\nexport const conversations = pgTable(\"conversations\", {\r\n    id: uuid(\"id\").primaryKey().defaultRandom(),\r\n    projectId: uuid(\"project_id\")\r\n        .notNull()\r\n        .references(() => projects.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    displayName: varchar(\"display_name\"),\r\n    createdAt: timestamp(\"created_at\", { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp(\"updated_at\", { withTimezone: true }).defaultNow().notNull(),\r\n}).enableRLS();\r\n\r\nexport const conversationInsertSchema = createInsertSchema(conversations);\r\n\r\nexport const conversationRelations = relations(conversations, ({ one, many }) => ({\r\n    project: one(projects, {\r\n        fields: [conversations.projectId],\r\n        references: [projects.id],\r\n        relationName: PROJECT_CONVERSATION_RELATION_NAME,\r\n    }),\r\n    messages: many(messages, {\r\n        relationName: CONVERSATION_MESSAGe_RELATION_NAME,\r\n    }),\r\n}));\r\n\r\nexport type Conversation = typeof conversations.$inferSelect;\r\nexport type NewConversation = typeof conversations.$inferInsert;"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAEO,MAAM,qCAAqC;AAE3C,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IAClD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACrB,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;AACnF,GAAG,SAAS;AAEL,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAEpD,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QAC9E,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,cAAc,SAAS;aAAC;YACjC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;YACzB,cAAc;QAClB;QACA,UAAU,KAAK,6JAAA,CAAA,WAAQ,EAAE;YACrB,cAAc,6JAAA,CAAA,qCAAkC;QACpD;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3734, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/domain/published.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { projects } from '../project';\r\nimport { customDomains } from './custom';\r\n\r\nexport const PUBLISHED_DOMAIN_PROJECT_RELATION_NAME = 'published_domain_project';\r\n\r\nexport const publishedDomains = pgTable('published_domains', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    domainId: uuid('domain_id').references(() => customDomains.id).unique(),\r\n    projectId: uuid('project_id').references(() => projects.id),\r\n    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n    fullDomain: text('full_domain').notNull().unique(),\r\n}).enableRLS();\r\n\r\nexport const publishedDomainRelations = relations(publishedDomains, ({ one }) => ({\r\n    customDomain: one(customDomains, {\r\n        fields: [publishedDomains.domainId],\r\n        references: [customDomains.id],\r\n    }),\r\n    project: one(projects, {\r\n        fields: [publishedDomains.projectId],\r\n        references: [projects.id],\r\n        relationName: PUBLISHED_DOMAIN_PROJECT_RELATION_NAME,\r\n    }),\r\n}));\r\n\r\nexport type PublishedDomain = typeof publishedDomains.$inferSelect;"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAEO,MAAM,yCAAyC;AAE/C,MAAM,mBAAmB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IACzD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,8JAAA,CAAA,gBAAa,CAAC,EAAE,EAAE,MAAM;IACrE,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE;IAC1D,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,GAAG,MAAM;AACpD,GAAG,SAAS;AAEL,MAAM,2BAA2B,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC9E,cAAc,IAAI,8JAAA,CAAA,gBAAa,EAAE;YAC7B,QAAQ;gBAAC,iBAAiB,QAAQ;aAAC;YACnC,YAAY;gBAAC,8JAAA,CAAA,gBAAa,CAAC,EAAE;aAAC;QAClC;QACA,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,iBAAiB,SAAS;aAAC;YACpC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;YACzB,cAAc;QAClB;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3788, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/domain/verification.ts"], "sourcesContent": ["import { VerificationRequestStatus } from '@onlook/models';\r\nimport { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { projects } from '../project';\r\nimport { customDomains } from './custom';\r\n\r\nexport const verificationRequestStatus = pgEnum('verification_request_status', VerificationRequestStatus);\r\n\r\nexport const customDomainVerification = pgTable('custom_domain_verification', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    domainId: uuid('domain_id').references(() => customDomains.id).notNull(),\r\n    projectId: uuid('project_id').references(() => projects.id).notNull(),\r\n    verificationId: text('verification_id').notNull(),\r\n    verificationCode: text('verification_code').notNull(),\r\n    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n    status: verificationRequestStatus('status').default(VerificationRequestStatus.ACTIVE).notNull(),\r\n}).enableRLS();\r\n\r\nexport const customDomainVerificationRelations = relations(customDomainVerification, ({ one }) => ({\r\n    customDomain: one(customDomains, {\r\n        fields: [customDomainVerification.domainId],\r\n        references: [customDomains.id],\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEO,MAAM,4BAA4B,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,+BAA+B,4IAAA,CAAA,4BAAyB;AAEjG,MAAM,2BAA2B,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,8BAA8B;IAC1E,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,8JAAA,CAAA,gBAAa,CAAC,EAAE,EAAE,OAAO;IACtE,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,OAAO;IACnE,gBAAgB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,OAAO;IAC/C,kBAAkB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO;IACnD,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,QAAQ,0BAA0B,UAAU,OAAO,CAAC,4IAAA,CAAA,4BAAyB,CAAC,MAAM,EAAE,OAAO;AACjG,GAAG,SAAS;AAEL,MAAM,oCAAoC,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,0BAA0B,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/F,cAAc,IAAI,8JAAA,CAAA,gBAAa,EAAE;YAC7B,QAAQ;gBAAC,yBAAyB,QAAQ;aAAC;YAC3C,YAAY;gBAAC,8JAAA,CAAA,gBAAa,CAAC,EAAE;aAAC;QAClC;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3839, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/domain/custom.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { publishedDomains } from './published';\r\nimport { customDomainVerification } from './verification';\r\n\r\nexport const customDomains = pgTable('custom_domains', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    apexDomain: text('apex_domain').notNull().unique(),\r\n    verified: boolean('verified').default(false).notNull(),\r\n    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n}).enableRLS();\r\n\r\nexport const customDomainRelations = relations(customDomains, ({ many }) => ({\r\n    publishedDomains: many(publishedDomains),\r\n    verificationRequests: many(customDomainVerification),\r\n}));\r\n\r\nexport type CustomDomain = typeof customDomains.$inferSelect;"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IACnD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,GAAG,MAAM;IAChD,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC,OAAO,OAAO;IACpD,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;AACnF,GAAG,SAAS;AAEL,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACzE,kBAAkB,KAAK,iKAAA,CAAA,mBAAgB;QACvC,sBAAsB,KAAK,oKAAA,CAAA,2BAAwB;IACvD,CAAC", "debugId": null}}, {"offset": {"line": 3876, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/domain/preview.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { projects } from '../project';\r\n\r\nexport const PREVIEW_DOMAIN_PROJECT_RELATION_NAME = 'preview_domain_project';\r\n\r\nexport const previewDomains = pgTable('preview_domains', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    fullDomain: text('full_domain').notNull().unique(),\r\n    projectId: uuid('project_id').references(() => projects.id),\r\n    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n}).enableRLS();\r\n\r\nexport const previewDomainRelations = relations(previewDomains, ({ one }) => ({\r\n    project: one(projects, {\r\n        fields: [previewDomains.projectId],\r\n        references: [projects.id],\r\n        relationName: PREVIEW_DOMAIN_PROJECT_RELATION_NAME,\r\n    }),\r\n}));\r\n\r\nexport type PreviewDomain = typeof previewDomains.$inferSelect;"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;AAEO,MAAM,uCAAuC;AAE7C,MAAM,iBAAiB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB;IACrD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,GAAG,MAAM;IAChD,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE;IAC1D,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;AACnF,GAAG,SAAS;AAEL,MAAM,yBAAyB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC1E,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,eAAe,SAAS;aAAC;YAClC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;YACzB,cAAc;QAClB;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 3919, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/domain/index.ts"], "sourcesContent": ["export * from './custom';\r\nexport * from './preview';\r\nexport * from './published';\r\nexport * from './verification';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3946, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/invitation.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, text, timestamp, uuid, varchar, unique } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema, createUpdateSchema } from 'drizzle-zod';\r\nimport { projectRole, users } from '../user';\r\nimport { projects } from './project';\r\n\r\nexport const projectInvitations = pgTable(\r\n    'project_invitations',\r\n    {\r\n        id: uuid('id').primaryKey().defaultRandom(),\r\n        projectId: uuid('project_id')\r\n            .notNull()\r\n            .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        inviterId: uuid('inviter_id')\r\n            .notNull()\r\n            .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        inviteeEmail: varchar('invitee_email').notNull(),\r\n        token: varchar('token').notNull().unique(),\r\n        role: projectRole('role').notNull(),\r\n        expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),\r\n        createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n        updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n    },\r\n    (table) => ({\r\n        uniqueEmailProject: unique().on(table.inviteeEmail, table.projectId),\r\n    }),\r\n).enableRLS();\r\n\r\nexport const projectInvitationInsertSchema = createInsertSchema(projectInvitations);\r\nexport const projectInvitationUpdateSchema = createUpdateSchema(projectInvitations);\r\n\r\nexport type ProjectInvitation = typeof projectInvitations.$inferSelect;\r\nexport type NewProjectInvitation = typeof projectInvitations.$inferInsert;\r\n\r\nexport const projectInvitationRelations = relations(projectInvitations, ({ one }) => ({\r\n    project: one(projects, {\r\n        fields: [projectInvitations.projectId],\r\n        references: [projects.id],\r\n    }),\r\n    inviter: one(users, {\r\n        fields: [projectInvitations.inviterId],\r\n        references: [users.id],\r\n        relationName: 'inviter',\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EACpC,uBACA;IACI,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC3E,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO;IAC9C,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACxC,MAAM,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,OAAO;IACjC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,OAAO;IAClE,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;AACnF,GACA,CAAC,QAAU,CAAC;QACR,oBAAoB,CAAA,GAAA,oKAAA,CAAA,SAAM,AAAD,IAAI,EAAE,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS;IACvE,CAAC,GACH,SAAS;AAEJ,MAAM,gCAAgC,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AACzD,MAAM,gCAAgC,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAKzD,MAAM,6BAA6B,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAClF,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,mBAAmB,SAAS;aAAC;YACtC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;QACA,SAAS,IAAI,+IAAA,CAAA,QAAK,EAAE;YAChB,QAAQ;gBAAC,mBAAmB,SAAS;aAAC;YACtC,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;YACtB,cAAc;QAClB;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 4020, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/settings.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { jsonb, pgTable, text, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema, createUpdateSchema } from 'drizzle-zod';\r\nimport { projects } from './project';\r\n\r\nexport const projectSettings = pgTable('project_settings', {\r\n    projectId: uuid('project_id')\r\n        .notNull()\r\n        .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' })\r\n        .unique(),\r\n    runCommand: text('run_command').notNull().default(''),\r\n    buildCommand: text('build_command').notNull().default(''),\r\n    installCommand: text('install_command').notNull().default(''),\r\n}).enableRLS();\r\n\r\nexport const projectSettingsInsertSchema = createInsertSchema(projectSettings);\r\nexport const projectSettingsUpdateSchema = createUpdateSchema(projectSettings);\r\n\r\nexport const projectSettingsRelations = relations(projectSettings, ({ one }) => ({\r\n    project: one(projects, {\r\n        fields: [projectSettings.projectId],\r\n        references: [projects.id],\r\n    }),\r\n}));\r\n\r\nexport type ProjectSettings = typeof projectSettings.$inferSelect;\r\nexport type NewProjectSettings = typeof projectSettings.$inferInsert; "], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACvD,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU,GACzE,MAAM;IACX,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,GAAG,OAAO,CAAC;IAClD,cAAc,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,OAAO,GAAG,OAAO,CAAC;IACtD,gBAAgB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,OAAO,GAAG,OAAO,CAAC;AAC9D,GAAG,SAAS;AAEL,MAAM,8BAA8B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AACvD,MAAM,8BAA8B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAEvD,MAAM,2BAA2B,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC7E,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,gBAAgB,SAAS;aAAC;YACnC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 4063, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/project.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { userProjects } from '../user';\r\nimport { canvases } from './canvas';\r\nimport { conversations, PROJECT_CONVERSATION_RELATION_NAME } from './chat/conversation';\r\nimport { PUBLISHED_DOMAIN_PROJECT_RELATION_NAME, publishedDomains } from './domain';\r\nimport { projectInvitations } from './invitation';\r\nimport { PREVIEW_DOMAIN_PROJECT_RELATION_NAME, previewDomains } from './domain/preview';\r\nimport { projectSettings} from './settings';\r\n\r\nexport const projects = pgTable('projects', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n\r\n    // metadata\r\n    name: varchar('name').notNull(),\r\n    description: text('description'),\r\n    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n\r\n    // preview image\r\n    previewImgUrl: varchar('preview_img_url'),\r\n    previewImgPath: varchar('preview_img_path'),\r\n    previewImgBucket: varchar('preview_img_bucket'),\r\n\r\n    // sandbox\r\n    sandboxId: varchar('sandbox_id').notNull(),\r\n    sandboxUrl: varchar('sandbox_url').notNull(),\r\n}).enableRLS();\r\n\r\nexport const projectInsertSchema = createInsertSchema(projects);\r\n\r\nexport const projectRelations = relations(projects, ({ one, many }) => ({\r\n    canvas: one(canvases, {\r\n        fields: [projects.id],\r\n        references: [canvases.projectId],\r\n    }),\r\n    userProjects: many(userProjects),\r\n    conversations: many(conversations, {\r\n        relationName: PROJECT_CONVERSATION_RELATION_NAME,\r\n    }),\r\n    projectInvitations: many(projectInvitations),\r\n    publishedDomains: many(publishedDomains, {\r\n        relationName: PUBLISHED_DOMAIN_PROJECT_RELATION_NAME,\r\n    }),\r\n    previewDomains: many(previewDomains, {\r\n        relationName: PREVIEW_DOMAIN_PROJECT_RELATION_NAME,\r\n    }),\r\n    settings: one(projectSettings, {\r\n        fields: [projects.id],\r\n        references: [projectSettings.projectId],\r\n    }),\r\n}));\r\n\r\nexport type Project = typeof projects.$inferSelect;\r\nexport type NewProject = typeof projects.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACxC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IAEzC,WAAW;IACX,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,aAAa,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IAClB,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAE/E,gBAAgB;IAChB,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACvB,gBAAgB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACxB,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IAE1B,UAAU;IACV,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO;IACxC,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO;AAC9C,GAAG,SAAS;AAEL,MAAM,sBAAsB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAE/C,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACpE,QAAQ,IAAI,8JAAA,CAAA,WAAQ,EAAE;YAClB,QAAQ;gBAAC,SAAS,EAAE;aAAC;YACrB,YAAY;gBAAC,8JAAA,CAAA,WAAQ,CAAC,SAAS;aAAC;QACpC;QACA,cAAc,KAAK,0JAAA,CAAA,eAAY;QAC/B,eAAe,KAAK,kKAAA,CAAA,gBAAa,EAAE;YAC/B,cAAc,kKAAA,CAAA,qCAAkC;QACpD;QACA,oBAAoB,KAAK,wJAAA,CAAA,qBAAkB;QAC3C,kBAAkB,KAAK,iKAAA,CAAA,mBAAgB,EAAE;YACrC,cAAc,iKAAA,CAAA,yCAAsC;QACxD;QACA,gBAAgB,KAAK,+JAAA,CAAA,iBAAc,EAAE;YACjC,cAAc,+JAAA,CAAA,uCAAoC;QACtD;QACA,UAAU,IAAI,sJAAA,CAAA,kBAAe,EAAE;YAC3B,QAAQ;gBAAC,SAAS,EAAE;aAAC;YACrB,YAAY;gBAAC,sJAAA,CAAA,kBAAe,CAAC,SAAS;aAAC;QAC3C;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 4150, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/canvas/frame.ts"], "sourcesContent": ["import { FrameType } from \"@onlook/models\";\r\nimport { relations } from \"drizzle-orm\";\r\nimport { numeric, pgEnum, pgTable, uuid, varchar } from \"drizzle-orm/pg-core\";\r\nimport { canvases } from \"./canvas\";\r\nimport { createInsertSchema, createUpdateSchema } from \"drizzle-zod\";\r\n\r\nexport const frameType = pgEnum(\"frame_type\", FrameType);\r\n\r\nexport const frames = pgTable(\"frames\", {\r\n    id: uuid(\"id\").primaryKey().defaultRandom(),\r\n    canvasId: uuid(\"canvas_id\")\r\n        .notNull()\r\n        .references(() => canvases.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    type: frameType(\"type\").notNull(),\r\n    url: varchar(\"url\").notNull(),\r\n\r\n    x: numeric(\"x\").notNull(),\r\n    y: numeric(\"y\").notNull(),\r\n\r\n    width: numeric(\"width\").notNull(),\r\n    height: numeric(\"height\").notNull(),\r\n}).enableRLS();\r\n\r\nexport const frameInsertSchema = createInsertSchema(frames);\r\nexport const frameUpdateSchema = createUpdateSchema(frames);\r\n\r\nexport type Frame = typeof frames.$inferSelect;\r\nexport type NewFrame = typeof frames.$inferInsert;\r\n\r\nexport const frameRelations = relations(frames, ({ one }) => ({\r\n    canvas: one(canvases, {\r\n        fields: [frames.canvasId],\r\n        references: [canvases.id],\r\n    }),\r\n}));"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEO,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,cAAc,6IAAA,CAAA,YAAS;AAEhD,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACpC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,aACV,OAAO,GACP,UAAU,CAAC,IAAM,8JAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,MAAM,UAAU,QAAQ,OAAO;IAC/B,KAAK,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;IAE3B,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;IACvB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;IAEvB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO;IAC/B,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;AACrC,GAAG,SAAS;AAEL,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAC7C,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAK7C,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC1D,QAAQ,IAAI,8JAAA,CAAA,WAAQ,EAAE;YAClB,QAAQ;gBAAC,OAAO,QAAQ;aAAC;YACzB,YAAY;gBAAC,8JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 4204, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/canvas/canvas.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, uuid } from 'drizzle-orm/pg-core';\r\nimport { projects } from '../project';\r\nimport { frames } from './frame';\r\nimport { createUpdateSchema } from 'drizzle-zod';\r\nimport { userCanvases } from '../../user';\r\n\r\nexport const canvases = pgTable('canvas', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    projectId: uuid('project_id')\r\n        .notNull()\r\n        .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n}).enableRLS();\r\n\r\nexport const canvasUpdateSchema = createUpdateSchema(canvases);\r\n\r\nexport type Canvas = typeof canvases.$inferSelect;\r\nexport type NewCanvas = typeof canvases.$inferInsert;\r\n\r\nexport const canvasRelations = relations(canvases, ({ one, many }) => ({\r\n    frames: many(frames),\r\n    userCanvases: many(userCanvases),\r\n    project: one(projects, {\r\n        fields: [canvases.projectId],\r\n        references: [projects.id],\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACtC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;AAClF,GAAG,SAAS;AAEL,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAK9C,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACnE,QAAQ,KAAK,6JAAA,CAAA,SAAM;QACnB,cAAc,KAAK,yJAAA,CAAA,eAAY;QAC/B,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,SAAS,SAAS;aAAC;YAC5B,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 4249, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/canvas/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './frame';\r\n\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 4270, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/chat/index.ts"], "sourcesContent": ["export * from './conversation';\r\nexport * from './message';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 4291, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './chat';\r\nexport * from './domain';\r\nexport * from './invitation';\r\nexport * from './project';\r\nexport * from './settings';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4324, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/index.ts"], "sourcesContent": ["export * from './project';\r\nexport * from './subscription';\r\nexport * from './supabase';\r\nexport * from './user';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4351, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/seed/constants.ts"], "sourcesContent": ["export const SEED_USER = {\r\n    EMAIL: '<EMAIL>',\r\n    PASSWORD: 'test',\r\n    ID: '2585ea6b-6303-4f21-977c-62af2f5a21f5',\r\n} "], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACrB,OAAO;IACP,UAAU;IACV,IAAI;AACR", "debugId": null}}, {"offset": {"line": 4365, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/index.ts"], "sourcesContent": ["export * from './defaults';\r\nexport * from './dto';\r\nexport * from './schema';\r\nexport * from './seed/constants';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4392, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/user/settings.ts"], "sourcesContent": ["import { api } from '@/trpc/client';\r\nimport { fromUserSettings, toUserSettings, createDefaultUserSettings } from '@onlook/db';\r\nimport type { ChatSettings, EditorSettings, UserSettings } from '@onlook/models';\r\nimport { makeAutoObservable, reaction } from 'mobx';\r\nimport type { UserManager } from './manager';\r\n\r\nexport class UserSettingsManager {\r\n    settings: UserSettings = toUserSettings(createDefaultUserSettings(''));\r\n\r\n    constructor(private userManager: UserManager) {\r\n        makeAutoObservable(this);\r\n\r\n        this.restoreSettings();\r\n        reaction(\r\n            () => this.userManager.user,\r\n            () => {\r\n                this.restoreSettings();\r\n            }\r\n        );\r\n    }\r\n\r\n    async restoreSettings() {\r\n        const user = this.userManager.user;\r\n        if (!user) {\r\n            console.warn('Cannot restore settings: No user found');\r\n            return;\r\n        }\r\n        const settings = await api.user.settings.get.query({ userId: user.id });\r\n        this.settings = settings;\r\n    }\r\n\r\n    async update(newSettings: Partial<UserSettings>) {\r\n        const user = this.userManager.user;\r\n        if (!user) {\r\n            console.error('Cannot update settings: No user found');\r\n            return;\r\n        }\r\n\r\n        this.settings = {\r\n            ...this.settings,\r\n            ...newSettings,\r\n        };\r\n\r\n        await api.user.settings.upsert.mutate({\r\n            userId: user.id,\r\n            settings: fromUserSettings(user.id, this.settings),\r\n        });\r\n    }\r\n\r\n    async updateChat(newSettings: Partial<ChatSettings>) {\r\n        await this.update({ ...this.settings, chat: { ...this.settings.chat, ...newSettings } });\r\n    }\r\n\r\n    async updateEditor(newSettings: Partial<EditorSettings>) {\r\n        await this.update({ ...this.settings, editor: { ...this.settings.editor, ...newSettings } });\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AAEA;;;;AAGO,MAAM;;IACT,SAAuE;IAEvE,YAAY,AAAQ,WAAwB,CAAE;aAA1B,cAAA;aAFpB,WAAyB,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,qJAAA,CAAA,4BAAyB,AAAD,EAAE;QAG9D,CAAA,GAAA,2IAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI;QAEvB,IAAI,CAAC,eAAe;QACpB,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EACH,IAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAC3B;YACI,IAAI,CAAC,eAAe;QACxB;IAER;IAEA,MAAM,kBAAkB;QACpB,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;QAClC,IAAI,CAAC,MAAM;YACP,QAAQ,IAAI,CAAC;YACb;QACJ;QACA,MAAM,WAAW,MAAM,8IAAA,CAAA,MAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;YAAE,QAAQ,KAAK,EAAE;QAAC;QACrE,IAAI,CAAC,QAAQ,GAAG;IACpB;IAEA,MAAM,OAAO,WAAkC,EAAE;QAC7C,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;QAClC,IAAI,CAAC,MAAM;YACP,QAAQ,KAAK,CAAC;YACd;QACJ;QAEA,IAAI,CAAC,QAAQ,GAAG;YACZ,GAAG,IAAI,CAAC,QAAQ;YAChB,GAAG,WAAW;QAClB;QAEA,MAAM,8IAAA,CAAA,MAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YAClC,QAAQ,KAAK,EAAE;YACf,UAAU,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ;QACrD;IACJ;IAEA,MAAM,WAAW,WAAkC,EAAE;QACjD,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,MAAM;gBAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI;gBAAE,GAAG,WAAW;YAAC;QAAE;IAC1F;IAEA,MAAM,aAAa,WAAoC,EAAE;QACrD,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,QAAQ;gBAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAAE,GAAG,WAAW;YAAC;QAAE;IAC9F;AACJ", "debugId": null}}, {"offset": {"line": 4466, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/user/subscription.ts"], "sourcesContent": ["import { type Usage } from '@onlook/models';\r\nimport { makeAutoObservable } from 'mobx';\r\nimport type { UserManager } from './manager';\r\n\r\ninterface UsageMetrics {\r\n    daily: Usage;\r\n    monthly: Usage;\r\n}\r\n\r\nexport class SubscriptionManager {\r\n    isModalOpen = false;\r\n\r\n    constructor(private userManager: UserManager) {\r\n        makeAutoObservable(this);\r\n    }\r\n\r\n    clear() {\r\n        this.isModalOpen = false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AAQO,MAAM;;IACT,YAAoB;IAEpB,YAAY,AAAQ,WAAwB,CAAE;aAA1B,cAAA;aAFpB,cAAc;QAGV,CAAA,GAAA,2IAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI;IAC3B;IAEA,QAAQ;QACJ,IAAI,CAAC,WAAW,GAAG;IACvB;AACJ", "debugId": null}}, {"offset": {"line": 4489, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/user/manager.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\nimport type { UserMetadata } from '@onlook/models';\r\nimport type { User as AuthUser } from '@supabase/supabase-js';\r\nimport { makeAutoObservable } from 'mobx';\r\nimport { LanguageManager } from './language';\r\nimport { UserSettingsManager } from './settings';\r\nimport { SubscriptionManager } from './subscription';\r\n\r\nexport class UserManager {\r\n    readonly supabase = createClient();\r\n    readonly settings: UserSettingsManager;\r\n    readonly subscription: SubscriptionManager;\r\n    readonly language = new LanguageManager();\r\n    user: UserMetadata | null = null;\r\n\r\n    constructor() {\r\n        makeAutoObservable(this);\r\n        this.settings = new UserSettingsManager(this);\r\n        this.subscription = new SubscriptionManager(this);\r\n        this.fetchUser();\r\n    }\r\n\r\n    async fetchUser() {\r\n        const { data, error } = await this.supabase.auth.getUser();\r\n        if (error) {\r\n            console.error(error);\r\n            return;\r\n        }\r\n        this.user = this.fromAuthUser(data.user);\r\n    }\r\n\r\n    fromAuthUser(authUser: AuthUser): UserMetadata {\r\n        return {\r\n            id: authUser.id,\r\n            name:\r\n                authUser.user_metadata?.full_name ||\r\n                authUser.user_metadata?.name ||\r\n                authUser.email,\r\n            email: authUser.email,\r\n            avatarUrl: authUser.user_metadata?.avatar_url,\r\n        };\r\n    }\r\n\r\n    async signOut() {\r\n        await this.supabase.auth.signOut();\r\n        this.user = null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAGA;AACA;AACA;AACA;;;;;;AAEO,MAAM;IACA,WAAW,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,IAAI;IAC1B,SAA8B;IAC9B,aAAkC;IAClC,WAAW,IAAI,uKAAA,CAAA,kBAAe,GAAG;IAC1C,OAA4B,KAAK;IAEjC,aAAc;QACV,CAAA,GAAA,2IAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,uKAAA,CAAA,sBAAmB,CAAC,IAAI;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,2KAAA,CAAA,sBAAmB,CAAC,IAAI;QAChD,IAAI,CAAC,SAAS;IAClB;IAEA,MAAM,YAAY;QACd,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;QACxD,IAAI,OAAO;YACP,QAAQ,KAAK,CAAC;YACd;QACJ;QACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI;IAC3C;IAEA,aAAa,QAAkB,EAAgB;QAC3C,OAAO;YACH,IAAI,SAAS,EAAE;YACf,MACI,SAAS,aAAa,EAAE,aACxB,SAAS,aAAa,EAAE,QACxB,SAAS,KAAK;YAClB,OAAO,SAAS,KAAK;YACrB,WAAW,SAAS,aAAa,EAAE;QACvC;IACJ;IAEA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;QAChC,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ", "debugId": null}}, {"offset": {"line": 4541, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/user/index.ts"], "sourcesContent": ["import { createContext, useContext } from 'react';\r\nimport { UserManager } from './manager';\r\n\r\nexport const userManager = new UserManager();\r\nconst UserContext = createContext(userManager);\r\nexport const useUserManager = () => useContext(UserContext);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,cAAc,IAAI,sKAAA,CAAA,cAAW;AAC1C,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAC3B,MAAM,iBAAiB,IAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 4558, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/posthog-provider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { env } from \"@/env\"\r\nimport { observer } from \"mobx-react-lite\"\r\nimport posthog from \"posthog-js\"\r\nimport { PostHog<PERSON><PERSON>ider as P<PERSON>rovider } from \"posthog-js/react\"\r\nimport { useEffect } from \"react\"\r\nimport { useUserManager } from \"./store/user\"\r\n\r\nexport const PostHogProvider = observer(({ children }: { children: React.ReactNode }) => {\r\n    const userManager = useUserManager();\r\n\r\n    useEffect(() => {\r\n        if (!env.NEXT_PUBLIC_POSTHOG_KEY) {\r\n            console.warn('PostHog key is not set, skipping initialization');\r\n            return;\r\n        }\r\n        posthog.init(\r\n            env.NEXT_PUBLIC_POSTHOG_KEY, {\r\n            api_host: env.NEXT_PUBLIC_POSTHOG_HOST,\r\n            capture_pageview: 'history_change',\r\n            capture_pageleave: true,\r\n            capture_exceptions: true,\r\n        })\r\n    }, [])\r\n\r\n    useEffect(() => {\r\n        if (userManager.user) {\r\n            try {\r\n                posthog.identify(userManager.user.id, {\r\n                    name: userManager.user.name,\r\n                    email: userManager.user.email,\r\n                    avatar_url: userManager.user.avatarUrl,\r\n                }, {\r\n                    signup_date: new Date().toISOString(),\r\n                })\r\n            } catch (error) {\r\n                console.error('Error identifying user:', error);\r\n            }\r\n        }\r\n    }, [userManager.user])\r\n\r\n    return (\r\n        <PHProvider client={posthog}>\r\n            {children}\r\n        </PHProvider>\r\n    )\r\n})\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,QAAQ,EAAiC;IAChF,MAAM,cAAc,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,mIAAA,CAAA,MAAG,CAAC,uBAAuB,EAAE;YAC9B,QAAQ,IAAI,CAAC;YACb;QACJ;QACA,+IAAA,CAAA,UAAO,CAAC,IAAI,CACR,mIAAA,CAAA,MAAG,CAAC,uBAAuB,EAAE;YAC7B,UAAU,mIAAA,CAAA,MAAG,CAAC,wBAAwB;YACtC,kBAAkB;YAClB,mBAAmB;YACnB,oBAAoB;QACxB;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,YAAY,IAAI,EAAE;YAClB,IAAI;gBACA,+IAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,EAAE,EAAE;oBAClC,MAAM,YAAY,IAAI,CAAC,IAAI;oBAC3B,OAAO,YAAY,IAAI,CAAC,KAAK;oBAC7B,YAAY,YAAY,IAAI,CAAC,SAAS;gBAC1C,GAAG;oBACC,aAAa,IAAI,OAAO,WAAW;gBACvC;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;YAC7C;QACJ;IACJ,GAAG;QAAC,YAAY,IAAI;KAAC;IAErB,qBACI,8OAAC,8JAAA,CAAA,kBAAU;QAAC,QAAQ,+IAAA,CAAA,UAAO;kBACtB;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 4623, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/hooks/use-feature-flags.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, type ReactNode } from 'react';\r\nimport { env } from '../env';\r\n\r\ntype ClientEnvKeys = keyof typeof env;\r\n\r\ntype FeatureFlags = Record<ClientEnvKeys, boolean>;\r\n\r\ninterface FeatureFlagsContextType {\r\n    isEnabled: <K extends ClientEnvKeys>(flag: K) => boolean;\r\n    flags: FeatureFlags;\r\n}\r\n\r\nconst FeatureFlagsContext = createContext<FeatureFlagsContextType | undefined>(undefined);\r\n\r\nexport const useFeatureFlags = () => {\r\n    const context = useContext(FeatureFlagsContext);\r\n    if (!context) {\r\n        throw new Error('useFeatureFlags must be used within a FeatureFlagsProvider');\r\n    }\r\n    return context;\r\n};\r\n\r\ninterface FeatureFlagsProviderProps {\r\n    children: ReactNode;\r\n}\r\n\r\nexport const FeatureFlagsProvider = ({ children }: FeatureFlagsProviderProps) => {\r\n    const flags = Object.keys(env).reduce((acc, key) => {\r\n        const envKey = key as ClientEnvKeys;\r\n        acc[envKey] = env[envKey] === 'true' || env[envKey] === true;\r\n        return acc;\r\n    }, {} as FeatureFlags);\r\n\r\n    const isEnabled = <K extends ClientEnvKeys>(flag: K): boolean => {\r\n        return flags[flag] || false;\r\n    };\r\n\r\n    return (\r\n        <FeatureFlagsContext.Provider value={{ isEnabled, flags }}>\r\n            {children}\r\n        </FeatureFlagsContext.Provider>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,kBAAkB;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;AAMO,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAA6B;IACxE,MAAM,QAAQ,OAAO,IAAI,CAAC,mIAAA,CAAA,MAAG,EAAE,MAAM,CAAC,CAAC,KAAK;QACxC,MAAM,SAAS;QACf,GAAG,CAAC,OAAO,GAAG,mIAAA,CAAA,MAAG,CAAC,OAAO,KAAK,UAAU,mIAAA,CAAA,MAAG,CAAC,OAAO,KAAK;QACxD,OAAO;IACX,GAAG,CAAC;IAEJ,MAAM,YAAY,CAA0B;QACxC,OAAO,KAAK,CAAC,KAAK,IAAI;IAC1B;IAEA,qBACI,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;YAAW;QAAM;kBACnD;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 4669, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/trpc/query-client.ts"], "sourcesContent": ["import { defaultShouldDehydrateQuery, QueryClient } from '@tanstack/react-query';\r\nimport SuperJSON from 'superjson';\r\n\r\nexport const createQueryClient = () =>\r\n    new QueryClient({\r\n        defaultOptions: {\r\n            queries: {\r\n                // With SSR, we usually want to set some default staleTime\r\n                // above 0 to avoid refetching immediately on the client\r\n                staleTime: 30 * 1000,\r\n            },\r\n            dehydrate: {\r\n                serializeData: SuperJSON.serialize,\r\n                shouldDehydrateQuery: (query) =>\r\n                    defaultShouldDehydrateQuery(query) || query.state.status === 'pending',\r\n            },\r\n            hydrate: {\r\n                deserializeData: SuperJSON.deserialize,\r\n            },\r\n        },\r\n    });\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,MAAM,oBAAoB,IAC7B,IAAI,6KAAA,CAAA,cAAW,CAAC;QACZ,gBAAgB;YACZ,SAAS;gBACL,0DAA0D;gBAC1D,wDAAwD;gBACxD,WAAW,KAAK;YACpB;YACA,WAAW;gBACP,eAAe,0IAAA,CAAA,UAAS,CAAC,SAAS;gBAClC,sBAAsB,CAAC,QACnB,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU,MAAM,KAAK,CAAC,MAAM,KAAK;YACrE;YACA,SAAS;gBACL,iBAAiB,0IAAA,CAAA,UAAS,CAAC,WAAW;YAC1C;QACJ;IACJ", "debugId": null}}, {"offset": {"line": 4699, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/trpc/react.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { QueryClientProvider, type QueryClient } from '@tanstack/react-query';\r\nimport { createTRPCReact } from '@trpc/react-query';\r\nimport { type inferRouterInputs, type inferRouterOutputs } from '@trpc/server';\r\nimport { useState } from 'react';\r\nimport { type AppRouter } from '~/server/api/root';\r\nimport { links } from './helpers';\r\nimport { createQueryClient } from './query-client';\r\n\r\nlet clientQueryClientSingleton: QueryClient | undefined = undefined;\r\nconst getQueryClient = () => {\r\n    if (typeof window === 'undefined') {\r\n        // Server: always make a new query client\r\n        return createQueryClient();\r\n    }\r\n    // Browser: use singleton pattern to keep the same query client\r\n    clientQueryClientSingleton ??= createQueryClient();\r\n\r\n    return clientQueryClientSingleton;\r\n};\r\n\r\nexport const api = createTRPCReact<AppRouter>();\r\n\r\n/**\r\n * Inference helper for inputs.\r\n *\r\n * @example type HelloInput = RouterInputs['example']['hello']\r\n */\r\nexport type RouterInputs = inferRouterInputs<AppRouter>;\r\n\r\n/**\r\n * Inference helper for outputs.\r\n *\r\n * @example type HelloOutput = RouterOutputs['example']['hello']\r\n */\r\nexport type RouterOutputs = inferRouterOutputs<AppRouter>;\r\n\r\nexport function TRPCReactProvider(props: { children: React.ReactNode }) {\r\n    const queryClient = getQueryClient();\r\n\r\n    const [trpcClient] = useState(() =>\r\n        api.createClient({\r\n            links,\r\n        }),\r\n    );\r\n\r\n    return (\r\n        <QueryClientProvider client={queryClient}>\r\n            <api.Provider client={trpcClient} queryClient={queryClient}>\r\n                {props.children}\r\n            </api.Provider>\r\n        </QueryClientProvider>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAEA;AAEA;AACA;AARA;;;;;;;AAUA,IAAI,6BAAsD;AAC1D,MAAM,iBAAiB;IACnB,wCAAmC;QAC/B,yCAAyC;QACzC,OAAO,CAAA,GAAA,uJAAA,CAAA,oBAAiB,AAAD;IAC3B;;AAKJ;AAEO,MAAM,MAAM,CAAA,GAAA,0KAAA,CAAA,kBAAe,AAAD;AAgB1B,SAAS,kBAAkB,KAAoC;IAClE,MAAM,cAAc;IAEpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAC1B,IAAI,YAAY,CAAC;YACb,OAAA,+IAAA,CAAA,QAAK;QACT;IAGJ,qBACI,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBACzB,cAAA,8OAAC,IAAI,QAAQ;YAAC,QAAQ;YAAY,aAAa;sBAC1C,MAAM,QAAQ;;;;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 4754, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/sonner.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from 'next-themes';\nimport { Toaster as Sonner, type ToasterProps, toast } from 'sonner';\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n    const { theme = 'system' } = useTheme();\n\n    return <Sonner theme={theme as ToasterProps['theme']} className=\"toaster group\" {...props} />;\n};\n\nexport { Toaster, toast };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACvC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBAAO,8OAAC,wIAAA,CAAA,UAAM;QAAC,OAAO;QAAgC,WAAU;QAAiB,GAAG,KAAK;;;;;;AAC7F", "debugId": null}}, {"offset": {"line": 4818, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/_components/theme.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\r\nimport * as React from 'react';\r\n\r\nexport function ThemeProvider({\r\n    children,\r\n    ...props\r\n}: React.ComponentProps<typeof NextThemesProvider>) {\r\n    return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,EAC1B,QAAQ,EACR,GAAG,OAC2C;IAC9C,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AAC3C", "debugId": null}}, {"offset": {"line": 4842, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/login/actions.tsx"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { SEED_USER } from '@onlook/db';\r\nimport { SignInMethod } from '@onlook/models';\r\nimport { headers } from 'next/headers';\r\nimport { redirect } from 'next/navigation';\r\n\r\nexport async function login(provider: SignInMethod) {\r\n    const supabase = await createClient();\r\n    const origin = (await headers()).get('origin');\r\n\r\n    // If already session, redirect\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n    if (session) {\r\n        redirect('/');\r\n    }\r\n\r\n    // Start OAuth flow\r\n    // Note: User object will be created in the auth callback route if it doesn't exist\r\n    const { data, error } = await supabase.auth.signInWithOAuth({\r\n        provider,\r\n        options: {\r\n            redirectTo: `${origin}/auth/callback`,\r\n        },\r\n    });\r\n\r\n    if (error) {\r\n        redirect('/error');\r\n    }\r\n\r\n    redirect(data.url);\r\n}\r\n\r\nexport async function devLogin() {\r\n    if (process.env.NODE_ENV !== 'development') {\r\n        throw new Error('Dev login is only available in development mode');\r\n    }\r\n\r\n    const supabase = await createClient();\r\n\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (session) {\r\n        redirect('/');\r\n    }\r\n\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n        email: SEED_USER.EMAIL,\r\n        password: SEED_USER.PASSWORD,\r\n    });\r\n\r\n    if (error) {\r\n        console.error('Error signing in with password:', error);\r\n        throw new Error('Error signing in with password');\r\n    }\r\n    redirect('/');\r\n}"], "names": [], "mappings": ";;;;;;IAoCsB,WAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 4855, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/login/actions.tsx"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { SEED_USER } from '@onlook/db';\r\nimport { SignInMethod } from '@onlook/models';\r\nimport { headers } from 'next/headers';\r\nimport { redirect } from 'next/navigation';\r\n\r\nexport async function login(provider: SignInMethod) {\r\n    const supabase = await createClient();\r\n    const origin = (await headers()).get('origin');\r\n\r\n    // If already session, redirect\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n    if (session) {\r\n        redirect('/');\r\n    }\r\n\r\n    // Start OAuth flow\r\n    // Note: User object will be created in the auth callback route if it doesn't exist\r\n    const { data, error } = await supabase.auth.signInWithOAuth({\r\n        provider,\r\n        options: {\r\n            redirectTo: `${origin}/auth/callback`,\r\n        },\r\n    });\r\n\r\n    if (error) {\r\n        redirect('/error');\r\n    }\r\n\r\n    redirect(data.url);\r\n}\r\n\r\nexport async function devLogin() {\r\n    if (process.env.NODE_ENV !== 'development') {\r\n        throw new Error('Dev login is only available in development mode');\r\n    }\r\n\r\n    const supabase = await createClient();\r\n\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (session) {\r\n        redirect('/');\r\n    }\r\n\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n        email: SEED_USER.EMAIL,\r\n        password: SEED_USER.PASSWORD,\r\n    });\r\n\r\n    if (error) {\r\n        console.error('Error signing in with password:', error);\r\n        throw new Error('Error signing in with password');\r\n    }\r\n    redirect('/');\r\n}"], "names": [], "mappings": ";;;;;;IAQsB,QAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 4868, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/auth/auth-context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { SignInMethod } from '@onlook/models/auth';\r\nimport localforage from 'localforage';\r\nimport { devLogin, login } from '../login/actions';\r\nconst LAST_SIGN_IN_METHOD_KEY = 'lastSignInMethod';\r\n\r\ninterface AuthContextType {\r\n    isPending: boolean;\r\n    lastSignInMethod: SignInMethod | null;\r\n    isAuthModalOpen: boolean;\r\n    setIsAuthModalOpen: (open: boolean) => void;\r\n    handleLogin: (method: SignInMethod) => void;\r\n    handleDevLogin: () => void;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider = ({ children }: { children: ReactNode }) => {\r\n    const [lastSignInMethod, setLastSignInMethod] = useState<SignInMethod | null>(null);\r\n    const [isPending, setIsPending] = useState(false);\r\n    const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);\r\n    useEffect(() => {\r\n        localforage.getItem(LAST_SIGN_IN_METHOD_KEY).then((lastSignInMethod: unknown) => {\r\n            setLastSignInMethod(lastSignInMethod as SignInMethod | null);\r\n        });\r\n    }, []);\r\n\r\n    const handleLogin = async (method: SignInMethod) => {\r\n        setIsPending(true);\r\n        await login(method);\r\n\r\n        localforage.setItem(LAST_SIGN_IN_METHOD_KEY, method);\r\n        setTimeout(() => {\r\n            setIsPending(false);\r\n        }, 5000);\r\n    };\r\n\r\n    const handleDevLogin = async () => {\r\n        setIsPending(true);\r\n        await devLogin();\r\n        setTimeout(() => {\r\n            setIsPending(false);\r\n        }, 5000);\r\n    }\r\n\r\n    return (\r\n        <AuthContext.Provider value={{ isPending, lastSignInMethod, handleLogin, handleDevLogin, isAuthModalOpen, setIsAuthModalOpen }}>\r\n            {children}\r\n        </AuthContext.Provider>\r\n    );\r\n};\r\n\r\nexport const useAuthContext = () => {\r\n    const context = useContext(AuthContext);\r\n    if (context === undefined) {\r\n        throw new Error('useAuthContext must be used within a AuthProvider');\r\n    }\r\n    return context;\r\n}; "], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AAAA;AANA;;;;;AAOA,MAAM,0BAA0B;AAWhC,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA2B;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,kJAAA,CAAA,UAAW,CAAC,OAAO,CAAC,yBAAyB,IAAI,CAAC,CAAC;YAC/C,oBAAoB;QACxB;IACJ,GAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACvB,aAAa;QACb,MAAM,CAAA,GAAA,oLAAA,CAAA,QAAK,AAAD,EAAE;QAEZ,kJAAA,CAAA,UAAW,CAAC,OAAO,CAAC,yBAAyB;QAC7C,WAAW;YACP,aAAa;QACjB,GAAG;IACP;IAEA,MAAM,iBAAiB;QACnB,aAAa;QACb,MAAM,CAAA,GAAA,oLAAA,CAAA,WAAQ,AAAD;QACb,WAAW;YACP,aAAa;QACjB,GAAG;IACP;IAEA,qBACI,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAW;YAAkB;YAAa;YAAgB;YAAiB;QAAmB;kBACxH;;;;;;AAGb;AAEO,MAAM,iBAAiB;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACvB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "debugId": null}}]}