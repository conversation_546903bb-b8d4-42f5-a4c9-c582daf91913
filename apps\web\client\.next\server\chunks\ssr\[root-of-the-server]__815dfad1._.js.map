{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/constants/index.ts"], "sourcesContent": ["export const Routes = {\r\n    HOME: '/',\r\n    LOGIN: '/login',\r\n    PRICING: '/pricing',\r\n    PROJECTS: '/projects',\r\n    PROJECT: '/project',\r\n    IMPORT_PROJECT: '/projects/import',\r\n    CALLBACK_STRIPE_SUCCESS: '/callback/stripe/success',\r\n    CALLBACK_STRIPE_CANCEL: '/callback/stripe/cancel',\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,SAAS;IAClB,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,SAAS;IACT,gBAAgB;IAChB,yBAAyB;IACzB,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/env.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\r\nimport { z } from 'zod';\r\n\r\nexport const env = createEnv({\r\n    /**\r\n     * Specify your server-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars.\r\n     */\r\n    server: {\r\n        NODE_ENV: z.enum(['development', 'test', 'production']),\r\n        ANTHROPIC_API_KEY: z.string(),\r\n        CSB_API_KEY: z.string(),\r\n        SUPABASE_DATABASE_URL: z.string().url(),\r\n        RESEND_API_KEY: z.string().optional(),\r\n        MORPH_API_KEY: z.string().optional(),\r\n        RELACE_API_KEY: z.string().optional(),\r\n        FREESTYLE_API_KEY: z.string().optional(),\r\n        STRIPE_WEBHOOK_SECRET: z.string().optional(),\r\n        STRIPE_SECRET_KEY: z.string().optional(),\r\n        AWS_ACCESS_KEY_ID: z.string().optional(),\r\n        AWS_SECRET_ACCESS_KEY: z.string().optional(),\r\n        AWS_REGION: z.string().optional(),\r\n    },\r\n    /**\r\n     * Specify your client-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n     * `NEXT_PUBLIC_`.\r\n     */\r\n    client: {\r\n        NEXT_PUBLIC_SITE_URL: z.string().url().default('http://localhost:3000'),\r\n        NEXT_PUBLIC_SUPABASE_URL: z.string(),\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\r\n        NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),\r\n        NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: z.boolean().default(false),\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: z.string().optional(),\r\n    },\r\n\r\n    /**\r\n     * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n     * middlewares) or client-side so we need to destruct manually.\r\n     */\r\n    runtimeEnv: {\r\n        NODE_ENV: process.env.NODE_ENV,\r\n        ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,\r\n        CSB_API_KEY: process.env.CSB_API_KEY,\r\n        RESEND_API_KEY: process.env.RESEND_API_KEY,\r\n        SUPABASE_DATABASE_URL: process.env.SUPABASE_DATABASE_URL,\r\n        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,\r\n        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\r\n        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\r\n        MORPH_API_KEY: process.env.MORPH_API_KEY,\r\n        RELACE_API_KEY: process.env.RELACE_API_KEY,\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: process.env.NEXT_PUBLIC_FEATURE_COLLABORATION,\r\n        FREESTYLE_API_KEY: process.env.FREESTYLE_API_KEY,\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: process.env.NEXT_PUBLIC_HOSTING_DOMAIN,\r\n        STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,\r\n        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,\r\n        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,\r\n        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,\r\n        AWS_REGION: process.env.AWS_REGION,\r\n    },\r\n    /**\r\n     * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n     * useful for Docker builds.\r\n     */\r\n    skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n    /**\r\n     * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n     * `SOME_VAR=''` will throw an error.\r\n     */\r\n    emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;IACzB;;;KAGC,GACD,QAAQ;QACJ,UAAU,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM;QAC3B,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;QACrB,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACrC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC;IACA;;;;KAIC,GACD,QAAQ;QACJ,sBAAsB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;QAC/C,0BAA0B,iLAAA,CAAA,IAAC,CAAC,MAAM;QAClC,+BAA+B,iLAAA,CAAA,IAAC,CAAC,MAAM;QACvC,yBAAyB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,0BAA0B,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7C,mCAAmC,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACvD,4BAA4B,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnD;IAEA;;;KAGC,GACD,YAAY;QACR,QAAQ;QACR,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,wBAAwB;QACxB,6BAA6B;QAC7B,yBAAyB,QAAQ,GAAG,CAAC,uBAAuB;QAC5D,0BAA0B,QAAQ,GAAG,CAAC,wBAAwB;QAC9D,eAAe,QAAQ,GAAG,CAAC,aAAa;QACxC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,mCAAmC,QAAQ,GAAG,CAAC,iCAAiC;QAChF,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;QAClE,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,YAAY,QAAQ,GAAG,CAAC,UAAU;IACtC;IACA;;;KAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;KAGC,GACD,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/supabase/server.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { createServerClient } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function createClient() {\r\n    const cookieStore = await cookies();\r\n\r\n    // Create a server's supabase client with newly configured cookie,\r\n    // which could be used to maintain user's session\r\n    return createServerClient(\r\n        env.NEXT_PUBLIC_SUPABASE_URL,\r\n        env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        {\r\n            cookies: {\r\n                getAll() {\r\n                    return cookieStore.getAll();\r\n                },\r\n                setAll(cookiesToSet) {\r\n                    try {\r\n                        cookiesToSet.forEach(({ name, value, options }) =>\r\n                            cookieStore.set(name, value, options),\r\n                        );\r\n                    } catch {\r\n                        // The `setAll` method was called from a Server Component.\r\n                        // This can be ignored if you have middleware refreshing\r\n                        // user sessions.\r\n                    }\r\n                },\r\n            },\r\n        },\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,eAAe;IAClB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,kEAAkE;IAClE,iDAAiD;IACjD,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACpB,mIAAA,CAAA,MAAG,CAAC,wBAAwB,EAC5B,mIAAA,CAAA,MAAG,CAAC,6BAA6B,EACjC;QACI,SAAS;YACL;gBACI,OAAO,YAAY,MAAM;YAC7B;YACA,QAAO,YAAY;gBACf,IAAI;oBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC1C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAErC,EAAE,OAAM;gBACJ,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACrB;YACJ;QACJ;IACJ;AAER", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/projects/layout.tsx"], "sourcesContent": ["import { Routes } from '@/utils/constants';\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { type Metadata } from 'next';\r\nimport { redirect } from 'next/navigation';\r\n\r\nexport const metadata: Metadata = {\r\n    title: 'Onlook',\r\n    description: 'Onlook – Projects',\r\n};\r\n\r\nexport default async function Layout({ children }: Readonly<{ children: React.ReactNode }>) {\r\n    const supabase = await createClient();\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n    if (!session) {\r\n        redirect(Routes.LOGIN);\r\n    }\r\n    return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AAAA;;;;;AAEO,MAAM,WAAqB;IAC9B,OAAO;IACP,aAAa;AACjB;AAEe,eAAe,OAAO,EAAE,QAAQ,EAA2C;IACtF,MAAM,WAAW,MAAM,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACF,MAAM,EAAE,OAAO,EAAE,EACpB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAClC,IAAI,CAAC,SAAS;QACV,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,2JAAA,CAAA,SAAM,CAAC,KAAK;IACzB;IACA,qBAAO;kBAAG;;AACd", "debugId": null}}]}