(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/webfontloader/webfontloader.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_webfontloader_webfontloader_770441c1.js",
  "static/chunks/node_modules_webfontloader_webfontloader_674450b8.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/webfontloader/webfontloader.js [app-client] (ecmascript)");
    });
});
}}),
}]);