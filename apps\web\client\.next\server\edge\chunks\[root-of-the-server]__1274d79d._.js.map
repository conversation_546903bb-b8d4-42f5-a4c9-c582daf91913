{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/client/src/env.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\r\nimport { z } from 'zod';\r\n\r\nexport const env = createEnv({\r\n    /**\r\n     * Specify your server-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars.\r\n     */\r\n    server: {\r\n        NODE_ENV: z.enum(['development', 'test', 'production']),\r\n        ANTHROPIC_API_KEY: z.string(),\r\n        CSB_API_KEY: z.string(),\r\n        SUPABASE_DATABASE_URL: z.string().url(),\r\n        RESEND_API_KEY: z.string().optional(),\r\n        MORPH_API_KEY: z.string().optional(),\r\n        RELACE_API_KEY: z.string().optional(),\r\n        FREESTYLE_API_KEY: z.string().optional(),\r\n        STRIPE_WEBHOOK_SECRET: z.string().optional(),\r\n        STRIPE_SECRET_KEY: z.string().optional(),\r\n        AWS_ACCESS_KEY_ID: z.string().optional(),\r\n        AWS_SECRET_ACCESS_KEY: z.string().optional(),\r\n        AWS_REGION: z.string().optional(),\r\n    },\r\n    /**\r\n     * Specify your client-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n     * `NEXT_PUBLIC_`.\r\n     */\r\n    client: {\r\n        NEXT_PUBLIC_SITE_URL: z.string().url().default('http://localhost:3000'),\r\n        NEXT_PUBLIC_SUPABASE_URL: z.string(),\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\r\n        NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),\r\n        NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: z.boolean().default(false),\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: z.string().optional(),\r\n    },\r\n\r\n    /**\r\n     * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n     * middlewares) or client-side so we need to destruct manually.\r\n     */\r\n    runtimeEnv: {\r\n        NODE_ENV: process.env.NODE_ENV,\r\n        ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,\r\n        CSB_API_KEY: process.env.CSB_API_KEY,\r\n        RESEND_API_KEY: process.env.RESEND_API_KEY,\r\n        SUPABASE_DATABASE_URL: process.env.SUPABASE_DATABASE_URL,\r\n        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,\r\n        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\r\n        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\r\n        MORPH_API_KEY: process.env.MORPH_API_KEY,\r\n        RELACE_API_KEY: process.env.RELACE_API_KEY,\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: process.env.NEXT_PUBLIC_FEATURE_COLLABORATION,\r\n        FREESTYLE_API_KEY: process.env.FREESTYLE_API_KEY,\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: process.env.NEXT_PUBLIC_HOSTING_DOMAIN,\r\n        STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,\r\n        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,\r\n        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,\r\n        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,\r\n        AWS_REGION: process.env.AWS_REGION,\r\n    },\r\n    /**\r\n     * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n     * useful for Docker builds.\r\n     */\r\n    skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n    /**\r\n     * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n     * `SOME_VAR=''` will throw an error.\r\n     */\r\n    emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;IACzB;;;KAGC,GACD,QAAQ;QACJ,UAAU,yLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,yLAAA,CAAA,IAAC,CAAC,MAAM;QAC3B,aAAa,yLAAA,CAAA,IAAC,CAAC,MAAM;QACrB,uBAAuB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACrC,gBAAgB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,eAAe,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,gBAAgB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,mBAAmB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,mBAAmB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,mBAAmB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,YAAY,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC;IACA;;;;KAIC,GACD,QAAQ;QACJ,sBAAsB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;QAC/C,0BAA0B,yLAAA,CAAA,IAAC,CAAC,MAAM;QAClC,+BAA+B,yLAAA,CAAA,IAAC,CAAC,MAAM;QACvC,yBAAyB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,0BAA0B,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7C,mCAAmC,yLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACvD,4BAA4B,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnD;IAEA;;;KAGC,GACD,YAAY;QACR,QAAQ;QACR,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,wBAAwB;QACxB,6BAA6B;QAC7B,yBAAyB,QAAQ,GAAG,CAAC,uBAAuB;QAC5D,0BAA0B,QAAQ,GAAG,CAAC,wBAAwB;QAC9D,eAAe,QAAQ,GAAG,CAAC,aAAa;QACxC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,mCAAmC,QAAQ,GAAG,CAAC,iCAAiC;QAChF,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;QAClE,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,YAAY,QAAQ,GAAG,CAAC,UAAU;IACtC;IACA;;;KAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;KAGC,GACD,wBAAwB;AAC5B"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/client/src/utils/supabase/middleware.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { createServerClient } from '@supabase/ssr';\r\nimport { NextResponse, type NextRequest } from 'next/server';\r\n\r\nexport async function updateSession(request: NextRequest) {\r\n    let supabaseResponse = NextResponse.next({\r\n        request,\r\n    });\r\n\r\n    const supabase = createServerClient(\r\n        env.NEXT_PUBLIC_SUPABASE_URL,\r\n        env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        {\r\n            cookies: {\r\n                getAll() {\r\n                    return request.cookies.getAll();\r\n                },\r\n                setAll(cookiesToSet) {\r\n                    cookiesToSet.forEach(({ name, value, options }) =>\r\n                        request.cookies.set(name, value),\r\n                    );\r\n                    supabaseResponse = NextResponse.next({\r\n                        request,\r\n                    });\r\n                    cookiesToSet.forEach(({ name, value, options }) =>\r\n                        supabaseResponse.cookies.set(name, value, options),\r\n                    );\r\n                },\r\n            },\r\n        },\r\n    );\r\n\r\n    // refreshing the auth token\r\n    await supabase.auth.getUser();\r\n    return supabaseResponse;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,eAAe,cAAc,OAAoB;IACpD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACrC;IACJ;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,EAC9B,2IAAA,CAAA,MAAG,CAAC,wBAAwB,EAC5B,2IAAA,CAAA,MAAG,CAAC,6BAA6B,EACjC;QACI,SAAS;YACL;gBACI,OAAO,QAAQ,OAAO,CAAC,MAAM;YACjC;YACA,QAAO,YAAY;gBACf,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC1C,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAE9B,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACjC;gBACJ;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC1C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAElD;QACJ;IACJ;IAGJ,4BAA4B;IAC5B,MAAM,SAAS,IAAI,CAAC,OAAO;IAC3B,OAAO;AACX"}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/client/src/middleware.ts"], "sourcesContent": ["import { updateSession } from '@/utils/supabase/middleware';\r\nimport { type NextRequest } from 'next/server';\r\n\r\nexport async function middleware(request: NextRequest) {\r\n    // update user's auth session\r\n    return await updateSession(request);\r\n}\r\n\r\nexport const config = {\r\n    matcher: [\r\n        /*\r\n         * Match all request paths except for the ones starting with:\r\n         * - _next/static (static files)\r\n         * - _next/image (image optimization files)\r\n         * - favicon.ico (favicon file)\r\n         * Feel free to modify this pattern to include more paths.\r\n         */\r\n        '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\r\n    ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,WAAW,OAAoB;IACjD,6BAA6B;IAC7B,OAAO,MAAM,CAAA,GAAA,uKAAA,CAAA,gBAAa,AAAD,EAAE;AAC/B;AAEO,MAAM,SAAS;IAClB,SAAS;QACL;;;;;;SAMC,GACD;KACH;AACL"}}]}