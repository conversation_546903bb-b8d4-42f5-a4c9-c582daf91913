const CHUNK_PUBLIC_PATH = "server/app/api/trpc/[trpc]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_d2853915._.js");
runtime.loadChunk("server/chunks/apps_web_client_1f176412._.js");
runtime.loadChunk("server/chunks/packages_db_src_38f6ebee._.js");
runtime.loadChunk("server/chunks/packages_utility_src_69c581cd._.js");
runtime.loadChunk("server/chunks/packages_fonts_src_72f52055._.js");
runtime.loadChunk("server/chunks/packages_34d409e2._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__da637332._.js");
runtime.loadChunk("server/chunks/node_modules_next_39cd53c6._.js");
runtime.loadChunk("server/chunks/node_modules_@trpc_server_dist_cdf3f1b7._.js");
runtime.loadChunk("server/chunks/node_modules_zod_dist_esm_54374b3b._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_ws_686b49a1._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_4e4e8dc6._.js");
runtime.loadChunk("server/chunks/node_modules_drizzle-orm_e27c2738._.js");
runtime.loadChunk("server/chunks/node_modules_stripe_esm_da04138f._.js");
runtime.loadChunk("server/chunks/node_modules_openai_a822ecb7._.js");
runtime.loadChunk("server/chunks/node_modules_color-namer_16226826._.js");
runtime.loadChunk("server/chunks/node_modules_culori_src_046c50d0._.js");
runtime.loadChunk("server/chunks/node_modules_@babel_standalone_babel_7851784c.js");
runtime.loadChunk("server/chunks/node_modules_tldts_dist_es6_28064b83._.js");
runtime.loadChunk("server/chunks/node_modules_ai_dist_index_mjs_1ce7e475._.js");
runtime.loadChunk("server/chunks/node_modules_@trpc_client_dist_1cbe8fea._.js");
runtime.loadChunk("server/chunks/node_modules_@octokit_plugin-rest-endpoint-methods_dist-src_f65126a0._.js");
runtime.loadChunk("server/chunks/node_modules_entities_lib_esm_8351ace8._.js");
runtime.loadChunk("server/chunks/node_modules_@react-email_tailwind_dist_index_mjs_1693d4ed._.js");
runtime.loadChunk("server/chunks/node_modules_@codesandbox_sdk_dist_esm_index_8e31e054.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_53b556bb._.js");
runtime.loadChunk("server/chunks/node_modules_@ai-sdk_3e1c18c6._.js");
runtime.loadChunk("server/chunks/node_modules_@octokit_720f2ff1._.js");
runtime.loadChunk("server/chunks/node_modules_653daa74._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/client/.next-internal/server/app/api/trpc/[trpc]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/client/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/client/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
