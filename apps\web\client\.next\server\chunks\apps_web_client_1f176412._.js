module.exports = {

"[project]/apps/web/client/.next-internal/server/app/api/trpc/[trpc]/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[project]/apps/web/client/src/env.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "env": (()=>env)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@t3-oss/env-nextjs/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
;
;
const env = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createEnv"])({
    /**
     * Specify your server-side environment variables schema here. This way you can ensure the app
     * isn't built with invalid env vars.
     */ server: {
        NODE_ENV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'development',
            'test',
            'production'
        ]),
        ANTHROPIC_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        CSB_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        SUPABASE_DATABASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url(),
        RESEND_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        MORPH_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        RELACE_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        FREESTYLE_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        STRIPE_WEBHOOK_SECRET: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        STRIPE_SECRET_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        AWS_ACCESS_KEY_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        AWS_SECRET_ACCESS_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        AWS_REGION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    },
    /**
     * Specify your client-side environment variables schema here. This way you can ensure the app
     * isn't built with invalid env vars. To expose them to the client, prefix them with
     * `NEXT_PUBLIC_`.
     */ client: {
        NEXT_PUBLIC_SITE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().default('http://localhost:3000'),
        NEXT_PUBLIC_SUPABASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        NEXT_PUBLIC_SUPABASE_ANON_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        NEXT_PUBLIC_POSTHOG_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        NEXT_PUBLIC_POSTHOG_HOST: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        NEXT_PUBLIC_FEATURE_COLLABORATION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(false),
        NEXT_PUBLIC_HOSTING_DOMAIN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    },
    /**
     * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
     * middlewares) or client-side so we need to destruct manually.
     */ runtimeEnv: {
        NODE_ENV: ("TURBOPACK compile-time value", "development"),
        ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
        CSB_API_KEY: process.env.CSB_API_KEY,
        RESEND_API_KEY: process.env.RESEND_API_KEY,
        SUPABASE_DATABASE_URL: process.env.SUPABASE_DATABASE_URL,
        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
        NEXT_PUBLIC_SUPABASE_URL: ("TURBOPACK compile-time value", "http://127.0.0.1:54321"),
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"),
        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
        MORPH_API_KEY: process.env.MORPH_API_KEY,
        RELACE_API_KEY: process.env.RELACE_API_KEY,
        NEXT_PUBLIC_FEATURE_COLLABORATION: process.env.NEXT_PUBLIC_FEATURE_COLLABORATION,
        FREESTYLE_API_KEY: process.env.FREESTYLE_API_KEY,
        NEXT_PUBLIC_HOSTING_DOMAIN: process.env.NEXT_PUBLIC_HOSTING_DOMAIN,
        STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
        AWS_REGION: process.env.AWS_REGION
    },
    /**
     * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
     * useful for Docker builds.
     */ skipValidation: !!process.env.SKIP_ENV_VALIDATION,
    /**
     * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
     * `SOME_VAR=''` will throw an error.
     */ emptyStringAsUndefined: true
});
}}),
"[project]/apps/web/client/src/utils/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/env.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    // Create a server's supabase client with newly configured cookie,
    // which could be used to maintain user's session
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NEXT_PUBLIC_SUPABASE_URL, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NEXT_PUBLIC_SUPABASE_ANON_KEY, {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}}),
"[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:
 * 1. You want to modify request context (see Part 1).
 * 2. You want to create a new middleware or type of procedure (see Part 3).
 *
 * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will
 * need to use are documented accordingly near the end.
 */ __turbopack_context__.s({
    "createCallerFactory": (()=>createCallerFactory),
    "createTRPCContext": (()=>createTRPCContext),
    "createTRPCRouter": (()=>createTRPCRouter),
    "protectedProcedure": (()=>protectedProcedure),
    "publicProcedure": (()=>publicProcedure)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$initTRPC$2d$COaJMShh$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/initTRPC-COaJMShh.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/superjson/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/ZodError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/utils/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/client.ts [app-route] (ecmascript)");
;
;
;
;
;
const createTRPCContext = async (opts)=>{
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: 'UNAUTHORIZED',
            message: error.message
        });
    }
    return {
        db: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"],
        supabase,
        user,
        ...opts
    };
};
/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */ const t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$initTRPC$2d$COaJMShh$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initTRPC"].context().create({
    transformer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"],
    errorFormatter ({ shape, error }) {
        return {
            ...shape,
            data: {
                ...shape.data,
                zodError: error.cause instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ZodError"] ? error.cause.flatten() : null
            }
        };
    }
});
const createCallerFactory = t.createCallerFactory;
const createTRPCRouter = t.router;
/**
 * Middleware for timing procedure execution and adding an artificial delay in development.
 *
 * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating
 * network latency that would occur in production but not in local development.
 */ const timingMiddleware = t.middleware(async ({ next, path })=>{
    const start = Date.now();
    if (t._config.isDev) {
        // artificial delay in dev
        const waitMs = Math.floor(Math.random() * 400) + 100;
        await new Promise((resolve)=>setTimeout(resolve, waitMs));
    }
    const result = await next();
    const end = Date.now();
    console.log(`[TRPC] ${path} took ${end - start}ms to execute`);
    return result;
});
const publicProcedure = t.procedure.use(timingMiddleware);
const protectedProcedure = t.procedure.use(timingMiddleware).use(({ ctx, next })=>{
    if (!ctx.user) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: 'UNAUTHORIZED'
        });
    }
    if (!ctx.user.email) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: 'UNAUTHORIZED',
            message: 'User must have an email address to access this resource'
        });
    }
    return next({
        ctx: {
            // infers the `session` as non-nullable
            user: ctx.user,
            db: ctx.db
        }
    });
});
}}),
"[project]/apps/web/client/src/server/api/routers/code.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "codeRouter": (()=>codeRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$ai$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/ai/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$ai$2f$src$2f$apply$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/ai/src/apply/client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
const codeRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    applyDiff: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        originalCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        updateSnippet: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$ai$2f$src$2f$apply$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["applyCodeChange"])(input.originalCode, input.updateSnippet);
            if (!result) {
                throw new Error('Failed to apply code change. Please try again.');
            }
            return {
                result,
                error: null
            };
        } catch (error) {
            console.error('Failed to apply code change', error);
            return {
                error: error instanceof Error ? error.message : 'Unknown error',
                result: null
            };
        }
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/domain/freestyle.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "initializeFreestyleSdk": (()=>initializeFreestyleSdk)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/env.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$freestyle$2d$sandboxes$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/freestyle-sandboxes/dist/index.mjs [app-route] (ecmascript)");
;
;
;
const initializeFreestyleSdk = ()=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].FREESTYLE_API_KEY) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: 'PRECONDITION_FAILED',
            message: 'FREESTYLE_API_KEY is not configured. Please set the environment variable to use domain publishing features.'
        });
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$freestyle$2d$sandboxes$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FreestyleSandboxes"]({
        apiKey: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].FREESTYLE_API_KEY
    });
};
}}),
"[project]/apps/web/client/src/server/api/routers/domain/adapters/freestyle.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FreestyleAdapter": (()=>FreestyleAdapter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/freestyle.ts [app-route] (ecmascript)");
;
class FreestyleAdapter {
    async deploy(request) {
        const sdk = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeFreestyleSdk"])();
        const res = await sdk.deployWeb({
            files: request.files,
            kind: 'files'
        }, request.config);
        const freestyleResponse = res;
        if (freestyleResponse.error) {
            throw new Error(freestyleResponse.error.message || freestyleResponse.message || 'Unknown error');
        }
        return {
            deploymentId: freestyleResponse.data?.deploymentId ?? '',
            success: true
        };
    }
}
}}),
"[project]/apps/web/client/src/server/api/routers/domain/hosting-factory.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HostingProviderFactory": (()=>HostingProviderFactory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/models/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$hosting$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/models/src/hosting/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$adapters$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/adapters/freestyle.ts [app-route] (ecmascript)");
;
;
class HostingProviderFactory {
    static create(provider = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$hosting$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HostingProvider"].FREESTYLE) {
        switch(provider){
            case __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$hosting$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HostingProvider"].FREESTYLE:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$adapters$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FreestyleAdapter"]();
            default:
                throw new Error(`Unsupported hosting provider: ${provider}`);
        }
    }
}
}}),
"[project]/apps/web/client/src/server/api/routers/domain/preview.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "previewRouter": (()=>previewRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/preview.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/published.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/models/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$hosting$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/models/src/hosting/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$hosting$2d$factory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/hosting-factory.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
const previewRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const preview = await ctx.db.query.previewDomains.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].projectId, input.projectId)
        });
        return preview;
    }),
    create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        domain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        // Check if the domain is already taken by another project
        // This should never happen, but just in case
        const existing = await ctx.db.query.previewDomains.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].fullDomain, input.domain), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ne"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].projectId, input.projectId))
        });
        if (existing) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'BAD_REQUEST',
                message: 'Domain already taken'
            });
        }
        const [preview] = await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"]).values({
            fullDomain: input.domain,
            projectId: input.projectId
        }).returning({
            fullDomain: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].fullDomain
        });
        if (!preview) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'BAD_REQUEST',
                message: 'Failed to create preview domain, no preview domain returned'
            });
        }
        return {
            domain: preview.fullDomain
        };
    }),
    publish: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'preview',
            'custom'
        ]),
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        files: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].record(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            encoding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        })),
        config: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            domains: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()),
            entrypoint: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            envVars: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].record(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional()
        })
    })).mutation(async ({ ctx, input })=>{
        if (input.type === 'preview') {
            const preview = await ctx.db.query.previewDomains.findFirst({
                where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].projectId, input.projectId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inArray"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].fullDomain, input.config.domains))
            });
            if (!preview) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: 'BAD_REQUEST',
                    message: 'No preview domain found'
                });
            }
        } else if (input.type === 'custom') {
            const custom = await ctx.db.query.publishedDomains.findFirst({
                where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publishedDomains"].projectId, input.projectId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inArray"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publishedDomains"].fullDomain, input.config.domains))
            });
            if (!custom) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: 'BAD_REQUEST',
                    message: 'No custom domain found'
                });
            }
        }
        const adapter = __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$hosting$2d$factory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HostingProviderFactory"].create(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$hosting$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HostingProvider"].FREESTYLE);
        const deploymentFiles = {};
        for (const [path, file] of Object.entries(input.files)){
            deploymentFiles[path] = {
                content: file.content,
                encoding: file.encoding === 'base64' ? 'base64' : 'utf-8'
            };
        }
        const result = await adapter.deploy({
            files: deploymentFiles,
            config: input.config
        });
        return result;
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/domain/verify.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "verificationRouter": (()=>verificationRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$custom$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/custom.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/verification.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/published.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/models/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/models/src/domain/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/freestyle.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
const verificationRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const verification = await ctx.db.query.customDomainVerification.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomainVerification"].projectId, input.projectId)
        });
        return verification;
    }),
    create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        domain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        // Use upsert to handle the unique constraint properly
        const [customDomain] = await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$custom$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomains"]).values({
            apexDomain: input.domain
        }).onConflictDoUpdate({
            target: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$custom$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomains"].apexDomain,
            set: {
                updatedAt: new Date()
            }
        }).returning();
        if (!customDomain) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to create or update domain'
            });
        }
        // Check if verification request already exists
        const verification = await ctx.db.query.customDomainVerification.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomainVerification"].domainId, customDomain.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomainVerification"].projectId, input.projectId))
        });
        if (verification) {
            return verification;
        }
        const sdk = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeFreestyleSdk"])();
        const res = await sdk.createDomainVerificationRequest(input.domain);
        await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomainVerification"]).values({
            domainId: customDomain.id,
            projectId: input.projectId,
            verificationId: res.id,
            verificationCode: res.verificationCode
        });
        return res;
    }),
    verify: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        verificationId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        const verification = await ctx.db.query.customDomainVerification.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomainVerification"].verificationId, input.verificationId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$verification$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomainVerification"].projectId, input.projectId))
        });
        if (!verification) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'NOT_FOUND',
                message: 'Verification request not found'
            });
        }
        if (verification.status === __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VerificationRequestStatus"].USED) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'BAD_REQUEST',
                message: 'Domain already verified'
            });
        }
        if (verification.status === __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VerificationRequestStatus"].EXPIRED) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'BAD_REQUEST',
                message: 'Verification request expired'
            });
        }
        const sdk = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$freestyle$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeFreestyleSdk"])();
        const res = await sdk.verifyDomainVerificationRequest(input.verificationId);
        if (res.message) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'INTERNAL_SERVER_ERROR',
                message: res.message
            });
        }
        const domain = res.domain;
        if (!domain) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Domain not found'
            });
        }
        await ctx.db.transaction(async (tx)=>{
            await tx.update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$custom$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomains"]).set({
                verified: true
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$custom$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customDomains"].id, verification.domainId));
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publishedDomains"]).values({
                projectId: verification.projectId,
                fullDomain: domain,
                domainId: verification.domainId
            });
        });
        return res;
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/domain/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "domainRouter": (()=>domainRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/preview.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/domain/published.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$domain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/domain.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/preview.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$verify$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/verify.ts [app-route] (ecmascript)");
;
;
;
;
;
;
const domainRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    preview: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewRouter"],
    verification: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$verify$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verificationRouter"],
    getAll: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const preview = await ctx.db.query.previewDomains.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$preview$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["previewDomains"].projectId, input.projectId)
        });
        const published = await ctx.db.query.publishedDomains.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$domain$2f$published$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publishedDomains"].projectId, input.projectId)
        });
        return {
            preview: preview ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$domain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toDomainInfoFromPreview"])(preview) : null,
            published: published ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$domain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toDomainInfoFromPublished"])(published) : null
        };
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/forward/editor.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "editorForwardRouter": (()=>editorForwardRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$rpc$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/rpc/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$rpc$2f$src$2f$trpc$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/rpc/src/trpc/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$DbSHOzlB$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/wsLink-DbSHOzlB.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$DIfnmmcu$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpBatchLink-DIfnmmcu.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$splitLink$2d$BVblHq4n$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/splitLink-BVblHq4n.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/superjson/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
;
const { port, prefix } = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$rpc$2f$src$2f$trpc$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["editorServerConfig"];
const urlEnd = `localhost:${port}${prefix}`;
const wsClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$DbSHOzlB$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createWSClient"])({
    url: `ws://${urlEnd}`
});
const editorClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createTRPCClient"])({
    links: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$splitLink$2d$BVblHq4n$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["splitLink"])({
            condition (op) {
                return op.type === 'subscription';
            },
            true: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$DbSHOzlB$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["wsLink"])({
                client: wsClient,
                transformer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]
            }),
            false: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$DIfnmmcu$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["httpBatchLink"])({
                url: `http://${urlEnd}`,
                transformer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]
            })
        })
    ]
});
const editorForwardRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    sandbox: (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
        create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publicProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).mutation(({ input })=>{
            return editorClient.sandbox.create.mutate(input);
        }),
        start: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publicProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).mutation(({ input })=>{
            return editorClient.sandbox.start.mutate(input);
        }),
        stop: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publicProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).mutation(({ input })=>{
            return editorClient.sandbox.stop.mutate(input);
        }),
        status: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["publicProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).query(({ input })=>{
            return editorClient.sandbox.status.query(input);
        })
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/forward/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$forward$2f$editor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/forward/editor.ts [app-route] (ecmascript)");
;
}}),
"[project]/apps/web/client/src/server/api/routers/forward/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$forward$2f$editor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/forward/editor.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$forward$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/forward/index.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/apps/web/client/src/server/api/routers/github.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "githubRouter": (()=>githubRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$octokit$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/octokit/dist-bundle/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
;
;
;
;
// Helper function to get user's GitHub access token from Supabase session
const getUserGitHubToken = async (supabase)=>{
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error || !session) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: 'UNAUTHORIZED',
            message: 'No active session found'
        });
    }
    // GitHub OAuth token should be in provider_token, not access_token
    // access_token is Supabase's JWT, provider_token is GitHub's OAuth token
    const githubToken = session.provider_token;
    if (!githubToken) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: 'UNAUTHORIZED',
            message: `GitHub token not found. Please reconnect your GitHub account.`
        });
    }
    return githubToken;
};
// Create Octokit instance with user's token
const createUserOctokit = async (supabase)=>{
    const token = await getUserGitHubToken(supabase);
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$octokit$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Octokit"]({
        auth: token
    });
};
const githubRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    validate: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        owner: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        repo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ input, ctx })=>{
        const octokit = await createUserOctokit(ctx.supabase);
        const { data } = await octokit.rest.repos.get({
            owner: input.owner,
            repo: input.repo
        });
        return {
            branch: data.default_branch,
            isPrivateRepo: data.private
        };
    }),
    getRepo: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        owner: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        repo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ input, ctx })=>{
        const octokit = await createUserOctokit(ctx.supabase);
        const { data } = await octokit.rest.repos.get({
            owner: input.owner,
            repo: input.repo
        });
        return data;
    }),
    getOrganizations: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        username: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    }).optional()).query(async ({ input, ctx })=>{
        const octokit = await createUserOctokit(ctx.supabase);
        if (input?.username) {
            const { data } = await octokit.rest.orgs.listForUser({
                username: input.username
            });
            return data;
        } else {
            const { data } = await octokit.rest.orgs.listForAuthenticatedUser();
            return data;
        }
    }),
    getRepositories: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        username: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    }).optional()).query(async ({ input, ctx })=>{
        const octokit = await createUserOctokit(ctx.supabase);
        if (input?.username) {
            // listForUser only supports 'all', 'owner', 'member' types
            const { data } = await octokit.rest.repos.listForUser({
                username: input.username
            });
            return data;
        } else {
            const { data } = await octokit.rest.repos.listForAuthenticatedUser({
                per_page: 100,
                page: 1
            });
            return data;
        }
    }),
    getRepoFiles: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        owner: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        repo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        path: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default(''),
        ref: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional() // branch, tag, or commit SHA
    })).query(async ({ input, ctx })=>{
        const octokit = await createUserOctokit(ctx.supabase);
        const { data } = await octokit.rest.repos.getContent({
            owner: input.owner,
            repo: input.repo,
            path: input.path,
            ...input.ref && {
                ref: input.ref
            }
        });
        return data;
    }),
    checkGitHubConnection: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ({ ctx })=>{
        try {
            const token = await getUserGitHubToken(ctx.supabase);
            return {
                connected: !!token
            };
        } catch (error) {
            return {
                connected: false
            };
        }
    }),
    reconnectGitHub: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].mutation(async ({ ctx })=>{
        const origin = process.env.NEXT_PUBLIC_APP_URL;
        const { data, error } = await ctx.supabase.auth.signInWithOAuth({
            provider: 'github',
            options: {
                redirectTo: `${origin}/auth/callback`,
                skipBrowserRedirect: true
            }
        });
        if (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to initiate GitHub reconnection',
                cause: error
            });
        }
        return {
            url: data.url
        };
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/image.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "imageRouter": (()=>imageRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$image$2d$server$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/image-server/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$image$2d$server$2f$src$2f$compress$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/image-server/src/compress.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
const imageRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    compress: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        imageData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        options: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            quality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            height: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            format: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
                'jpeg',
                'png',
                'webp',
                'avif',
                'auto'
            ]).optional(),
            progressive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
            mozjpeg: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
            effort: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            compressionLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            keepAspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
            withoutEnlargement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional()
        }).optional()
    })).mutation(async ({ input })=>{
        try {
            const buffer = Buffer.from(input.imageData, 'base64');
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$image$2d$server$2f$src$2f$compress$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compressImageServer"])(buffer, undefined, input.options || {});
            // Convert buffer to base64 for client transmission
            if (result.success && result.buffer) {
                const { buffer: resultBuffer, ...restResult } = result;
                return {
                    ...restResult,
                    bufferData: resultBuffer.toString('base64')
                };
            }
            const { buffer: resultBuffer, ...restResult } = result;
            return restResult;
        } catch (error) {
            console.error('Error compressing image:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown compression error'
            };
        }
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/project/canvas.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "canvasRouter": (()=>canvasRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/canvas/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
const canvasRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const dbCanvas = await ctx.db.query.canvases.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvases"].projectId, input.projectId)
        });
        if (!dbCanvas) {
            return null;
        }
        return dbCanvas;
    }),
    update: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvasUpdateSchema"]).mutation(async ({ ctx, input })=>{
        try {
            if (!input.id) {
                throw new Error('Canvas ID is required');
            }
            await ctx.db.update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvases"]).set(input).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvases"].id, input.id));
            return true;
        } catch (error) {
            console.error('Error updating canvas', error);
            return false;
        }
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/project/chat.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "chatRouter": (()=>chatRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/chat/conversation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/chat/message.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/conversation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/message.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
const conversationRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const dbConversations = await ctx.db.query.conversations.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["conversations"].projectId, input.projectId),
            orderBy: (conversations, { desc })=>[
                    desc(conversations.updatedAt)
                ]
        });
        return dbConversations.map((conversation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toConversation"])(conversation));
    }),
    upsert: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        conversation: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["conversationInsertSchema"]
    })).mutation(async ({ ctx, input })=>{
        return await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["conversations"]).values(input.conversation).onConflictDoUpdate({
            target: [
                __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["conversations"].id
            ],
            set: {
                ...input.conversation
            }
        });
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        conversationId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        return await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["conversations"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$conversation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["conversations"].id, input.conversationId));
    })
});
const messageRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        conversationId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const dbMessages = await ctx.db.query.messages.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["messages"].conversationId, input.conversationId),
            orderBy: (messages, { asc })=>[
                    asc(messages.createdAt)
                ]
        });
        return dbMessages.map((message)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toMessage"])(message));
    }),
    upsert: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        message: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["messageInsertSchema"]
    })).mutation(async ({ ctx, input })=>{
        const normalizedMessage = {
            ...input.message,
            role: input.message.role,
            parts: input.message.parts
        };
        return await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["messages"]).values(normalizedMessage).onConflictDoUpdate({
            target: [
                __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["messages"].id
            ],
            set: {
                ...normalizedMessage
            }
        });
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        messageIds: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string())
    })).mutation(async ({ ctx, input })=>{
        return await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["messages"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inArray"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$chat$2f$message$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["messages"].id, input.messageIds));
    })
});
const chatRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    conversation: conversationRouter,
    message: messageRouter
});
}}),
"[project]/apps/web/client/src/server/api/routers/project/frame.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "frameRouter": (()=>frameRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/canvas/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
const frameRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        frameId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const dbFrame = await ctx.db.query.frames.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"].id, input.frameId)
        });
        if (!dbFrame) {
            return null;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toFrame"])(dbFrame);
    }),
    getByCanvas: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        canvasId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const dbFrames = await ctx.db.query.frames.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"].canvasId, input.canvasId),
            orderBy: (frames, { asc })=>[
                    asc(frames.x),
                    asc(frames.y)
                ]
        });
        return dbFrames.map((frame)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toFrame"])(frame));
    }),
    create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frameInsertSchema"]).mutation(async ({ ctx, input })=>{
        try {
            const normalizedInput = {
                ...input,
                type: input.type
            };
            await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"]).values(normalizedInput);
            return true;
        } catch (error) {
            console.error('Error creating frame', error);
            return false;
        }
    }),
    update: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frameUpdateSchema"]).mutation(async ({ ctx, input })=>{
        try {
            if (!input.id) {
                throw new Error('Frame ID is required');
            }
            const normalizedInput = {
                ...input,
                type: input.type
            };
            await ctx.db.update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"]).set(normalizedInput).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"].id, input.id));
            return true;
        } catch (error) {
            console.error('Error updating frame', error);
            return false;
        }
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        frameId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        try {
            await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"].id, input.frameId));
            return true;
        } catch (error) {
            console.error('Error deleting frame', error);
            return false;
        }
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/project/invitation.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "invitationRouter": (()=>invitationRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/env.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/supabase/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/defaults/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/invitation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user-project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/email/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/email/src/client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/email/src/invitation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$utility$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/utility/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$utility$2f$src$2f$email$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/utility/src/email.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$url$2d$join$2f$lib$2f$url$2d$join$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/url-join/lib/url-join.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
const invitationRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const invitation = await ctx.db.query.projectInvitations.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].id, input.id)
        });
        if (!invitation) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'NOT_FOUND',
                message: 'Invitation not found'
            });
        }
        const inviter = await ctx.db.query.authUsers.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].id, invitation.inviterId)
        });
        if (!inviter) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'NOT_FOUND',
                message: 'Inviter not found'
            });
        }
        return {
            ...invitation,
            inviter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fromAuthUser"])(inviter)
        };
    }),
    list: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const invitations = await ctx.db.query.projectInvitations.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].projectId, input.projectId)
        });
        return invitations;
    }),
    create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitationInsertSchema"].pick({
        projectId: true,
        inviteeEmail: true,
        role: true
    })).mutation(async ({ ctx, input })=>{
        if (!ctx.user.id) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'UNAUTHORIZED',
                message: 'You must be logged in to invite a user'
            });
        }
        const invitation = await ctx.db.transaction(async (tx)=>{
            const existingUser = await tx.select().from(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"]).innerJoin(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].id, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].userId)).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].projectId, input.projectId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].email, input.inviteeEmail))).limit(1);
            if (existingUser.length > 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: 'CONFLICT',
                    message: 'User is already a member of the project'
                });
            }
            return await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"]).values([
                {
                    ...input,
                    role: input.role,
                    token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                    inviterId: ctx.user.id,
                    expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])().add(7, 'day').toDate()
                }
            ]).returning();
        }).then(([invitation])=>invitation);
        if (invitation) {
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].RESEND_API_KEY) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'RESEND_API_KEY is not set, cannot send email'
                });
            }
            const emailClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getResendClient"])({
                apiKey: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].RESEND_API_KEY
            });
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$src$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sendInvitationEmail"])(emailClient, {
                invitedByEmail: ctx.user.email,
                inviteLink: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$url$2d$join$2f$lib$2f$url$2d$join$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NEXT_PUBLIC_SITE_URL, 'invitation', invitation.id, new URLSearchParams([
                    [
                        'token',
                        invitation.token
                    ]
                ]).toString())
            }, {
                dryRun: ("TURBOPACK compile-time value", "development") !== 'production'
            });
        }
        return invitation;
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].id, input.id));
        return true;
    }),
    accept: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        token: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        if (!ctx.user.id) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'UNAUTHORIZED',
                message: 'You must be logged in to accept an invitation'
            });
        }
        const invitation = await ctx.db.query.projectInvitations.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].id, input.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].token, input.token), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].inviteeEmail, ctx.user.email)),
            with: {
                project: {
                    with: {
                        canvas: true
                    }
                }
            }
        });
        if (!invitation || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])().isAfter((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(invitation.expiresAt))) {
            if (invitation) {
                await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].id, invitation.id));
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'BAD_REQUEST',
                message: 'Invitation does not exist or has expired'
            });
        }
        await ctx.db.transaction(async (tx)=>{
            await tx.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].id, invitation.id));
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"]).values({
                projectId: invitation.projectId,
                userId: ctx.user.id,
                role: invitation.role
            }).onConflictDoNothing();
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"]).values((0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultUserCanvas"])(ctx.user.id, invitation.project.canvas.id)).onConflictDoNothing();
        });
    }),
    suggested: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$utility$2f$src$2f$email$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFreeEmail"])(ctx.user.email)) {
            return [];
        }
        const domain = ctx.user.email.split('@').at(-1);
        const suggestedUsers = await ctx.db.select().from(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"]).leftJoin(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].userId, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].projectId, input.projectId))).leftJoin(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].inviteeEmail, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].email), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].projectId, input.projectId))).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ilike"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].email, `%@${domain}`), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNull"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].userId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNull"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInvitations"].id))).limit(5);
        return suggestedUsers.map((user)=>user.users.email);
    })
});
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/routers/project/member.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "memberRouter": (()=>memberRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/supabase/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user-project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
const memberRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    list: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const members = await ctx.db.select().from(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"]).innerJoin(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].userId, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$supabase$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authUsers"].id)).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].projectId, input.projectId));
        return members.map((member)=>{
            return {
                ...member.user_projects,
                user: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fromAuthUser"])(member.users)
            };
        });
    }),
    remove: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].userId, input.userId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].projectId, input.projectId)));
        return true;
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/project/project.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "projectRouter": (()=>projectRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/canvas/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/defaults/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/defaults/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/defaults/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/canvas/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user-project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/models/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$project$2f$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/models/src/project/role.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
;
const projectRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    getFullProject: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const project = await ctx.db.query.projects.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projects"].id, input.projectId),
            with: {
                canvas: {
                    with: {
                        frames: true,
                        userCanvases: {
                            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"].userId, ctx.user.id)
                        }
                    }
                },
                conversations: {
                    orderBy: (conversations, { desc })=>[
                            desc(conversations.updatedAt)
                        ],
                    limit: 1
                }
            }
        });
        if (!project) {
            console.error('project not found');
            return null;
        }
        const canvas = project.canvas ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultCanvas"])(project.id);
        const userCanvas = project.canvas?.userCanvases[0] ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultUserCanvas"])(ctx.user.id, canvas.id);
        return {
            project: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toProject"])(project),
            userCanvas: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toCanvas"])(userCanvas),
            frames: project.canvas?.frames.map(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toFrame"]) ?? []
        };
    }),
    create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        project: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInsertSchema"],
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        return await ctx.db.transaction(async (tx)=>{
            // 1. Insert the new project
            const [newProject] = await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projects"]).values(input.project).returning();
            if (!newProject) {
                throw new Error('Failed to create project in database');
            }
            // 2. Create the association in the junction table
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"]).values({
                userId: input.userId,
                projectId: newProject.id,
                role: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$project$2f$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ProjectRole"].OWNER
            });
            // 3. Create the default canvas
            const newCanvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultCanvas"])(newProject.id);
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvases"]).values(newCanvas);
            const newUserCanvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultUserCanvas"])(input.userId, newCanvas.id);
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"]).values(newUserCanvas);
            // 4. Create the default frame
            const newFrame = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultFrame"])(newCanvas.id, input.project.sandboxUrl);
            await tx.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frames"]).values(newFrame);
            return newProject;
        });
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        await ctx.db.transaction(async (tx)=>{
            await tx.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projects"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projects"].id, input.id));
            await tx.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].projectId, input.id));
        });
    }),
    getPreviewProjects: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const projects = await ctx.db.query.userProjects.findMany({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userProjects"].userId, input.userId),
            with: {
                project: true
            }
        });
        return projects.map((project)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toProject"])(project.project));
    }),
    update: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectInsertSchema"]).mutation(async ({ ctx, input })=>{
        if (!input.id) {
            throw new Error('Project ID is required');
        }
        await ctx.db.update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projects"]).set(input).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projects"].id, input.id));
    })
});
}}),
"[project]/apps/web/client/src/components/store/editor/sandbox/helpers.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "formatContent": (()=>formatContent),
    "normalizePath": (()=>normalizePath)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/constants/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$files$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/constants/src/files.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$estree__$5b$external$5d$__$28$prettier$2f$plugins$2f$estree$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/prettier/plugins/estree [external] (prettier/plugins/estree, esm_import)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$typescript__$5b$external$5d$__$28$prettier$2f$plugins$2f$typescript$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/prettier/plugins/typescript [external] (prettier/plugins/typescript, esm_import)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$standalone__$5b$external$5d$__$28$prettier$2f$standalone$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/prettier/standalone [external] (prettier/standalone, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$estree__$5b$external$5d$__$28$prettier$2f$plugins$2f$estree$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$typescript__$5b$external$5d$__$28$prettier$2f$plugins$2f$typescript$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$standalone__$5b$external$5d$__$28$prettier$2f$standalone$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$estree__$5b$external$5d$__$28$prettier$2f$plugins$2f$estree$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$typescript__$5b$external$5d$__$28$prettier$2f$plugins$2f$typescript$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$standalone__$5b$external$5d$__$28$prettier$2f$standalone$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
const SANDBOX_ROOT = '/project/sandbox';
function normalizePath(p) {
    let abs = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].isAbsolute(p) ? p : __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(SANDBOX_ROOT, p);
    let relative = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].relative(SANDBOX_ROOT, abs);
    return relative.replace(/\\/g, '/'); // Always POSIX style
}
async function formatContent(filePath, content) {
    try {
        // Only format if the file is a .ts or .tsx file
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$files$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JSX_FILE_EXTENSIONS"].includes(extension) && !__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$files$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JS_FILE_EXTENSIONS"].includes(extension)) {
            console.log('Skipping formatting for non-TS/TSX file:', filePath);
            return content;
        }
        // Use browser standalone version with necessary plugins
        const formattedContent = await __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$standalone__$5b$external$5d$__$28$prettier$2f$standalone$2c$__esm_import$29$__["default"].format(content, {
            filepath: filePath,
            plugins: [
                __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$estree__$5b$external$5d$__$28$prettier$2f$plugins$2f$estree$2c$__esm_import$29$__["default"],
                __TURBOPACK__imported__module__$5b$externals$5d2f$prettier$2f$plugins$2f$typescript__$5b$external$5d$__$28$prettier$2f$plugins$2f$typescript$2c$__esm_import$29$__["default"]
            ],
            parser: 'typescript'
        });
        return formattedContent;
    } catch (error) {
        console.error('Error formatting file:', error);
        return content;
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/components/store/editor/pages/helper.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "createPageInSandbox": (()=>createPageInSandbox),
    "deletePageInSandbox": (()=>deletePageInSandbox),
    "doesRouteExist": (()=>doesRouteExist),
    "duplicatePageInSandbox": (()=>duplicatePageInSandbox),
    "injectPreloadScript": (()=>injectPreloadScript),
    "normalizeRoute": (()=>normalizeRoute),
    "parseRepoUrl": (()=>parseRepoUrl),
    "renamePageInSandbox": (()=>renamePageInSandbox),
    "scanPagesFromSandbox": (()=>scanPagesFromSandbox),
    "updatePageMetadataInSandbox": (()=>updatePageMetadataInSandbox),
    "validateNextJsRoute": (()=>validateNextJsRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/parser/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/parser/src/packages.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/nanoid/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$sandbox$2f$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/components/store/editor/sandbox/helpers.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$sandbox$2f$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$sandbox$2f$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
const DEFAULT_LAYOUT_CONTENT = `export default function Layout({
    children,
}: {
    children: React.ReactNode;
}) {
    return <>{children}</>;
}`;
const normalizeRoute = (route)=>{
    return route.replace(/\\/g, '/') // Replace backslashes with forward slashes
    .replace(/\/+/g, '/') // Replace multiple slashes with single slash
    .replace(/^\/|\/$/g, '') // Remove leading and trailing slashes
    .toLowerCase(); // Ensure lowercase
};
const validateNextJsRoute = (route)=>{
    if (!route) {
        return {
            valid: false,
            error: 'Page name is required'
        };
    }
    // Checks if it's a dynamic route
    const hasMatchingBrackets = /\[[^\]]*\]/.test(route);
    if (hasMatchingBrackets) {
        const dynamicRegex = /^\[([a-z0-9-]+)\]$/;
        if (!dynamicRegex.test(route)) {
            return {
                valid: false,
                error: 'Invalid dynamic route format. Example: [id] or [blog]'
            };
        }
        return {
            valid: true
        };
    }
    // For regular routes, allow lowercase letters, numbers, and hyphens
    const validCharRegex = /^[a-z0-9-]+$/;
    if (!validCharRegex.test(route)) {
        return {
            valid: false,
            error: 'Page name can only contain lowercase letters, numbers, and hyphens'
        };
    }
    return {
        valid: true
    };
};
const doesRouteExist = (nodes, route)=>{
    const normalizedRoute = normalizeRoute(route);
    const checkNode = (nodes)=>{
        for (const node of nodes){
            if (normalizeRoute(node.path) === normalizedRoute) {
                return true;
            }
            if (Array.isArray(node.children) && node.children.length > 0 && checkNode(node.children)) {
                return true;
            }
        }
        return false;
    };
    return checkNode(nodes);
};
const IGNORED_DIRECTORIES = [
    'api',
    'components',
    'lib',
    'utils',
    'node_modules'
];
const APP_ROUTER_PATHS = [
    'src/app',
    'app'
];
const PAGES_ROUTER_PATHS = [
    'src/pages',
    'pages'
];
const ALLOWED_EXTENSIONS = [
    '.tsx',
    '.ts',
    '.jsx',
    '.js'
];
const ROOT_PAGE_NAME = 'Home';
const ROOT_PATH_IDENTIFIERS = [
    '',
    '/',
    '.'
];
const ROOT_PAGE_COPY_NAME = 'landing-page-copy';
const DEFAULT_PAGE_CONTENT = `export default function Page() {
    return (
        <div className="w-full min-h-screen flex items-center justify-center bg-white dark:bg-black transition-colors duration-200 flex-col p-4 gap-[32px]">
            <div className="text-center text-gray-900 dark:text-gray-100 p-4">
                <h1 className="text-4xl md:text-5xl font-semibold mb-4 tracking-tight">
                    This is a blank page
                </h1>
            </div>
        </div>
    );
}
`;
const getFileExtension = (fileName)=>{
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot) : '';
};
const getBaseName = (filePath)=>{
    const parts = filePath.replace(/\\/g, '/').split('/');
    return parts[parts.length - 1] || '';
};
const getDirName = (filePath)=>{
    const parts = filePath.replace(/\\/g, '/').split('/');
    return parts.slice(0, -1).join('/');
};
const joinPath = (...parts)=>{
    return parts.filter(Boolean).join('/').replace(/\/+/g, '/');
};
// Helper function to extract metadata from file content
const extractMetadata = async (content)=>{
    try {
        const ast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(content, {
            sourceType: 'module',
            plugins: [
                'typescript',
                'jsx'
            ]
        });
        let metadata;
        // Helper functions for AST traversal
        const extractObjectValue = (obj)=>{
            const result = {};
            for (const prop of obj.properties){
                if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isObjectProperty(prop) && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isIdentifier(prop.key)) {
                    const key = prop.key.name;
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isStringLiteral(prop.value)) {
                        result[key] = prop.value.value;
                    } else if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isObjectExpression(prop.value)) {
                        result[key] = extractObjectValue(prop.value);
                    } else if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isArrayExpression(prop.value)) {
                        result[key] = extractArrayValue(prop.value);
                    }
                }
            }
            return result;
        };
        const extractArrayValue = (arr)=>{
            return arr.elements.map((element)=>{
                if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isStringLiteral(element)) {
                    return element.value;
                } else if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isObjectExpression(element)) {
                    return extractObjectValue(element);
                } else if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isArrayExpression(element)) {
                    return extractArrayValue(element);
                }
                return null;
            }).filter(Boolean);
        };
        // Traverse the AST to find metadata export
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["traverse"])(ast, {
            ExportNamedDeclaration (path) {
                const declaration = path.node.declaration;
                if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isVariableDeclaration(declaration)) {
                    const declarator = declaration.declarations[0];
                    if (declarator && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isIdentifier(declarator.id) && declarator.id.name === 'metadata' && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isObjectExpression(declarator.init)) {
                        metadata = {};
                        // Extract properties from the object expression
                        for (const prop of declarator.init.properties){
                            if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isObjectProperty(prop) && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isIdentifier(prop.key)) {
                                const key = prop.key.name;
                                if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isStringLiteral(prop.value)) {
                                    metadata[key] = prop.value.value;
                                } else if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isObjectExpression(prop.value)) {
                                    metadata[key] = extractObjectValue(prop.value);
                                } else if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isArrayExpression(prop.value)) {
                                    metadata[key] = extractArrayValue(prop.value);
                                }
                            }
                        }
                    }
                }
            }
        });
        return metadata;
    } catch (error) {
        console.error(`Error reading metadata:`, error);
        return undefined;
    }
};
const scanAppDirectory = async (session, dir, parentPath = '')=>{
    const nodes = [];
    let entries;
    try {
        entries = await session.fs.readdir(dir);
    } catch (error) {
        console.error(`Error reading directory ${dir}:`, error);
        return nodes;
    }
    // Handle page files
    const pageFile = entries.find((entry)=>entry.type === 'file' && entry.name.startsWith('page.') && ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)));
    if (pageFile) {
        const currentDir = getBaseName(dir);
        const isDynamicRoute = currentDir.startsWith('[') && currentDir.endsWith(']');
        let cleanPath;
        if (isDynamicRoute) {
            const paramName = currentDir;
            cleanPath = parentPath ? joinPath(getDirName(parentPath), paramName) : '/' + paramName;
        } else {
            cleanPath = parentPath ? `/${parentPath}` : '/';
        }
        // Normalize path and ensure leading slash & no trailing slash
        cleanPath = '/' + cleanPath.replace(/^\/|\/$/g, '');
        const isRoot = ROOT_PATH_IDENTIFIERS.includes(cleanPath);
        // Extract metadata from both page and layout files
        let pageMetadata;
        try {
            const pageContent = await session.fs.readTextFile(`${dir}/${pageFile.name}`);
            pageMetadata = await extractMetadata(pageContent);
        } catch (error) {
            console.error(`Error reading page file ${dir}/${pageFile.name}:`, error);
        }
        // Look for layout file in the same directory
        const layoutFile = entries.find((entry)=>entry.type === 'file' && entry.name.startsWith('layout.') && ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)));
        let layoutMetadata;
        if (layoutFile) {
            try {
                const layoutContent = await session.fs.readTextFile(`${dir}/${layoutFile.name}`);
                layoutMetadata = await extractMetadata(layoutContent);
            } catch (error) {
                console.error(`Error reading layout file ${dir}/${layoutFile.name}:`, error);
            }
        }
        // Merge metadata, with page metadata taking precedence over layout metadata
        const metadata = {
            ...layoutMetadata,
            ...pageMetadata
        };
        nodes.push({
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(),
            name: isDynamicRoute ? currentDir : parentPath ? getBaseName(parentPath) : ROOT_PAGE_NAME,
            path: cleanPath,
            children: [],
            isActive: false,
            isRoot,
            metadata: metadata || {}
        });
    }
    // Handle directories
    for (const entry of entries){
        if (IGNORED_DIRECTORIES.includes(entry.name)) {
            continue;
        }
        const fullPath = `${dir}/${entry.name}`;
        const relativePath = joinPath(parentPath, entry.name);
        if (entry.type === 'directory') {
            const children = await scanAppDirectory(session, fullPath, relativePath);
            if (children.length > 0) {
                const dirPath = relativePath.replace(/\\/g, '/');
                const cleanPath = '/' + dirPath.replace(/^\/|\/$/g, '');
                nodes.push({
                    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(),
                    name: entry.name,
                    path: cleanPath,
                    children,
                    isActive: false,
                    isRoot: false,
                    metadata: {}
                });
            }
        }
    }
    return nodes;
};
const scanPagesDirectory = async (session, dir, parentPath = '')=>{
    const nodes = [];
    let entries;
    try {
        entries = await session.fs.readdir(dir);
    } catch (error) {
        console.error(`Error reading directory ${dir}:`, error);
        return nodes;
    }
    // Process files first
    for (const entry of entries){
        const fileName = entry.name?.split('.')[0];
        if (!fileName) {
            console.error(`Error reading file ${entry.name}`);
            continue;
        }
        if (entry.type === 'file' && ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)) && !IGNORED_DIRECTORIES.includes(fileName)) {
            const isDynamicRoute = fileName.startsWith('[') && fileName.endsWith(']');
            let cleanPath;
            if (fileName === 'index') {
                cleanPath = parentPath ? `/${parentPath}` : '/';
            } else {
                if (isDynamicRoute) {
                    const paramName = fileName.slice(1, -1);
                    cleanPath = joinPath(parentPath, paramName);
                } else {
                    cleanPath = joinPath(parentPath, fileName);
                }
                // Normalize path
                cleanPath = '/' + cleanPath.replace(/\\/g, '/').replace(/^\/|\/$/g, '');
            }
            const isRoot = ROOT_PATH_IDENTIFIERS.includes(cleanPath);
            // Extract metadata from the page file
            let metadata;
            try {
                const fileContent = await session.fs.readTextFile(`${dir}/${entry.name}`);
                metadata = await extractMetadata(fileContent);
            } catch (error) {
                console.error(`Error reading file ${dir}/${entry.name}:`, error);
            }
            nodes.push({
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(),
                name: fileName === 'index' ? parentPath ? `/${getBaseName(parentPath)}` : ROOT_PAGE_NAME : '/' + fileName,
                path: cleanPath,
                children: [],
                isActive: false,
                isRoot,
                metadata: metadata || {}
            });
        }
    }
    // Process directories
    for (const entry of entries){
        if (IGNORED_DIRECTORIES.includes(entry.name)) {
            continue;
        }
        const fullPath = `${dir}/${entry.name}`;
        const isDynamicDir = entry.name.startsWith('[') && entry.name.endsWith(']');
        const dirNameForPath = isDynamicDir ? entry.name.slice(1, -1) : entry.name;
        const relativePath = joinPath(parentPath, dirNameForPath);
        if (entry.type === 'directory') {
            const children = await scanPagesDirectory(session, fullPath, relativePath);
            if (children.length > 0) {
                const dirPath = relativePath.replace(/\\/g, '/');
                const cleanPath = '/' + dirPath.replace(/^\/|\/$/g, '');
                nodes.push({
                    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(),
                    name: entry.name,
                    path: cleanPath,
                    children,
                    isActive: false,
                    isRoot: false,
                    metadata: {}
                });
            }
        }
    }
    return nodes;
};
const scanPagesFromSandbox = async (session)=>{
    if (!session) {
        throw new Error('No sandbox session available');
    }
    // Detect router configuration
    let routerConfig = null;
    // Check for App Router first (Next.js 13+)
    for (const appPath of APP_ROUTER_PATHS){
        try {
            const entries = await session.fs.readdir(appPath);
            if (entries && entries.length > 0) {
                console.log(`Found App Router at: ${appPath}`);
                routerConfig = {
                    type: 'app',
                    basePath: appPath
                };
                break;
            }
        } catch (error) {
        // Directory doesn't exist, continue checking
        }
    }
    // Check for Pages Router if App Router not found
    if (!routerConfig) {
        for (const pagesPath of PAGES_ROUTER_PATHS){
            try {
                const entries = await session.fs.readdir(pagesPath);
                if (entries && entries.length > 0) {
                    console.log(`Found Pages Router at: ${pagesPath}`);
                    routerConfig = {
                        type: 'pages',
                        basePath: pagesPath
                    };
                    break;
                }
            } catch (error) {
            // Directory doesn't exist, continue checking
            }
        }
    }
    if (!routerConfig) {
        console.log('No Next.js router detected, returning empty pages');
        return [];
    }
    if (routerConfig.type === 'app') {
        return await scanAppDirectory(session, routerConfig.basePath);
    } else {
        return await scanPagesDirectory(session, routerConfig.basePath);
    }
};
const detectRouterTypeInSandbox = async (session)=>{
    // Check for App Router
    for (const appPath of APP_ROUTER_PATHS){
        try {
            const entries = await session.fs.readdir(appPath);
            if (entries && entries.length > 0) {
                // Check for layout file (required for App Router)
                const hasLayout = entries.some((entry)=>entry.type === 'file' && entry.name.startsWith('layout.') && ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)));
                if (hasLayout) {
                    console.log(`Found App Router at: ${appPath}`);
                    return {
                        type: 'app',
                        basePath: appPath
                    };
                }
            }
        } catch (error) {
        // Directory doesn't exist, continue checking
        }
    }
    // Check for Pages Router if App Router not found
    for (const pagesPath of PAGES_ROUTER_PATHS){
        try {
            const entries = await session.fs.readdir(pagesPath);
            if (entries && entries.length > 0) {
                // Check for index file (common in Pages Router)
                const hasIndex = entries.some((entry)=>entry.type === 'file' && entry.name.startsWith('index.') && ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)));
                if (hasIndex) {
                    console.log(`Found Pages Router at: ${pagesPath}`);
                    return {
                        type: 'pages',
                        basePath: pagesPath
                    };
                }
            }
        } catch (error) {
        // Directory doesn't exist, continue checking
        }
    }
    return null;
};
// checks if file/directory exists
const pathExists = async (session, filePath)=>{
    try {
        await session.fs.readdir(getDirName(filePath));
        const dirEntries = await session.fs.readdir(getDirName(filePath));
        const fileName = getBaseName(filePath);
        return dirEntries.some((entry)=>entry.name === fileName);
    } catch (error) {
        return false;
    }
};
const cleanupEmptyFolders = async (session, folderPath)=>{
    while(folderPath && folderPath !== getDirName(folderPath)){
        try {
            const entries = await session.fs.readdir(folderPath);
            if (entries.length === 0) {
                // Delete empty directory using remove method
                await session.fs.remove(folderPath);
                folderPath = getDirName(folderPath);
            } else {
                break;
            }
        } catch (error) {
            break;
        }
    }
};
const getUniqueDir = async (session, basePath, dirName, maxAttempts = 100)=>{
    let uniquePath = dirName;
    let counter = 1;
    const baseName = dirName.replace(/-copy(-\d+)?$/, '');
    while(counter <= maxAttempts){
        const fullPath = joinPath(basePath, uniquePath);
        if (!await pathExists(session, fullPath)) {
            return uniquePath;
        }
        uniquePath = `${baseName}-copy-${counter}`;
        counter++;
    }
    throw new Error(`Unable to find available directory name for ${dirName}`);
};
const createDirectory = async (session, dirPath)=>{
    // Creates a temporary file to ensure directory structure exists, then remove it
    const tempFile = joinPath(dirPath, '.temp');
    await session.fs.writeTextFile(tempFile, '');
    await session.fs.remove(tempFile);
};
const createPageInSandbox = async (session, pagePath)=>{
    try {
        const routerConfig = await detectRouterTypeInSandbox(session);
        if (!routerConfig) {
            throw new Error('Could not detect Next.js router type');
        }
        if (routerConfig.type !== 'app') {
            throw new Error('Page creation is only supported for App Router projects.');
        }
        // Validate and normalize the path
        const normalizedPagePath = pagePath.replace(/\/+/g, '/').replace(/^\/|\/$/g, '');
        if (!/^[a-zA-Z0-9\-_[\]()/]+$/.test(normalizedPagePath)) {
            throw new Error('Page path contains invalid characters');
        }
        const fullPath = joinPath(routerConfig.basePath, normalizedPagePath);
        const pageFilePath = joinPath(fullPath, 'page.tsx');
        if (await pathExists(session, pageFilePath)) {
            throw new Error('Page already exists at this path');
        }
        await session.fs.writeTextFile(pageFilePath, DEFAULT_PAGE_CONTENT);
        console.log(`Created page at: ${pageFilePath}`);
    } catch (error) {
        console.error('Error creating page:', error);
        throw error;
    }
};
const deletePageInSandbox = async (session, pagePath, isDir)=>{
    try {
        const routerConfig = await detectRouterTypeInSandbox(session);
        if (!routerConfig) {
            throw new Error('Could not detect Next.js router type');
        }
        if (routerConfig.type !== 'app') {
            throw new Error('Page deletion is only supported for App Router projects.');
        }
        const normalizedPath = pagePath.replace(/\/+/g, '/').replace(/^\/|\/$/g, '');
        if (normalizedPath === '' || normalizedPath === '/') {
            throw new Error('Cannot delete root page');
        }
        const fullPath = joinPath(routerConfig.basePath, normalizedPath);
        if (!await pathExists(session, fullPath)) {
            throw new Error('Selected page not found');
        }
        if (isDir) {
            // Delete entire directory
            await session.fs.remove(fullPath, true);
        } else {
            // Delete just the page.tsx file
            const pageFilePath = joinPath(fullPath, 'page.tsx');
            await session.fs.remove(pageFilePath);
            // Clean up empty parent directories
            await cleanupEmptyFolders(session, fullPath);
        }
        console.log(`Deleted: ${fullPath}`);
    } catch (error) {
        console.error('Error deleting page:', error);
        throw error;
    }
};
const renamePageInSandbox = async (session, oldPath, newName)=>{
    try {
        const routerConfig = await detectRouterTypeInSandbox(session);
        if (!routerConfig || routerConfig.type !== 'app') {
            throw new Error('Page renaming is only supported for App Router projects.');
        }
        if (ROOT_PATH_IDENTIFIERS.includes(oldPath)) {
            throw new Error('Cannot rename root page');
        }
        // Validate new name
        if (!/^[a-zA-Z0-9\-_[\]()]+$/.test(newName)) {
            throw new Error('Page name contains invalid characters');
        }
        const normalizedOldPath = oldPath.replace(/\/+/g, '/').replace(/^\/|\/$/g, '');
        const oldFullPath = joinPath(routerConfig.basePath, normalizedOldPath);
        const parentDir = getDirName(oldFullPath);
        const newFullPath = joinPath(parentDir, newName);
        if (!await pathExists(session, oldFullPath)) {
            throw new Error(`Source page not found: ${oldFullPath}`);
        }
        if (await pathExists(session, newFullPath)) {
            throw new Error(`Target path already exists: ${newFullPath}`);
        }
        await session.fs.rename(oldFullPath, newFullPath);
        console.log(`Renamed page from ${oldFullPath} to ${newFullPath}`);
    } catch (error) {
        console.error('Error renaming page:', error);
        throw error;
    }
};
const duplicatePageInSandbox = async (session, sourcePath, targetPath)=>{
    try {
        const routerConfig = await detectRouterTypeInSandbox(session);
        if (!routerConfig || routerConfig.type !== 'app') {
            throw new Error('Page duplication is only supported for App Router projects.');
        }
        // Handle root path case
        const isRootPath = ROOT_PATH_IDENTIFIERS.includes(sourcePath);
        if (isRootPath) {
            const sourcePageFile = joinPath(routerConfig.basePath, 'page.tsx');
            const targetDir = await getUniqueDir(session, routerConfig.basePath, ROOT_PAGE_COPY_NAME);
            const targetDirPath = joinPath(routerConfig.basePath, targetDir);
            const targetPageFile = joinPath(targetDirPath, 'page.tsx');
            if (await pathExists(session, targetDirPath)) {
                throw new Error('Target path already exists');
            }
            await session.fs.copy(sourcePageFile, targetPageFile);
            console.log(`Duplicated root page to: ${targetPageFile}`);
            return;
        }
        // Handle non-root pages
        const normalizedSourcePath = sourcePath.replace(/\/+/g, '/').replace(/^\/|\/$/g, '');
        const normalizedTargetPath = await getUniqueDir(session, routerConfig.basePath, targetPath);
        const sourceFull = joinPath(routerConfig.basePath, normalizedSourcePath);
        const targetFull = joinPath(routerConfig.basePath, normalizedTargetPath);
        if (await pathExists(session, targetFull)) {
            throw new Error('Target path already exists');
        }
        // Check if source is a directory or file
        const sourceEntries = await session.fs.readdir(getDirName(sourceFull));
        const sourceEntry = sourceEntries.find((entry)=>entry.name === getBaseName(sourceFull));
        if (!sourceEntry) {
            throw new Error('Source page not found');
        }
        await session.fs.copy(sourceFull, targetFull, true);
        console.log(`Duplicated page from ${sourceFull} to ${targetFull}`);
    } catch (error) {
        console.error('Error duplicating page:', error);
        throw error;
    }
};
const updatePageMetadataInSandbox = async (session, pagePath, metadata)=>{
    const routerConfig = await detectRouterTypeInSandbox(session);
    if (!routerConfig) {
        throw new Error('Could not detect Next.js router type');
    }
    if (routerConfig.type !== 'app') {
        throw new Error('Metadata update is only supported for App Router projects for now.');
    }
    const fullPath = joinPath(routerConfig.basePath, pagePath);
    const pageFilePath = joinPath(fullPath, 'page.tsx');
    // check if page.tsx exists
    const pageExists = await pathExists(session, pageFilePath);
    if (!pageExists) {
        throw new Error('Page not found');
    }
    const pageContent = await session.fs.readTextFile(pageFilePath);
    const hasUseClient = pageContent.includes("'use client'") || pageContent.includes('"use client"');
    if (hasUseClient) {
        // check if layout.tsx exists
        const layoutFilePath = joinPath(fullPath, 'layout.tsx');
        const layoutExists = await pathExists(session, layoutFilePath);
        if (layoutExists) {
            await updateMetadataInFile(session, layoutFilePath, metadata);
        } else {
            // create layout.tsx
            // Create new layout file with metadata
            const layoutContent = `import type { Metadata } from 'next';\n\nexport const metadata: Metadata = ${JSON.stringify(metadata, null, 2)};\n\n${DEFAULT_LAYOUT_CONTENT}`;
            await session.fs.writeTextFile(layoutFilePath, layoutContent);
        }
    } else {
        await updateMetadataInFile(session, pageFilePath, metadata);
    }
};
async function updateMetadataInFile(session, filePath, metadata) {
    // Read the current file content
    const content = await session.fs.readTextFile(filePath);
    // Parse the file content using Babel
    const ast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(content, {
        sourceType: 'module',
        plugins: [
            'typescript',
            'jsx'
        ]
    });
    let hasMetadataImport = false;
    let metadataNode = null;
    // Traverse the AST to find metadata import and export
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["traverse"])(ast, {
        ImportDeclaration (path) {
            if (path.node.source.value === 'next' && path.node.specifiers.some((spec)=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isImportSpecifier(spec) && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isIdentifier(spec.imported) && spec.imported.name === 'Metadata')) {
                hasMetadataImport = true;
            }
        },
        ExportNamedDeclaration (path) {
            const declaration = path.node.declaration;
            if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isVariableDeclaration(declaration)) {
                const declarator = declaration.declarations[0];
                if (declarator && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isIdentifier(declarator.id) && declarator.id.name === 'metadata') {
                    metadataNode = path.node;
                }
            }
        }
    });
    // Add Metadata import if not present
    if (!hasMetadataImport) {
        const metadataImport = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].importDeclaration([
            __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].importSpecifier(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier('Metadata'), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier('Metadata'))
        ], __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral('next'));
        ast.program.body.unshift(metadataImport);
    }
    // Create metadata object expression
    const metadataObject = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectExpression(Object.entries(metadata).map(([key, value])=>{
        if (typeof value === 'string') {
            if (key === 'metadataBase') {
                return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(key), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].newExpression(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier('URL'), [
                    __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(value)
                ]));
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(key), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(value));
        } else if (value === null) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(key), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].nullLiteral());
        } else if (Array.isArray(value)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(key), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].arrayExpression(value.map((v)=>{
                if (typeof v === 'string') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(v);
                } else if (typeof v === 'object' && v !== null) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectExpression(Object.entries(v).map(([k, val])=>{
                        if (typeof val === 'string') {
                            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(val));
                        } else if (typeof val === 'number') {
                            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].numericLiteral(val));
                        }
                        return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(String(val)));
                    }));
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(String(v));
            })));
        } else if (typeof value === 'object' && value !== null) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(key), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectExpression(Object.entries(value).map(([k, v])=>{
                if (typeof v === 'string') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(v));
                } else if (typeof v === 'number') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].numericLiteral(v));
                } else if (Array.isArray(v)) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].arrayExpression(v.map((item)=>{
                        if (typeof item === 'string') {
                            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(item);
                        } else if (typeof item === 'object' && item !== null) {
                            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectExpression(Object.entries(item).map(([ik, iv])=>{
                                if (typeof iv === 'string') {
                                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(ik), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(iv));
                                } else if (typeof iv === 'number') {
                                    return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(ik), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].numericLiteral(iv));
                                }
                                return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(ik), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(String(iv)));
                            }));
                        }
                        return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(String(item));
                    })));
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(k), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(String(v)));
            })));
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].objectProperty(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier(key), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(String(value)));
    }));
    // Create metadata variable declaration
    const metadataVarDecl = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].variableDeclaration('const', [
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].variableDeclarator(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier('metadata'), metadataObject)
    ]);
    // Add type annotation
    const metadataTypeAnnotation = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].tsTypeAnnotation(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].tsTypeReference(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier('Metadata')));
    (metadataVarDecl.declarations[0]?.id).typeAnnotation = metadataTypeAnnotation;
    // Create metadata export
    const metadataExport = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].exportNamedDeclaration(metadataVarDecl);
    if (metadataNode) {
        // Replace existing metadata export
        const metadataExportIndex = ast.program.body.findIndex((node)=>{
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isExportNamedDeclaration(node) || !__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isVariableDeclaration(node.declaration)) {
                return false;
            }
            const declarator = node.declaration.declarations[0];
            return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isIdentifier(declarator?.id) && declarator.id.name === 'metadata';
        });
        if (metadataExportIndex !== -1) {
            ast.program.body[metadataExportIndex] = metadataExport;
        }
    } else {
        // Find the default export and add metadata before it
        const defaultExportIndex = ast.program.body.findIndex((node)=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isExportDefaultDeclaration(node));
        if (defaultExportIndex === -1) {
            throw new Error('Could not find default export in the file');
        }
        ast.program.body.splice(defaultExportIndex, 0, metadataExport);
    }
    // Generate the updated code
    const { code } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generate"])(ast);
    const formattedContent = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$sandbox$2f$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatContent"])(filePath, code);
    // Write the updated content back to the file
    await session.fs.writeTextFile(filePath, formattedContent);
}
const injectPreloadScript = async (session)=>{
    await addSetupTask(session);
    await updatePackageJson(session);
    // Step 3: Inject script tag
    const routerType = await detectRouterTypeInSandbox(session);
    const preLoadScript = 'https://cdn.jsdelivr.net/gh/onlook-dev/web@latest/apps/web/preload/dist/index.js';
    if (!routerType || routerType.type !== 'app') {
        throw new Error('We currently support only Next.js App projects.');
    }
    const layoutPath = './src/app/layout.tsx';
    const layoutRaw = await session.fs.readFile(layoutPath);
    const layoutSrc = new TextDecoder().decode(layoutRaw);
    const ast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(layoutSrc, {
        sourceType: 'module',
        plugins: [
            'typescript',
            'jsx'
        ]
    });
    let importedScript = false;
    let foundHead = false;
    let alreadyInjected = false;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["traverse"])(ast, {
        ImportDeclaration (path) {
            if (path.node.source.value === 'next/script') {
                importedScript = true;
            }
        },
        JSXElement (path) {
            const opening = path.node.openingElement;
            if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isJSXIdentifier(opening.name, {
                name: 'Script'
            }) && opening.attributes.some((attr)=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isJSXAttribute(attr) && attr.name.name === 'src' && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isStringLiteral(attr.value) && attr.value.value === preLoadScript)) {
                alreadyInjected = true;
            }
            if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isJSXIdentifier(opening.name, {
                name: 'head'
            })) {
                foundHead = true;
                if (!alreadyInjected) {
                    const scriptElement = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxOpeningElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('Script'), [
                        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('type'), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral('module')),
                        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('src'), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(preLoadScript))
                    ], true), null, [], true);
                    // Prepend the script to the <head> children
                    path.node.children.unshift(scriptElement);
                    alreadyInjected = true;
                }
            }
            if (!foundHead && __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].isJSXIdentifier(opening.name, {
                name: 'html'
            })) {
                if (!alreadyInjected) {
                    const scriptInHead = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxOpeningElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('Script'), [
                        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('type'), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral('module')),
                        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('src'), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral(preLoadScript))
                    ], true), null, [], true);
                    const headElement = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxOpeningElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('head'), [], false), __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxClosingElement(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].jsxIdentifier('head')), [
                        scriptInHead
                    ], false);
                    path.node.children.unshift(headElement);
                    foundHead = true;
                    alreadyInjected = true;
                }
            }
        }
    });
    if (!importedScript) {
        ast.program.body.unshift(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].importDeclaration([
            __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].importDefaultSpecifier(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].identifier('Script'))
        ], __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["types"].stringLiteral('next/script')));
    }
    const { code } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generate"])(ast, {}, layoutSrc);
    await session.fs.writeFile(layoutPath, new TextEncoder().encode(code));
};
const addSetupTask = async (session)=>{
    const tasks = {
        setupTasks: [
            'npm install'
        ],
        tasks: {
            dev: {
                name: 'Dev Server',
                command: 'npm run dev',
                preview: {
                    port: 3000
                },
                runAtStart: true
            }
        }
    };
    await session.fs.writeFile('./.codesandbox/tasks.json', new TextEncoder().encode(JSON.stringify(tasks, null, 2)));
};
const updatePackageJson = async (session)=>{
    const pkgRaw = await session.fs.readFile('./package.json');
    const pkgJson = JSON.parse(new TextDecoder().decode(pkgRaw));
    pkgJson.scripts = pkgJson.scripts || {};
    pkgJson.scripts.dev = 'next dev';
    await session.fs.writeFile('./package.json', new TextEncoder().encode(JSON.stringify(pkgJson, null, 2)));
};
const parseRepoUrl = (repoUrl)=>{
    const match = repoUrl.match(/github\.com\/([^/]+)\/([^/]+)(?:\.git)?/);
    if (!match || !match[1] || !match[2]) {
        throw new Error('Invalid GitHub URL');
    }
    return {
        owner: match[1],
        repo: match[2]
    };
};
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/routers/project/sandbox.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "sandboxRouter": (()=>sandboxRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$pages$2f$helper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/components/store/editor/pages/helper.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/env.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codesandbox$2f$sdk$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codesandbox/sdk/dist/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/constants/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$csb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/constants/src/csb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/parser/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/parser/src/packages.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$code$2d$edit$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/parser/src/code-edit/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$utility$2f$src$2f$id$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/utility/src/id.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$pages$2f$helper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$pages$2f$helper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
const sdk = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codesandbox$2f$sdk$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeSandbox"](__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].CSB_API_KEY);
const sandboxRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    start: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        sandboxId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    })).mutation(async ({ input })=>{
        const startData = await sdk.sandboxes.resume(input.sandboxId);
        const session = await startData.createBrowserSession({
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$utility$2f$src$2f$id$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shortenUuid"])(input.userId ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(), 20)
        });
        return session;
    }),
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        sandboxId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    })).query(async ({ input })=>{
        const startData = await sdk.sandboxes.resume(input.sandboxId);
        const session = await startData.createBrowserSession({
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$utility$2f$src$2f$id$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shortenUuid"])(input.userId ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(), 20)
        });
        return session;
    }),
    hibernate: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        sandboxId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ input })=>{
        await sdk.sandboxes.hibernate(input.sandboxId);
    }),
    list: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ()=>{
        const listResponse = await sdk.sandboxes.list();
        return listResponse;
    }),
    fork: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        sandbox: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            port: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
        })
    })).mutation(async ({ input })=>{
        const sandbox = await sdk.sandboxes.create({
            source: 'template',
            id: input.sandbox.id
        });
        const previewUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$csb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSandboxPreviewUrl"])(sandbox.id, input.sandbox.port);
        return {
            sandboxId: sandbox.id,
            previewUrl
        };
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        sandboxId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ input })=>{
        await sdk.sandboxes.shutdown(input.sandboxId);
    }),
    uploadProject: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        files: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].record(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            isBinary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional()
        })),
        projectName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
    })).mutation(async ({ input })=>{
        let session = null;
        let templateSandbox = null;
        try {
            const template = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$csb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SandboxTemplates"][__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$csb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Templates"].BLANK];
            templateSandbox = await sdk.sandboxes.create({
                source: 'template',
                id: template.id
            });
            session = await templateSandbox.connect();
            // Upload all project files
            for (const [path, file] of Object.entries(input.files)){
                try {
                    // Handle binary vs text files using the correct SDK methods
                    if (file.isBinary) {
                        // For binary files, convert base64 string back to Uint8Array
                        const binaryData = Uint8Array.from(atob(file.content), (c)=>c.charCodeAt(0));
                        await session.fs.writeFile(path, binaryData, {
                            overwrite: true
                        });
                    } else {
                        // For text files, use writeTextFile
                        let content = file.content;
                        // Add script config to the file 'app/layout.tsx' or 'src/app/layout.tsx'
                        if (path === 'app/layout.tsx' || path === 'src/app/layout.tsx') {
                            try {
                                const ast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(content, {
                                    sourceType: 'module',
                                    plugins: [
                                        'jsx',
                                        'typescript'
                                    ]
                                });
                                const modifiedAst = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$code$2d$edit$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addScriptConfig"])(ast);
                                content = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$parser$2f$src$2f$packages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generate"])(modifiedAst, {}, content).code;
                            } catch (parseError) {
                                console.warn('Failed to add script config to layout.tsx:', parseError);
                            }
                        }
                        await session.fs.writeTextFile(path, content, {
                            overwrite: true
                        });
                    }
                } catch (fileError) {
                    console.error(`Error uploading file ${path}:`, fileError);
                    throw new Error(`Failed to upload file: ${path} - ${fileError instanceof Error ? fileError.message : 'Unknown error'}`);
                }
            }
            // Run setup task
            await session.setup.run();
            // Start the dev task
            const task = await session.tasks.get(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$csb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CSB_PREVIEW_TASK_NAME"]);
            if (task) {
                await task.run();
            }
            // Disconnect the session
            try {
                await session.disconnect();
                console.log('Disconnected session');
            } catch (disconnectError) {
                console.error('Error disconnecting session:', disconnectError);
            }
            return {
                sandboxId: templateSandbox.id,
                previewUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$constants$2f$src$2f$csb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSandboxPreviewUrl"])(templateSandbox.id, template.port)
            };
        } catch (error) {
            console.error('Error creating project sandbox:', error);
            if (session) {
                try {
                    await session.disconnect();
                    console.log('Disconnected session during cleanup');
                } catch (cleanupError) {
                    console.error('Error disconnecting session during cleanup:', cleanupError);
                }
            }
            if (templateSandbox?.id) {
                try {
                    await sdk.sandboxes.shutdown(templateSandbox.id);
                    console.log('Cleaned up failed sandbox:', templateSandbox.id);
                } catch (cleanupError) {
                    console.error('Error cleaning up sandbox:', cleanupError);
                }
            }
            throw new Error(`Failed to create project sandbox: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    createFromGitHub: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        repoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        branch: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ input })=>{
        const sandbox = await sdk.sandboxes.create({
            source: 'git',
            url: input.repoUrl,
            branch: input.branch,
            async setup (session) {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$components$2f$store$2f$editor$2f$pages$2f$helper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["injectPreloadScript"])(session);
                await session.setup.run();
            }
        });
        return {
            sandboxId: sandbox.id,
            previewUrl: `https://${sandbox.id}-8084.csb.app`
        };
    })
});
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/routers/project/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$chat$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/chat.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/invitation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$member$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/member.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/sandbox.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/routers/project/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$chat$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/chat.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/invitation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$member$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/member.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/sandbox.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/index.ts [app-route] (ecmascript) <locals>");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/routers/settings.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "settingsRouter": (()=>settingsRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/settings.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$setting$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/setting.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-GEWPoL0C.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
;
const settingsRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const setting = await ctx.db.query.projectSettings.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectSettings"].projectId, input.projectId)
        });
        if (!setting) {
            return null;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$setting$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toProjectSettings"])(setting);
    }),
    upsert: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        settings: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectSettingsInsertSchema"]
    })).mutation(async ({ ctx, input })=>{
        const [updatedSettings] = await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectSettings"]).values(input).onConflictDoUpdate({
            target: [
                __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectSettings"].projectId
            ],
            set: input.settings
        }).returning();
        if (!updatedSettings) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$GEWPoL0C$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to update project settings'
            });
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$setting$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toProjectSettings"])(updatedSettings);
    }),
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        await ctx.db.delete(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectSettings"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectSettings"].projectId, input.projectId));
        return true;
    })
});
}}),
"[project]/apps/web/client/src/utils/constants/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Routes": (()=>Routes)
});
const Routes = {
    HOME: '/',
    LOGIN: '/login',
    PRICING: '/pricing',
    PROJECTS: '/projects',
    PROJECT: '/project',
    IMPORT_PROJECT: '/projects/import',
    CALLBACK_STRIPE_SUCCESS: '/callback/stripe/success',
    CALLBACK_STRIPE_CANCEL: '/callback/stripe/cancel'
};
}}),
"[project]/apps/web/client/src/server/api/routers/subscription/subscription.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "subscriptionRouter": (()=>subscriptionRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$utils$2f$constants$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/utils/constants/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$price$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/subscription/price.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/subscription/subscription.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/subscription.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/stripe/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/stripe/src/functions.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/stripe/src/constants.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
const subscriptionRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ({ ctx })=>{
        const user = ctx.user;
        const subscription = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.subscriptions.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].userId, user.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].status, 'active')),
            with: {
                product: true,
                price: true
            }
        });
        if (!subscription) {
            console.error('No active subscription found for user', user.id);
            return null;
        }
        // If there is a scheduled price, we need to fetch it from the database.
        let scheduledPrice = null;
        if (subscription.scheduledPriceId) {
            scheduledPrice = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.prices.findFirst({
                where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$price$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prices"].id, subscription.scheduledPriceId)
            }) ?? null;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toSubscription"])(subscription, scheduledPrice);
    }),
    getPriceId: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        priceKey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].nativeEnum(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PriceKey"])
    })).mutation(async ({ input })=>{
        const price = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.prices.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$price$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prices"].key, input.priceKey)
        });
        if (!price) {
            throw new Error(`Price not found for key: ${input.priceKey}`);
        }
        return price.stripePriceId;
    }),
    checkout: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        priceId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ ctx, input })=>{
        const originUrl = (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["headers"])()).get('origin');
        const user = ctx.user;
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCheckoutSession"])({
            priceId: input.priceId,
            userId: user.id,
            successUrl: `${originUrl}${__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$utils$2f$constants$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Routes"].CALLBACK_STRIPE_SUCCESS}`,
            cancelUrl: `${originUrl}${__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$utils$2f$constants$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Routes"].CALLBACK_STRIPE_CANCEL}`
        });
        return session;
    }),
    manageSubscription: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].mutation(async ({ ctx })=>{
        const user = ctx.user;
        const subscription = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.subscriptions.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].userId, user.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].status, 'active'))
        });
        if (!subscription) {
            throw new Error('No active subscription found for user');
        }
        const originUrl = (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["headers"])()).get('origin');
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createBillingPortalSession"])({
            customerId: subscription.stripeCustomerId,
            returnUrl: `${originUrl}/subscription/manage`
        });
        return session;
    }),
    update: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        stripeSubscriptionId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        stripeSubscriptionItemId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        stripePriceId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ input })=>{
        const { stripeSubscriptionId, stripeSubscriptionItemId, stripePriceId } = input;
        const subscription = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.subscriptions.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].stripeSubscriptionId, stripeSubscriptionId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].stripeSubscriptionItemId, stripeSubscriptionItemId)),
            with: {
                price: true
            }
        });
        if (!subscription) {
            throw new Error('Subscription not found');
        }
        const currentPrice = subscription.price;
        const newPrice = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.prices.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$price$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prices"].stripePriceId, stripePriceId)
        });
        if (!newPrice) {
            throw new Error(`Price not found for priceId: ${stripePriceId}`);
        }
        // If there is a future scheduled change, we release it.
        if (subscription.stripeSubscriptionScheduleId) {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["releaseSubscriptionSchedule"])({
                subscriptionScheduleId: subscription.stripeSubscriptionScheduleId
            });
        }
        const isUpgrade = newPrice?.monthlyMessageLimit > currentPrice.monthlyMessageLimit;
        if (isUpgrade) {
            // If the new price is higher, we invoice the customer immediately.
            const updatedSubscription = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateSubscription"])({
                subscriptionId: stripeSubscriptionId,
                subscriptionItemId: stripeSubscriptionItemId,
                priceId: stripePriceId
            });
            await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"]).set({
                priceId: newPrice.id,
                status: 'active',
                updatedAt: new Date()
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].stripeSubscriptionItemId, stripeSubscriptionItemId)).returning();
            return updatedSubscription;
        } else {
            // If the new price is lower, we schedule the change for the end of the current period.
            const schedule = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateSubscriptionNextPeriod"])({
                subscriptionId: stripeSubscriptionId,
                priceId: stripePriceId
            });
            const endDate = schedule.phases[0]?.end_date;
            const scheduledChangeAt = endDate ? new Date(endDate * 1000) : null;
            const [updatedSubscription] = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"]).set({
                priceId: currentPrice.id,
                updatedAt: new Date(),
                scheduledPriceId: newPrice.id,
                stripeSubscriptionScheduleId: schedule.id,
                scheduledChangeAt
            }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].stripeSubscriptionItemId, stripeSubscriptionItemId)).returning();
            return updatedSubscription;
        }
    }),
    releaseSubscriptionSchedule: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        subscriptionScheduleId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).mutation(async ({ input })=>{
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$functions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["releaseSubscriptionSchedule"])({
            subscriptionScheduleId: input.subscriptionScheduleId
        });
        await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"]).set({
            status: 'active',
            updatedAt: new Date(),
            scheduledPriceId: null,
            stripeSubscriptionScheduleId: null,
            scheduledChangeAt: null
        }).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].stripeSubscriptionScheduleId, input.subscriptionScheduleId)).returning();
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/usage/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "usageRouter": (()=>usageRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/subscription/subscription.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/subscription/usage.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/models/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/models/src/usage/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/stripe/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/stripe/src/constants.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/sql.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
const usageRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ({ ctx })=>{
        const user = ctx.user;
        // Calculate date ranges
        const now = new Date();
        const lastDay = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
        const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        // Count records from last day
        const lastDayCount = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].select({
            count: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sql"]`count(*)`
        }).from(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"].userId, user.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["gte"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"].timestamp, lastDay)));
        // Count records from last month
        const lastMonthCount = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].select({
            count: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sql"]`count(*)`
        }).from(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"]).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"].userId, user.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["gte"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"].timestamp, lastMonth)));
        let dailyLimitCount = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FREE_PRODUCT_CONFIG"].dailyLimit;
        let monthlyLimitCount = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$stripe$2f$src$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FREE_PRODUCT_CONFIG"].monthlyLimit;
        const subscription = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query.subscriptions.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].userId, user.id), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptions"].status, 'active')),
            with: {
                price: true
            }
        });
        if (subscription) {
            // Monthly and daily limits are the same for PRO subscription
            dailyLimitCount = subscription.price.monthlyMessageLimit;
            monthlyLimitCount = subscription.price.monthlyMessageLimit;
        }
        return {
            daily: {
                period: 'day',
                usageCount: lastDayCount[0]?.count || 0,
                limitCount: dailyLimitCount
            },
            monthly: {
                period: 'month',
                usageCount: lastMonthCount[0]?.count || 0,
                limitCount: monthlyLimitCount
            }
        };
    }),
    increment: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].nativeEnum(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$models$2f$src$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UsageType"])
    })).mutation(async ({ ctx, input })=>{
        const user = ctx.user;
        await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$subscription$2f$usage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRecords"]).values({
            userId: user.id,
            type: input.type,
            timestamp: new Date()
        });
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/subscription/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/subscription/subscription.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/usage/index.ts [app-route] (ecmascript)");
;
;
}}),
"[project]/apps/web/client/src/server/api/routers/subscription/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/subscription/subscription.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/usage/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/subscription/index.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/apps/web/client/src/server/api/routers/user/user.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "userRouter": (()=>userRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/defaults/user-settings.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/settings.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
const userSettingsRoute = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const settings = await ctx.db.query.userSettings.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userSettings"].userId, input.userId)
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toUserSettings"])(settings ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$defaults$2f$user$2d$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultUserSettings"])(input.userId));
    }),
    upsert: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        settings: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userSettingsInsertSchema"]
    })).mutation(async ({ ctx, input })=>{
        if (!input.userId) {
            throw new Error('User ID is required');
        }
        const [updatedSettings] = await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userSettings"]).values({
            ...input.settings,
            userId: input.userId
        }).onConflictDoUpdate({
            target: [
                __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userSettings"].userId
            ],
            set: input.settings
        }).returning();
        if (!updatedSettings) {
            throw new Error('Failed to update user settings');
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toUserSettings"])(updatedSettings);
    })
});
const userRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    getById: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).query(async ({ ctx, input })=>{
        const user = await ctx.db.query.users.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["users"].id, input),
            with: {
                userProjects: {
                    with: {
                        project: true
                    }
                }
            }
        });
        return user;
    }),
    create: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userInsertSchema"]).mutation(async ({ ctx, input })=>{
        const user = await ctx.db.insert(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["users"]).values(input).returning({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["users"].id
        });
        if (!user[0]) {
            throw new Error('Failed to create user');
        }
        return user[0];
    }),
    settings: userSettingsRoute
});
}}),
"[project]/apps/web/client/src/server/api/routers/user/user-canvas.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "userCanvasRouter": (()=>userCanvasRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/db/src/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/project/canvas/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/dto/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/db/src/schema/user/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/drizzle-orm/sql/expressions/conditions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
const userCanvasRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    get: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        projectId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })).query(async ({ ctx, input })=>{
        const userCanvas = await ctx.db.query.userCanvases.findFirst({
            where: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$project$2f$canvas$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvases"].projectId, input.projectId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"].userId, ctx.user.id)),
            with: {
                canvas: true
            }
        });
        if (!userCanvas) {
            throw new Error('User canvas not found');
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$dto$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toCanvas"])(userCanvas);
    }),
    update: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvasUpdateSchema"]).mutation(async ({ ctx, input })=>{
        try {
            if (!input.canvasId) {
                throw new Error('Canvas ID is required');
            }
            await ctx.db.update(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"]).set(input).where((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["and"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"].canvasId, input.canvasId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$expressions$2f$conditions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eq"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$db$2f$src$2f$schema$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvases"].userId, ctx.user.id)));
            return true;
        } catch (error) {
            console.error('Error updating user canvas', error);
            return false;
        }
    })
});
}}),
"[project]/apps/web/client/src/server/api/routers/user/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/user-canvas.ts [app-route] (ecmascript)");
;
;
}}),
"[project]/apps/web/client/src/server/api/routers/user/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/user.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/index.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/apps/web/client/src/server/api/routers/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$code$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/code.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$forward$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/forward/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/github.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$image$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/image.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/settings.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/subscription/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/usage/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/index.ts [app-route] (ecmascript) <module evaluation>");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/routers/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$code$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/code.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$forward$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/forward/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/github.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$image$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/image.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/settings.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/subscription/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/usage/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/index.ts [app-route] (ecmascript) <locals>");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/server/api/root.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "appRouter": (()=>appRouter),
    "createCaller": (()=>createCaller)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$chat$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/chat.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$code$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/code.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/domain/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/frame.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/github.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$image$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/image.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/invitation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$member$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/member.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/project.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/project/sandbox.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/settings.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/subscription/subscription.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/usage/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/user-canvas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/routers/user/user.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
const appRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    sandbox: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$sandbox$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sandboxRouter"],
    user: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userRouter"],
    invitation: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$invitation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invitationRouter"],
    project: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$project$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["projectRouter"],
    settings: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$settings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["settingsRouter"],
    chat: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$chat$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chatRouter"],
    frame: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$frame$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["frameRouter"],
    canvas: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["canvasRouter"],
    userCanvas: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$user$2f$user$2d$canvas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userCanvasRouter"],
    code: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$code$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeRouter"],
    member: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$project$2f$member$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["memberRouter"],
    domain: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$domain$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["domainRouter"],
    github: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["githubRouter"],
    subscription: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$subscription$2f$subscription$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptionRouter"],
    usage: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$usage$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["usageRouter"],
    image: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$routers$2f$image$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["imageRouter"]
});
const createCaller = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCallerFactory"])(appRouter);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/apps/web/client/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "GET": (()=>handler),
    "POST": (()=>handler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$adapters$2f$fetch$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/adapters/fetch/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/env.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$root$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/root.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/client/src/server/api/trpc.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$root$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$root$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
/**
 * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when
 * handling a HTTP request (e.g. when you make requests from Client Components).
 */ const createContext = async (req)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCContext"])({
        headers: req.headers
    });
};
const handler = (req)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$adapters$2f$fetch$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fetchRequestHandler"])({
        endpoint: '/api/trpc',
        req,
        router: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$server$2f$api$2f$root$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["appRouter"],
        createContext: ()=>createContext(req),
        onError: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$client$2f$src$2f$env$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NODE_ENV === 'development' ? ({ path, error })=>{
            console.error(`❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`);
        } : undefined
    });
;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=apps_web_client_1f176412._.js.map