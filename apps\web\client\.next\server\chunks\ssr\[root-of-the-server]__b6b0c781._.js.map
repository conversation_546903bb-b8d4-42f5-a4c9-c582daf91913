{"version": 3, "sources": [], "sections": [{"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/vujahday_script_c94149d0.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"vujahday_script_c94149d0-module__7v866q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/vujahday_script_c94149d0.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Vujahday_Script%22,%22arguments%22:[{%22weight%22:%22400%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22vujahdayScript%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Vujahday Script', 'Vujahday Script Fallback'\",\n        fontWeight: 400,\nfontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,YAAY;QACpB,WAAW;IAEP;AACJ;AAEA,IAAI,+JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}]}