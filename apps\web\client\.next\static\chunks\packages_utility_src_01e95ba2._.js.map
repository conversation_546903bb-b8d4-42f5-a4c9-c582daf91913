{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/assert.ts"], "sourcesContent": ["export function assertNever(n: never): never {\n    throw new Error(`Expected \\`never\\`, found: ${JSON.stringify(n)}`);\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,YAAY,CAAQ;IAChC,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,KAAK,SAAS,CAAC,IAAI;AACrE", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/autolayout.ts"], "sourcesContent": ["export enum LayoutProperty {\n    width = 'width',\n    height = 'height',\n}\n\nexport enum LayoutMode {\n    Fit = 'Fit',\n    Fill = 'Fill',\n    Relative = 'Relative',\n    Fixed = 'Fixed',\n}\n\nexport function parseModeAndValue(value: string): {\n    mode: LayoutMode;\n    layoutValue: string;\n} {\n    if (value === 'fit-content' || value === 'auto' || value === '') {\n        return { mode: LayoutMode.Fit, layoutValue: '' };\n    }\n    if (value === '100%' || value === 'auto') {\n        return { mode: LayoutMode.Fill, layoutValue: '100%' };\n    }\n    if (value.includes('%')) {\n        return { mode: LayoutMode.Relative, layoutValue: value };\n    }\n    return { mode: LayoutMode.Fixed, layoutValue: value };\n}\n\nexport function getRelativeValue(\n    property: LayoutProperty,\n    childRect: DOMRect,\n    parentRect: DOMRect,\n): string {\n    const parentDimension =\n        property === LayoutProperty.width ? parentRect.width : parentRect.height;\n    const childDimension = property === LayoutProperty.width ? childRect.width : childRect.height;\n    return `${((childDimension / parentDimension) * 100).toFixed(0)}%`;\n}\n\nexport function getAutolayoutStyles(\n    property: LayoutProperty,\n    mode: LayoutMode,\n    value: string,\n    childRect: DOMRect,\n    parentRect: DOMRect,\n): string {\n    const MODE_PROPERTIES = {\n        [LayoutMode.Fit]: 'fit-content',\n        [LayoutMode.Fill]: '100%',\n        [LayoutMode.Relative]: getRelativeValue(property, childRect, parentRect),\n        [LayoutMode.Fixed]: `${property === LayoutProperty.width ? childRect.width : childRect.height}px`,\n    };\n    return MODE_PROPERTIES[mode] || value;\n}\n\nexport function getRowColumnCount(value: string): number {\n    return value.split(' ').length;\n}\n\nexport function generateRowColumnTemplate(value: string): string {\n    return `repeat(${value}, 1fr)`;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAO,IAAA,AAAK,wCAAA;;;WAAA;;AAKL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAOL,SAAS,kBAAkB,KAAa;IAI3C,IAAI,UAAU,iBAAiB,UAAU,UAAU,UAAU,IAAI;QAC7D,OAAO;YAAE,IAAI;YAAkB,aAAa;QAAG;IACnD;IACA,IAAI,UAAU,UAAU,UAAU,QAAQ;QACtC,OAAO;YAAE,IAAI;YAAmB,aAAa;QAAO;IACxD;IACA,IAAI,MAAM,QAAQ,CAAC,MAAM;QACrB,OAAO;YAAE,IAAI;YAAuB,aAAa;QAAM;IAC3D;IACA,OAAO;QAAE,IAAI;QAAoB,aAAa;IAAM;AACxD;AAEO,SAAS,iBACZ,QAAwB,EACxB,SAAkB,EAClB,UAAmB;IAEnB,MAAM,kBACF,uBAAoC,WAAW,KAAK,GAAG,WAAW,MAAM;IAC5E,MAAM,iBAAiB,uBAAoC,UAAU,KAAK,GAAG,UAAU,MAAM;IAC7F,OAAO,GAAG,CAAC,AAAC,iBAAiB,kBAAmB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACtE;AAEO,SAAS,oBACZ,QAAwB,EACxB,IAAgB,EAChB,KAAa,EACb,SAAkB,EAClB,UAAmB;IAEnB,MAAM,kBAAkB;QACpB,OAAgB,EAAE;QAClB,QAAiB,EAAE;QACnB,YAAqB,EAAE,iBAAiB,UAAU,WAAW;QAC7D,SAAkB,EAAE,GAAG,uBAAoC,UAAU,KAAK,GAAG,UAAU,MAAM,CAAC,EAAE,CAAC;IACrG;IACA,OAAO,eAAe,CAAC,KAAK,IAAI;AACpC;AAEO,SAAS,kBAAkB,KAAa;IAC3C,OAAO,MAAM,KAAK,CAAC,KAAK,MAAM;AAClC;AAEO,SAAS,0BAA0B,KAAa;IACnD,OAAO,CAAC,OAAO,EAAE,MAAM,MAAM,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/clone.ts"], "sourcesContent": ["export const jsonClone = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY,CAAI,MAAc,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/math.ts"], "sourcesContent": ["export function isNearEqual(x: number, y: number, delta: number): boolean {\n    return Math.abs(x - y) <= delta;\n}\n\nexport function mod(x: number, y: number): number {\n    return x - y * Math.floor(x / y);\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,YAAY,CAAS,EAAE,CAAS,EAAE,KAAa;IAC3D,OAAO,KAAK,GAAG,CAAC,IAAI,MAAM;AAC9B;AAEO,SAAS,IAAI,CAAS,EAAE,CAAS;IACpC,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;AAClC", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/color.ts"], "sourcesContent": ["import colorNamer from 'color-namer';\nimport type cssColorNames from 'css-color-names';\nimport { oklch, rgb } from 'culori';\nimport parseCSSColor from 'parse-css-color';\nimport { isNearEqual } from './math';\n\nexport function isColorEmpty(colorValue: string) {\n    const color = Color.from(colorValue);\n    return color.a === 0 || color.isEqual(Color.transparent);\n}\n\nexport function formatHexString(hex: string): string {\n    if (/^#?[0-9a-fA-F]{1,2}$/.exec(hex)) {\n        const s = hex.replace('#', '');\n        return '#' + s + s + s;\n    }\n    if (/^[0-9a-fA-F]{3,}$/.exec(hex)) {\n        return '#' + hex;\n    }\n    return hex;\n}\n\nexport function parseHslValue(value: string): Color | null {\n    let h = 0,\n        s = 0,\n        l = 0,\n        a = 1;\n\n    if (value.includes('hsl')) {\n        const hslMatch = value.match(\n            /hsla?\\(\\s*([^,\\s]+)(?:deg)?\\s*[,\\s]\\s*([^,\\s]+)%\\s*[,\\s]\\s*([^,\\s]+)%\\s*(?:[,/]\\s*([^)]+))?\\s*\\)/,\n        );\n\n        if (hslMatch) {\n            // Parse hue with unit support\n            const hueValue = hslMatch[1];\n            h = parseHueValue(hueValue ?? '0');\n            s = parseFloat(hslMatch[2] ?? '0');\n            l = parseFloat(hslMatch[3] ?? '0');\n\n            if (hslMatch[4]) {\n                a = hslMatch[4].endsWith('%')\n                    ? parseFloat(hslMatch[4]) / 100\n                    : parseFloat(hslMatch[4]);\n            }\n        } else {\n            return null;\n        }\n    } else {\n        // Parse space-separated format\n        const parts = value.split(/\\s+/);\n        if (parts.length >= 3) {\n            h = parseFloat(parts[0] ?? '0');\n            s = parseFloat(parts[1]?.replace('%', '') ?? '0');\n            l = parseFloat(parts[2]?.replace('%', '') ?? '0');\n\n            if (parts.length >= 4) {\n                a = parts[3]?.endsWith('%')\n                    ? parseFloat(parts[3]) / 100\n                    : parseFloat(parts[3] ?? '0');\n            }\n        } else {\n            return null;\n        }\n    }\n\n    // Normalize values\n    h = ((h % 360) + 360) % 360;\n    s = Math.max(0, Math.min(100, s));\n    l = Math.max(0, Math.min(100, l));\n    a = Math.max(0, Math.min(1, a));\n\n    return Color.hsl({\n        h: h / 360,\n        s: s / 100,\n        l: l / 100,\n        a,\n    });\n}\n\nfunction parseHueValue(value: string): number {\n    if (value.endsWith('turn')) {\n        return parseFloat(value) * 360;\n    }\n    if (value.endsWith('rad')) {\n        return parseFloat(value) * (180 / Math.PI);\n    }\n    if (value.endsWith('grad')) {\n        return parseFloat(value) * 0.9;\n    }\n    return parseFloat(value);\n}\n\nexport function parseOklchValue(value: string): Color | null {\n    let l = 0,\n        c = 0,\n        h = 0,\n        a = 1;\n\n    if (value.includes('oklch')) {\n        const oklchMatch = value.match(\n            /oklch\\(\\s*([^,\\s]+)%?\\s*[,\\s]\\s*([^,\\s]+)\\s*[,\\s]\\s*([^,\\s]+)(?:deg)?\\s*(?:[,/]\\s*([^)]+))?\\s*\\)/,\n        );\n\n        if (oklchMatch) {\n            l = oklchMatch[1]?.trim().endsWith('%')\n                ? parseFloat(oklchMatch[1]) / 100\n                : parseFloat(oklchMatch[1] ?? '0');\n            c = parseFloat(oklchMatch[2] ?? '0');\n\n            // Parse hue with unit support\n            const hueValue = oklchMatch[3];\n            h = parseHueValue(hueValue ?? '0');\n\n            if (oklchMatch[4]) {\n                a = oklchMatch[4].endsWith('%')\n                    ? parseFloat(oklchMatch[4]) / 100\n                    : parseFloat(oklchMatch[4]);\n            }\n        } else {\n            return null;\n        }\n    } else {\n        // Parse space-separated format: \"l c h / a\"\n        const parts = value.split(/\\s+/);\n        if (parts.length >= 3) {\n            l = parseFloat(parts[0]?.replace('%', '') ?? '0');\n            c = parseFloat(parts[1] ?? '0');\n            h = parseFloat(parts[2] ?? '0');\n\n            // Check for alpha after slash\n            const slashIndex = parts.findIndex((part) => part === '/');\n            if (slashIndex !== -1 && parts[slashIndex + 1]) {\n                const alphaPart = parts[slashIndex + 1];\n                a = alphaPart?.endsWith('%')\n                    ? parseFloat(alphaPart) / 100\n                    : parseFloat(alphaPart ?? '1');\n            }\n        } else {\n            return null;\n        }\n    }\n\n    // Normalize values\n    l = Math.max(0, Math.min(1, l));\n    c = Math.max(0, c);\n    h = ((h % 360) + 360) % 360;\n    a = Math.max(0, Math.min(1, a));\n\n    return Color.oklch({ l, c, h, a });\n}\n\nexport interface Palette {\n    name: string;\n    colors: {\n        [key: number]: string;\n    };\n}\n\nexport class Color {\n    constructor(opts: { h: number; s: number; v: number; a?: number }) {\n        this.h = opts.h;\n        this.s = opts.s;\n        this.v = opts.v;\n        this.a = opts.a ?? 1;\n    }\n\n    static get white(): Color {\n        return new Color({ h: 0, s: 0, v: 1 });\n    }\n    static get black(): Color {\n        return new Color({ h: 0, s: 0, v: 0 });\n    }\n    static get transparent(): Color {\n        return new Color({ h: 0, s: 0, v: 0, a: 0 });\n    }\n\n    static rgb(rgb: { r: number; g: number; b: number; a?: number }): Color {\n        return new Color({ ...rgb2hsv(rgb), a: rgb.a ?? 1 });\n    }\n    static hsl(hsl: { h: number; s: number; l: number; a?: number }): Color {\n        return new Color({ ...hsl2hsv(hsl), a: hsl.a ?? 1 });\n    }\n\n    static oklch(oklchColor: { l: number; c: number; h: number; a?: number }): Color {\n        // Convert OKLCH to RGB using culori\n        const oklchInput = {\n            mode: 'oklch' as const,\n            l: oklchColor.l,\n            c: oklchColor.c,\n            h: oklchColor.h,\n            alpha: oklchColor.a ?? 1,\n        };\n\n        const rgbColor = rgb(oklchInput);\n\n        if (!rgbColor) {\n            return Color.transparent;\n        }\n\n        return Color.rgb({\n            r: rgbColor.r ?? 0,\n            g: rgbColor.g ?? 0,\n            b: rgbColor.b ?? 0,\n            a: rgbColor.alpha ?? 1,\n        });\n    }\n\n    static from(name: keyof typeof cssColorNames): Color;\n    static from(name: string): Color;\n\n    static from(str: string): Color {\n        // First try to parse as OKLCH\n        if (str.includes('oklch') || /^\\s*[\\d.]+\\s+[\\d.]+\\s+[\\d.]+/.test(str)) {\n            const oklchColor = parseOklchValue(str);\n            if (oklchColor) {\n                return oklchColor;\n            }\n        }\n\n        const color = parseCSSColor(formatHexString(str));\n        if (color) {\n            if (color.type === 'rgb') {\n                return Color.rgb({\n                    r: (color.values[0] ?? 0) / 255,\n                    g: (color.values[1] ?? 0) / 255,\n                    b: (color.values[2] ?? 0) / 255,\n                    a: color.alpha ?? 1,\n                });\n            } else if (color.type === 'hsl') {\n                return Color.hsl({\n                    h: (color.values[0] ?? 0) / 360,\n                    s: (color.values[1] ?? 0) / 100,\n                    l: (color.values[2] ?? 0) / 100,\n                    a: color.alpha ?? 1,\n                });\n            }\n        }\n        return Color.transparent;\n    }\n\n    static mix(color0: Color, color1: Color, ratio: number): Color {\n        const rgb0 = color0.rgb;\n        const rgb1 = color1.rgb;\n\n        const r = rgb0.r * (1 - ratio) + rgb1.r * ratio;\n        const g = rgb0.g * (1 - ratio) + rgb1.g * ratio;\n        const b = rgb0.b * (1 - ratio) + rgb1.b * ratio;\n        const a = rgb0.a * (1 - ratio) + rgb1.a * ratio;\n\n        return new Color({ ...rgb2hsv({ r, g, b }), a });\n    }\n\n    readonly h: number;\n    readonly s: number;\n    readonly v: number;\n    readonly a: number;\n\n    private _name: string | undefined;\n\n    lighten(intensity: number): Color {\n        const { h, s, l } = this.hsl;\n        const newColor = {\n            h,\n            s,\n            l: Math.min(l + (1 - l) * intensity, 1),\n            a: this.a,\n        };\n        return Color.hsl(newColor);\n    }\n\n    darken(intensity: number): Color {\n        const { h, s, l } = this.hsl;\n        const newColor = {\n            h,\n            s,\n            l: Math.max(l - l * intensity, 0),\n            a: this.a,\n        };\n        return Color.hsl(newColor);\n    }\n\n    get palette(): Palette {\n        const name = this.name;\n\n        const palette: Palette = {\n            name,\n            colors: {\n                500: this.toString(),\n            },\n        };\n\n        const intensityMap: {\n            [key: number]: number;\n        } = {\n            50: 0.9,\n            100: 0.8,\n            200: 0.595,\n            300: 0.415,\n            400: 0.21,\n            500: 0.0,\n            600: 0.21,\n            700: 0.415,\n            800: 0.595,\n            900: 0.8,\n            950: 0.9,\n        };\n\n        [50, 100, 200, 300, 400].forEach((level) => {\n            palette.colors[level] = this.lighten(intensityMap[level] ?? 0).toString();\n        });\n\n        [600, 700, 800, 900, 950].forEach((level) => {\n            palette.colors[level] = this.darken(intensityMap[level] ?? 0).toString();\n        });\n\n        return palette;\n    }\n\n    toHex6(): string {\n        const { r, g, b } = this.rgb;\n        return (\n            '#' +\n            [r, g, b]\n                .map((c) => {\n                    const str = Math.round(c * 255)\n                        .toString(16)\n                        .toUpperCase();\n                    return str.length === 1 ? '0' + str : str;\n                })\n                .join('')\n        );\n    }\n\n    toHex8(): string {\n        const { r, g, b, a } = this.rgb;\n        return (\n            '#' +\n            [r, g, b, a]\n                .map((c) => {\n                    const str = Math.round(c * 255)\n                        .toString(16)\n                        .toUpperCase();\n                    return str.length === 1 ? '0' + str : str;\n                })\n                .join('')\n        );\n    }\n\n    toHex(): string {\n        if (this.a > 0.999) {\n            return this.toHex6();\n        } else {\n            return this.toHex8();\n        }\n    }\n\n    toString(): string {\n        return this.toHex();\n    }\n\n    isEqual(other: Color): boolean {\n        return (\n            isNearEqual(this.h, other.h, 0.001) &&\n            isNearEqual(this.s, other.s, 0.001) &&\n            isNearEqual(this.v, other.v, 0.001) &&\n            isNearEqual(this.a, other.a, 0.001)\n        );\n    }\n\n    get rgb(): { r: number; g: number; b: number; a: number } {\n        return { ...hsv2rgb(this), a: this.a };\n    }\n    get hsl(): { h: number; s: number; l: number; a: number } {\n        return { ...hsv2hsl(this), a: this.a };\n    }\n\n    get name(): string {\n        return this._name\n            ? this._name\n            : (colorNamer(this.toHex6()).ntc[0]?.name?.toLowerCase().replace(' ', '-') ?? '');\n    }\n\n    set name(newName: string) {\n        this._name = newName;\n    }\n\n    withAlpha(a: number): Color {\n        return new Color({ ...this, a });\n    }\n}\n\n// https://stackoverflow.com/a/54116681\nfunction hsl2hsv({ h, s, l }: { h: number; s: number; l: number }): {\n    h: number;\n    s: number;\n    v: number;\n} {\n    const v = s * Math.min(l, 1 - l) + l;\n    return { h, s: v ? 2 - (2 * l) / v : 0, v };\n}\n\nfunction hsv2hsl({ h, s, v }: { h: number; s: number; v: number }): {\n    h: number;\n    s: number;\n    l: number;\n} {\n    const l = v - (v * s) / 2;\n    const m = Math.min(l, 1 - l);\n    return { h, s: m ? (v - l) / m : 0, l };\n}\n\n// https://stackoverflow.com/a/54024653\nfunction hsv2rgb({ h, s, v }: { h: number; s: number; v: number }): {\n    r: number;\n    g: number;\n    b: number;\n} {\n    const f = (n: number, k = (n + h * 6) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n    return { r: f(5), g: f(3), b: f(1) };\n}\n\n// https://stackoverflow.com/a/54070620\nfunction rgb2hsv({ r, g, b }: { r: number; g: number; b: number }): {\n    h: number;\n    s: number;\n    v: number;\n} {\n    const v = Math.max(r, g, b),\n        c = v - Math.min(r, g, b);\n    const h = c && (v === r ? (g - b) / c : v === g ? 2 + (b - r) / c : 4 + (r - g) / c);\n    return { h: (h < 0 ? h + 6 : h) / 6, s: v && c / v, v };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;AAAA;AACA;AACA;;;;;AAEO,SAAS,aAAa,UAAkB;IAC3C,MAAM,QAAQ,MAAM,IAAI,CAAC;IACzB,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM,OAAO,CAAC,MAAM,WAAW;AAC3D;AAEO,SAAS,gBAAgB,GAAW;IACvC,IAAI,uBAAuB,IAAI,CAAC,MAAM;QAClC,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK;QAC3B,OAAO,MAAM,IAAI,IAAI;IACzB;IACA,IAAI,oBAAoB,IAAI,CAAC,MAAM;QAC/B,OAAO,MAAM;IACjB;IACA,OAAO;AACX;AAEO,SAAS,cAAc,KAAa;IACvC,IAAI,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI;IAER,IAAI,MAAM,QAAQ,CAAC,QAAQ;QACvB,MAAM,WAAW,MAAM,KAAK,CACxB;QAGJ,IAAI,UAAU;YACV,8BAA8B;YAC9B,MAAM,WAAW,QAAQ,CAAC,EAAE;YAC5B,IAAI,cAAc,YAAY;YAC9B,IAAI,WAAW,QAAQ,CAAC,EAAE,IAAI;YAC9B,IAAI,WAAW,QAAQ,CAAC,EAAE,IAAI;YAE9B,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACb,IAAI,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OACnB,WAAW,QAAQ,CAAC,EAAE,IAAI,MAC1B,WAAW,QAAQ,CAAC,EAAE;YAChC;QACJ,OAAO;YACH,OAAO;QACX;IACJ,OAAO;QACH,+BAA+B;QAC/B,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,IAAI,GAAG;YACnB,IAAI,WAAW,KAAK,CAAC,EAAE,IAAI;YAC3B,IAAI,WAAW,KAAK,CAAC,EAAE,EAAE,QAAQ,KAAK,OAAO;YAC7C,IAAI,WAAW,KAAK,CAAC,EAAE,EAAE,QAAQ,KAAK,OAAO;YAE7C,IAAI,MAAM,MAAM,IAAI,GAAG;gBACnB,IAAI,KAAK,CAAC,EAAE,EAAE,SAAS,OACjB,WAAW,KAAK,CAAC,EAAE,IAAI,MACvB,WAAW,KAAK,CAAC,EAAE,IAAI;YACjC;QACJ,OAAO;YACH,OAAO;QACX;IACJ;IAEA,mBAAmB;IACnB,IAAI,CAAC,AAAC,IAAI,MAAO,GAAG,IAAI;IACxB,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;IAC9B,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;IAC9B,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;IAE5B,OAAO,MAAM,GAAG,CAAC;QACb,GAAG,IAAI;QACP,GAAG,IAAI;QACP,GAAG,IAAI;QACP;IACJ;AACJ;AAEA,SAAS,cAAc,KAAa;IAChC,IAAI,MAAM,QAAQ,CAAC,SAAS;QACxB,OAAO,WAAW,SAAS;IAC/B;IACA,IAAI,MAAM,QAAQ,CAAC,QAAQ;QACvB,OAAO,WAAW,SAAS,CAAC,MAAM,KAAK,EAAE;IAC7C;IACA,IAAI,MAAM,QAAQ,CAAC,SAAS;QACxB,OAAO,WAAW,SAAS;IAC/B;IACA,OAAO,WAAW;AACtB;AAEO,SAAS,gBAAgB,KAAa;IACzC,IAAI,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI;IAER,IAAI,MAAM,QAAQ,CAAC,UAAU;QACzB,MAAM,aAAa,MAAM,KAAK,CAC1B;QAGJ,IAAI,YAAY;YACZ,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,SAAS,OAC7B,WAAW,UAAU,CAAC,EAAE,IAAI,MAC5B,WAAW,UAAU,CAAC,EAAE,IAAI;YAClC,IAAI,WAAW,UAAU,CAAC,EAAE,IAAI;YAEhC,8BAA8B;YAC9B,MAAM,WAAW,UAAU,CAAC,EAAE;YAC9B,IAAI,cAAc,YAAY;YAE9B,IAAI,UAAU,CAAC,EAAE,EAAE;gBACf,IAAI,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,OACrB,WAAW,UAAU,CAAC,EAAE,IAAI,MAC5B,WAAW,UAAU,CAAC,EAAE;YAClC;QACJ,OAAO;YACH,OAAO;QACX;IACJ,OAAO;QACH,4CAA4C;QAC5C,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,IAAI,GAAG;YACnB,IAAI,WAAW,KAAK,CAAC,EAAE,EAAE,QAAQ,KAAK,OAAO;YAC7C,IAAI,WAAW,KAAK,CAAC,EAAE,IAAI;YAC3B,IAAI,WAAW,KAAK,CAAC,EAAE,IAAI;YAE3B,8BAA8B;YAC9B,MAAM,aAAa,MAAM,SAAS,CAAC,CAAC,OAAS,SAAS;YACtD,IAAI,eAAe,CAAC,KAAK,KAAK,CAAC,aAAa,EAAE,EAAE;gBAC5C,MAAM,YAAY,KAAK,CAAC,aAAa,EAAE;gBACvC,IAAI,WAAW,SAAS,OAClB,WAAW,aAAa,MACxB,WAAW,aAAa;YAClC;QACJ,OAAO;YACH,OAAO;QACX;IACJ;IAEA,mBAAmB;IACnB,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;IAC5B,IAAI,KAAK,GAAG,CAAC,GAAG;IAChB,IAAI,CAAC,AAAC,IAAI,MAAO,GAAG,IAAI;IACxB,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;IAE5B,OAAO,MAAM,KAAK,CAAC;QAAE;QAAG;QAAG;QAAG;IAAE;AACpC;AASO,MAAM;IACT,YAAY,IAAqD,CAAE;QAC/D,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI;IACvB;IAEA,WAAW,QAAe;QACtB,OAAO,IAAI,MAAM;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;IACxC;IACA,WAAW,QAAe;QACtB,OAAO,IAAI,MAAM;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;IACxC;IACA,WAAW,cAAqB;QAC5B,OAAO,IAAI,MAAM;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;IAC9C;IAEA,OAAO,IAAI,GAAoD,EAAS;QACpE,OAAO,IAAI,MAAM;YAAE,GAAG,QAAQ,IAAI;YAAE,GAAG,IAAI,CAAC,IAAI;QAAE;IACtD;IACA,OAAO,IAAI,GAAoD,EAAS;QACpE,OAAO,IAAI,MAAM;YAAE,GAAG,QAAQ,IAAI;YAAE,GAAG,IAAI,CAAC,IAAI;QAAE;IACtD;IAEA,OAAO,MAAM,UAA2D,EAAS;QAC7E,oCAAoC;QACpC,MAAM,aAAa;YACf,MAAM;YACN,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,OAAO,WAAW,CAAC,IAAI;QAC3B;QAEA,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,MAAG,AAAD,EAAE;QAErB,IAAI,CAAC,UAAU;YACX,OAAO,MAAM,WAAW;QAC5B;QAEA,OAAO,MAAM,GAAG,CAAC;YACb,GAAG,SAAS,CAAC,IAAI;YACjB,GAAG,SAAS,CAAC,IAAI;YACjB,GAAG,SAAS,CAAC,IAAI;YACjB,GAAG,SAAS,KAAK,IAAI;QACzB;IACJ;IAKA,OAAO,KAAK,GAAW,EAAS;QAC5B,8BAA8B;QAC9B,IAAI,IAAI,QAAQ,CAAC,YAAY,+BAA+B,IAAI,CAAC,MAAM;YACnE,MAAM,aAAa,gBAAgB;YACnC,IAAI,YAAY;gBACZ,OAAO;YACX;QACJ;QAEA,MAAM,QAAQ,CAAA,GAAA,gKAAA,CAAA,UAAa,AAAD,EAAE,gBAAgB;QAC5C,IAAI,OAAO;YACP,IAAI,MAAM,IAAI,KAAK,OAAO;gBACtB,OAAO,MAAM,GAAG,CAAC;oBACb,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;oBAC5B,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;oBAC5B,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;oBAC5B,GAAG,MAAM,KAAK,IAAI;gBACtB;YACJ,OAAO,IAAI,MAAM,IAAI,KAAK,OAAO;gBAC7B,OAAO,MAAM,GAAG,CAAC;oBACb,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;oBAC5B,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;oBAC5B,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;oBAC5B,GAAG,MAAM,KAAK,IAAI;gBACtB;YACJ;QACJ;QACA,OAAO,MAAM,WAAW;IAC5B;IAEA,OAAO,IAAI,MAAa,EAAE,MAAa,EAAE,KAAa,EAAS;QAC3D,MAAM,OAAO,OAAO,GAAG;QACvB,MAAM,OAAO,OAAO,GAAG;QAEvB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG;QAC1C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG;QAC1C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG;QAC1C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG;QAE1C,OAAO,IAAI,MAAM;YAAE,GAAG,QAAQ;gBAAE;gBAAG;gBAAG;YAAE,EAAE;YAAE;QAAE;IAClD;IAES,EAAU;IACV,EAAU;IACV,EAAU;IACV,EAAU;IAEX,MAA0B;IAElC,QAAQ,SAAiB,EAAS;QAC9B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;QAC5B,MAAM,WAAW;YACb;YACA;YACA,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW;YACrC,GAAG,IAAI,CAAC,CAAC;QACb;QACA,OAAO,MAAM,GAAG,CAAC;IACrB;IAEA,OAAO,SAAiB,EAAS;QAC7B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;QAC5B,MAAM,WAAW;YACb;YACA;YACA,GAAG,KAAK,GAAG,CAAC,IAAI,IAAI,WAAW;YAC/B,GAAG,IAAI,CAAC,CAAC;QACb;QACA,OAAO,MAAM,GAAG,CAAC;IACrB;IAEA,IAAI,UAAmB;QACnB,MAAM,OAAO,IAAI,CAAC,IAAI;QAEtB,MAAM,UAAmB;YACrB;YACA,QAAQ;gBACJ,KAAK,IAAI,CAAC,QAAQ;YACtB;QACJ;QAEA,MAAM,eAEF;YACA,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACT;QAEA;YAAC;YAAI;YAAK;YAAK;YAAK;SAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,GAAG,QAAQ;QAC3E;QAEA;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI,CAAC,OAAO,CAAC,CAAC;YAC/B,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,GAAG,QAAQ;QAC1E;QAEA,OAAO;IACX;IAEA,SAAiB;QACb,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;QAC5B,OACI,MACA;YAAC;YAAG;YAAG;SAAE,CACJ,GAAG,CAAC,CAAC;YACF,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,KACtB,QAAQ,CAAC,IACT,WAAW;YAChB,OAAO,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM;QAC1C,GACC,IAAI,CAAC;IAElB;IAEA,SAAiB;QACb,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;QAC/B,OACI,MACA;YAAC;YAAG;YAAG;YAAG;SAAE,CACP,GAAG,CAAC,CAAC;YACF,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,KACtB,QAAQ,CAAC,IACT,WAAW;YAChB,OAAO,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM;QAC1C,GACC,IAAI,CAAC;IAElB;IAEA,QAAgB;QACZ,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO;YAChB,OAAO,IAAI,CAAC,MAAM;QACtB,OAAO;YACH,OAAO,IAAI,CAAC,MAAM;QACtB;IACJ;IAEA,WAAmB;QACf,OAAO,IAAI,CAAC,KAAK;IACrB;IAEA,QAAQ,KAAY,EAAW;QAC3B,OACI,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,UAC7B,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,UAC7B,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,UAC7B,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;IAErC;IAEA,IAAI,MAAsD;QACtD,OAAO;YAAE,GAAG,QAAQ,IAAI,CAAC;YAAE,GAAG,IAAI,CAAC,CAAC;QAAC;IACzC;IACA,IAAI,MAAsD;QACtD,OAAO;YAAE,GAAG,QAAQ,IAAI,CAAC;YAAE,GAAG,IAAI,CAAC,CAAC;QAAC;IACzC;IAEA,IAAI,OAAe;QACf,OAAO,IAAI,CAAC,KAAK,GACX,IAAI,CAAC,KAAK,GACT,CAAA,GAAA,0IAAA,CAAA,UAAU,AAAD,EAAE,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE,MAAM,cAAc,QAAQ,KAAK,QAAQ;IACtF;IAEA,IAAI,KAAK,OAAe,EAAE;QACtB,IAAI,CAAC,KAAK,GAAG;IACjB;IAEA,UAAU,CAAS,EAAS;QACxB,OAAO,IAAI,MAAM;YAAE,GAAG,IAAI;YAAE;QAAE;IAClC;AACJ;AAEA,uCAAuC;AACvC,SAAS,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAuC;IAK7D,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,KAAK;IACnC,OAAO;QAAE;QAAG,GAAG,IAAI,IAAI,AAAC,IAAI,IAAK,IAAI;QAAG;IAAE;AAC9C;AAEA,SAAS,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAuC;IAK7D,MAAM,IAAI,IAAI,AAAC,IAAI,IAAK;IACxB,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI;IAC1B,OAAO;QAAE;QAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;QAAG;IAAE;AAC1C;AAEA,uCAAuC;AACvC,SAAS,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAuC;IAK7D,MAAM,IAAI,CAAC,GAAW,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAK,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAC1F,OAAO;QAAE,GAAG,EAAE;QAAI,GAAG,EAAE;QAAI,GAAG,EAAE;IAAG;AACvC;AAEA,uCAAuC;AACvC,SAAS,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAuC;IAK7D,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,IACrB,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG;IAC3B,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACnF,OAAO;QAAE,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;QAAG,GAAG,KAAK,IAAI;QAAG;IAAE;AAC1D", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/domain.ts"], "sourcesContent": ["/**\n * Domain utility functions\n */\n\n/**\n * Verifies domain ownership by checking various conditions\n * @param requestDomains - Array of domains to verify\n * @param ownedDomains - Array of domains that are owned\n * @param hostingDomain - The main hosting domain (optional)\n * @returns True if all request domains are owned or valid, false otherwise\n */\nexport const verifyDomainOwnership = (\n    requestDomains: string[],\n    ownedDomains: string[],\n    hostingDomain?: string,\n): boolean => {\n    return requestDomains.every((requestDomain) => {\n        // Check if domain is directly owned\n        if (ownedDomains.includes(requestDomain)) {\n            return true;\n        }\n\n        // Check if www version of owned domain\n        const withoutWww = requestDomain.replace(/^www\\./, '');\n        if (ownedDomains.includes(withoutWww)) {\n            return true;\n        }\n\n        // Check if subdomain of hosting domain\n        if (hostingDomain && requestDomain.endsWith(`.${hostingDomain}`)) {\n            return true;\n        }\n\n        return false;\n    });\n};\n\n/**\n * Checks if a domain is a subdomain of a given parent domain\n * @param domain - The domain to check\n * @param parentDomain - The parent domain\n * @returns True if domain is a subdomain of parentDomain\n */\nexport const isSubdomain = (domain: string, parentDomain: string): boolean => {\n    return domain !== parentDomain && domain.endsWith(`.${parentDomain}`);\n};\n\n/**\n * Extracts the root domain from a full domain (removes subdomains)\n * @param domain - The domain to extract root from\n * @returns The root domain\n */\nexport const getRootDomain = (domain: string): string => {\n    const parts = domain.split('.');\n    if (parts.length <= 2) {\n        return domain;\n    }\n    return parts.slice(-2).join('.');\n};\n\n/**\n * Validates if a string is a valid domain format\n * @param domain - The domain string to validate\n * @returns True if the domain format is valid\n */\nexport const isValidDomain = (domain: string): boolean => {\n    const domainRegex =\n        /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)*[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?$/i;\n    return domainRegex.test(domain) && domain.length <= 253;\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;CAMC;;;;;;AACM,MAAM,wBAAwB,CACjC,gBACA,cACA;IAEA,OAAO,eAAe,KAAK,CAAC,CAAC;QACzB,oCAAoC;QACpC,IAAI,aAAa,QAAQ,CAAC,gBAAgB;YACtC,OAAO;QACX;QAEA,uCAAuC;QACvC,MAAM,aAAa,cAAc,OAAO,CAAC,UAAU;QACnD,IAAI,aAAa,QAAQ,CAAC,aAAa;YACnC,OAAO;QACX;QAEA,uCAAuC;QACvC,IAAI,iBAAiB,cAAc,QAAQ,CAAC,CAAC,CAAC,EAAE,eAAe,GAAG;YAC9D,OAAO;QACX;QAEA,OAAO;IACX;AACJ;AAQO,MAAM,cAAc,CAAC,QAAgB;IACxC,OAAO,WAAW,gBAAgB,OAAO,QAAQ,CAAC,CAAC,CAAC,EAAE,cAAc;AACxE;AAOO,MAAM,gBAAgB,CAAC;IAC1B,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,MAAM,MAAM,IAAI,GAAG;QACnB,OAAO;IACX;IACA,OAAO,MAAM,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;AAChC;AAOO,MAAM,gBAAgB,CAAC;IAC1B,MAAM,cACF;IACJ,OAAO,YAAY,IAAI,CAAC,WAAW,OAAO,MAAM,IAAI;AACxD", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/email.ts"], "sourcesContent": ["import freeEmailDomains from 'free-email-domains';\n\nexport const isFreeEmail = (email: string) => {\n    const domain = email.split('@').at(-1);\n    return freeEmailDomains.includes(domain ?? '');\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,SAAS,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;IACpC,OAAO,sJAAA,CAAA,UAAgB,CAAC,QAAQ,CAAC,UAAU;AAC/C", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/errors.ts"], "sourcesContent": ["import stripAnsi from 'strip-ansi';\n\nexport interface ParsedError {\n    sourceId: string;\n    type: 'frame' | 'terminal' | 'apply-code';\n    content: string;\n}\n\nexport function compareErrors(a: ParsedError, b: ParsedError): boolean {\n    if (a.sourceId === b.sourceId && a.content === b.content) {\n        return true;\n    }\n    return false;\n}\n\nexport function shouldIgnoreMessage(message: string) {\n    if (message.startsWith('<w>')) {\n        return true;\n    }\n    return false;\n}\n\nexport function isErrorMessage(data: string) {\n    // Critical CLI errors\n    const errorPatterns = [\n        // Next.js errors\n        'Syntax Error',\n        'Reference Error',\n        'Type Error',\n\n        'command not found',\n        'ENOENT:',\n        'fatal:',\n        'error:',\n\n        // Critical Node.js errors\n        'TypeError',\n        'ReferenceError',\n        'SyntaxError',\n        'Cannot find module',\n        'Module not found',\n\n        // Critical React/Next.js errors\n        'Failed to compile',\n        'Build failed',\n        'Invalid hook call',\n        'Invalid configuration',\n\n        // Critical Package errors\n        'npm ERR!',\n        'yarn error',\n        'pnpm ERR!',\n        'Missing dependencies',\n\n        // Critical TypeScript errors\n        'TS2304:', // Cannot find name\n        'TS2307:', // Cannot find module\n    ];\n\n    let errorFound = false;\n    if (errorPatterns.some((pattern) => data.toLowerCase().includes(pattern.toLowerCase()))) {\n        errorFound = true;\n    }\n    return errorFound;\n}\n\nexport function isSuccessMessage(data: string): boolean {\n    const successPatterns = ['get / 200'];\n\n    if (successPatterns.some((pattern) => data.toLowerCase().includes(pattern.toLowerCase()))) {\n        return true;\n    }\n    return false;\n}\n\n// Stateful buffer for terminal output\nexport class TerminalBuffer {\n    private buffer: string[] = [];\n    private readonly maxLines: number;\n    private errorCallback?: (errorLines: string[]) => void;\n    private successCallback?: () => void;\n\n    constructor(maxLines: number = 20) {\n        this.maxLines = maxLines;\n    }\n\n    /**\n     * Register a callback to be called when an error is detected.\n     */\n    onError(callback: (errorLines: string[]) => void) {\n        this.errorCallback = callback;\n    }\n\n    /**\n     * Register a callback to be called when a success is detected (buffer is cleared).\n     */\n    onSuccess(callback: () => void) {\n        this.successCallback = callback;\n    }\n\n    /**\n     * Add a new line to the buffer and process for errors/success.\n     */\n    addLine(line: string) {\n        const rawMessage = stripAnsi(line);\n\n        this.buffer.push(rawMessage);\n        if (this.buffer.length > this.maxLines) {\n            this.buffer.shift();\n        }\n        // Check for error in the buffer\n        if (this.buffer.some(isErrorMessage)) {\n            if (this.errorCallback) {\n                this.errorCallback([...this.buffer]);\n            }\n        }\n        // Check for success in the buffer\n        if (this.buffer.some(isSuccessMessage)) {\n            this.clear();\n            if (this.successCallback) {\n                this.successCallback();\n            }\n        }\n    }\n\n    /**\n     * Clear the buffer.\n     */\n    clear() {\n        this.buffer = [];\n    }\n\n    /**\n     * Get the current buffer contents.\n     */\n    getBuffer() {\n        return [...this.buffer];\n    }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAQO,SAAS,cAAc,CAAc,EAAE,CAAc;IACxD,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE;QACtD,OAAO;IACX;IACA,OAAO;AACX;AAEO,SAAS,oBAAoB,OAAe;IAC/C,IAAI,QAAQ,UAAU,CAAC,QAAQ;QAC3B,OAAO;IACX;IACA,OAAO;AACX;AAEO,SAAS,eAAe,IAAY;IACvC,sBAAsB;IACtB,MAAM,gBAAgB;QAClB,iBAAiB;QACjB;QACA;QACA;QAEA;QACA;QACA;QACA;QAEA,0BAA0B;QAC1B;QACA;QACA;QACA;QACA;QAEA,gCAAgC;QAChC;QACA;QACA;QACA;QAEA,0BAA0B;QAC1B;QACA;QACA;QACA;QAEA,6BAA6B;QAC7B;QACA;KACH;IAED,IAAI,aAAa;IACjB,IAAI,cAAc,IAAI,CAAC,CAAC,UAAY,KAAK,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,MAAM;QACrF,aAAa;IACjB;IACA,OAAO;AACX;AAEO,SAAS,iBAAiB,IAAY;IACzC,MAAM,kBAAkB;QAAC;KAAY;IAErC,IAAI,gBAAgB,IAAI,CAAC,CAAC,UAAY,KAAK,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,MAAM;QACvF,OAAO;IACX;IACA,OAAO;AACX;AAGO,MAAM;IACD,SAAmB,EAAE,CAAC;IACb,SAAiB;IAC1B,cAA+C;IAC/C,gBAA6B;IAErC,YAAY,WAAmB,EAAE,CAAE;QAC/B,IAAI,CAAC,QAAQ,GAAG;IACpB;IAEA;;KAEC,GACD,QAAQ,QAAwC,EAAE;QAC9C,IAAI,CAAC,aAAa,GAAG;IACzB;IAEA;;KAEC,GACD,UAAU,QAAoB,EAAE;QAC5B,IAAI,CAAC,eAAe,GAAG;IAC3B;IAEA;;KAEC,GACD,QAAQ,IAAY,EAAE;QAClB,MAAM,aAAa,CAAA,GAAA,yIAAA,CAAA,UAAS,AAAD,EAAE;QAE7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,KAAK;QACrB;QACA,gCAAgC;QAChC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;YAClC,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,aAAa,CAAC;uBAAI,IAAI,CAAC,MAAM;iBAAC;YACvC;QACJ;QACA,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB;YACpC,IAAI,CAAC,KAAK;YACV,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe;YACxB;QACJ;IACJ;IAEA;;KAEC,GACD,QAAQ;QACJ,IAAI,CAAC,MAAM,GAAG,EAAE;IACpB;IAEA;;KAEC,GACD,YAAY;QACR,OAAO;eAAI,IAAI,CAAC,MAAM;SAAC;IAC3B;AACJ", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/file.ts"], "sourcesContent": ["import { BINARY_EXTENSIONS, IMAGE_EXTENSIONS } from '@onlook/constants';\r\nimport mime from 'mime-lite';\r\n\r\n/**\r\n * Check if a file is binary based on its extension\r\n * @param filename - The filename to check\r\n * @returns True if the file is binary, false otherwise\r\n */\r\nexport const isBinaryFile = (filename: string): boolean => {\r\n    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\r\n    return BINARY_EXTENSIONS.includes(ext);\r\n};\r\n\r\n/**\r\n * File operation interface for abstracted file operations\r\n */\r\nexport interface FileOperations {\r\n    readFile: (filePath: string) => Promise<string | null>;\r\n    writeFile: (filePath: string, content: string) => Promise<boolean>;\r\n    fileExists: (filePath: string) => Promise<boolean>;\r\n    delete: (filePath: string, recursive?: boolean) => Promise<boolean>;\r\n    copy: (\r\n        source: string,\r\n        destination: string,\r\n        recursive?: boolean,\r\n        overwrite?: boolean,\r\n    ) => Promise<boolean>;\r\n}\r\n\r\n/**\r\n * Updates .gitignore file to include a target entry\r\n * @param target - The entry to add to .gitignore\r\n * @param fileOps - File operations interface\r\n * @returns True if successfully updated, false otherwise\r\n */\r\nexport const updateGitignore = async (\r\n    target: string,\r\n    fileOps: FileOperations,\r\n): Promise<boolean> => {\r\n    const gitignorePath = '.gitignore';\r\n\r\n    try {\r\n        // Check if .gitignore exists\r\n        const gitignoreExists = await fileOps.fileExists(gitignorePath);\r\n\r\n        if (!gitignoreExists) {\r\n            // Create .gitignore with the target\r\n            await fileOps.writeFile(gitignorePath, target + '\\n');\r\n            return true;\r\n        }\r\n\r\n        // Read existing .gitignore content\r\n        const gitignoreContent = await fileOps.readFile(gitignorePath);\r\n        if (gitignoreContent === null) {\r\n            return false;\r\n        }\r\n\r\n        const lines = gitignoreContent.split(/\\r?\\n/);\r\n\r\n        // Look for exact match of target\r\n        if (!lines.some((line: string) => line.trim() === target)) {\r\n            // Ensure there's a newline before adding if the file doesn't end with one\r\n            const separator = gitignoreContent.endsWith('\\n') ? '' : '\\n';\r\n            await fileOps.writeFile(gitignorePath, gitignoreContent + `${separator}${target}\\n`);\r\n        }\r\n\r\n        return true;\r\n    } catch (error) {\r\n        console.error(`Failed to update .gitignore: ${error}`);\r\n        return false;\r\n    }\r\n};\r\n\r\nexport const getDirName = (filePath: string): string => {\r\n    const parts = filePath.split('/');\r\n    if (parts.length <= 1) return '.';\r\n    return parts.slice(0, -1).join('/') || '.';\r\n};\r\n\r\nexport const getBaseName = (filePath: string): string => {\r\n    const parts = filePath.split('/');\r\n    return parts.pop() || '';\r\n};\r\n\r\nexport const getMimeType = (fileName: string): string => {\r\n    const lowerCasedFileName = fileName.toLowerCase();\r\n\r\n    if (lowerCasedFileName.endsWith('.ico')) return 'image/x-icon';\r\n    if (lowerCasedFileName.endsWith('.png')) return 'image/png';\r\n    if (lowerCasedFileName.endsWith('.jpg') || lowerCasedFileName.endsWith('.jpeg'))\r\n        return 'image/jpeg';\r\n    if (lowerCasedFileName.endsWith('.svg')) return 'image/svg+xml';\r\n    if (lowerCasedFileName.endsWith('.gif')) return 'image/gif';\r\n    if (lowerCasedFileName.endsWith('.webp')) return 'image/webp';\r\n    if (lowerCasedFileName.endsWith('.bmp')) return 'image/bmp';\r\n    const res = mime.getType(fileName);\r\n    if (res) return res;\r\n    return 'application/octet-stream';\r\n};\r\n\r\nexport const isImageFile = (fileName: string): boolean => {\r\n    const mimeType = mime.getType(fileName);\r\n    return IMAGE_EXTENSIONS.includes(mimeType);\r\n};\r\n\r\nexport const convertToBase64 = (file: Uint8Array): string => {\r\n    return btoa(\r\n        Array.from(file)\r\n            .map((byte: number) => String.fromCharCode(byte))\r\n            .join(''),\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AACA;;;AAOO,MAAM,eAAe,CAAC;IACzB,MAAM,MAAM,SAAS,WAAW,GAAG,SAAS,CAAC,SAAS,WAAW,CAAC;IAClE,OAAO,wIAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC;AACtC;AAwBO,MAAM,kBAAkB,OAC3B,QACA;IAEA,MAAM,gBAAgB;IAEtB,IAAI;QACA,6BAA6B;QAC7B,MAAM,kBAAkB,MAAM,QAAQ,UAAU,CAAC;QAEjD,IAAI,CAAC,iBAAiB;YAClB,oCAAoC;YACpC,MAAM,QAAQ,SAAS,CAAC,eAAe,SAAS;YAChD,OAAO;QACX;QAEA,mCAAmC;QACnC,MAAM,mBAAmB,MAAM,QAAQ,QAAQ,CAAC;QAChD,IAAI,qBAAqB,MAAM;YAC3B,OAAO;QACX;QAEA,MAAM,QAAQ,iBAAiB,KAAK,CAAC;QAErC,iCAAiC;QACjC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,OAAiB,KAAK,IAAI,OAAO,SAAS;YACvD,0EAA0E;YAC1E,MAAM,YAAY,iBAAiB,QAAQ,CAAC,QAAQ,KAAK;YACzD,MAAM,QAAQ,SAAS,CAAC,eAAe,mBAAmB,GAAG,YAAY,OAAO,EAAE,CAAC;QACvF;QAEA,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO;QACrD,OAAO;IACX;AACJ;AAEO,MAAM,aAAa,CAAC;IACvB,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,IAAI,MAAM,MAAM,IAAI,GAAG,OAAO;IAC9B,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ;AAC3C;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,OAAO,MAAM,GAAG,MAAM;AAC1B;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,qBAAqB,SAAS,WAAW;IAE/C,IAAI,mBAAmB,QAAQ,CAAC,SAAS,OAAO;IAChD,IAAI,mBAAmB,QAAQ,CAAC,SAAS,OAAO;IAChD,IAAI,mBAAmB,QAAQ,CAAC,WAAW,mBAAmB,QAAQ,CAAC,UACnE,OAAO;IACX,IAAI,mBAAmB,QAAQ,CAAC,SAAS,OAAO;IAChD,IAAI,mBAAmB,QAAQ,CAAC,SAAS,OAAO;IAChD,IAAI,mBAAmB,QAAQ,CAAC,UAAU,OAAO;IACjD,IAAI,mBAAmB,QAAQ,CAAC,SAAS,OAAO;IAChD,MAAM,MAAM,+IAAA,CAAA,UAAI,CAAC,OAAO,CAAC;IACzB,IAAI,KAAK,OAAO;IAChB,OAAO;AACX;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,WAAW,+IAAA,CAAA,UAAI,CAAC,OAAO,CAAC;IAC9B,OAAO,wIAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC;AACrC;AAEO,MAAM,kBAAkB,CAAC;IAC5B,OAAO,KACH,MAAM,IAAI,CAAC,MACN,GAAG,CAAC,CAAC,OAAiB,OAAO,YAAY,CAAC,OAC1C,IAAI,CAAC;AAElB", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/id.ts"], "sourcesContent": ["import { EditorAttributes } from '@onlook/constants';\nimport { custom<PERSON>l<PERSON>bet } from 'nanoid';\n\nexport const VALID_DATA_ATTR_CHARS = 'abcdefghijklmnopqrstuvwxyz0123456789-._:';\n\nconst generateCustomId = customAlphabet(VALID_DATA_ATTR_CHARS, 7);\n\nexport function createDomId(): string {\n    return `odid-${generateCustomId()}`;\n}\n\nexport function createOid(): string {\n    return `${generateCustomId()}`;\n}\n\nexport function getOid(node: HTMLElement): string | undefined {\n    return node.getAttribute(EditorAttributes.DATA_ONLOOK_ID) as string;\n}\n\nexport function getInstanceId(node: HTMLElement): string | undefined {\n    return node.getAttribute(EditorAttributes.DATA_ONLOOK_INSTANCE_ID) as string;\n}\n\nexport function getDomId(node: HTMLElement): string | undefined {\n    return node.getAttribute(EditorAttributes.DATA_ONLOOK_DOM_ID) as string;\n}\n\n/**\n * Shortens a UUID; maintains uniqueness probabilistically (collisions are possible).\n */\n\nexport function shortenUuid(uuid: string, maxLength: number): string {\n    let hash = 0;\n    for (let i = 0; i < uuid.length; i++) {\n        const char = uuid.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n    }\n\n    // Convert to base36 (alphanumeric) for compact representation\n    const base36 = Math.abs(hash).toString(36);\n\n    // Pad with leading zeros if needed\n    const padded = base36.padStart(maxLength, '0');\n\n    // Truncate if longer than maxLength\n    return padded.slice(-maxLength);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AACA;;;AAEO,MAAM,wBAAwB;AAErC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,uBAAuB;AAExD,SAAS;IACZ,OAAO,CAAC,KAAK,EAAE,oBAAoB;AACvC;AAEO,SAAS;IACZ,OAAO,GAAG,oBAAoB;AAClC;AAEO,SAAS,OAAO,IAAiB;IACpC,OAAO,KAAK,YAAY,CAAC,yIAAA,CAAA,mBAAgB,CAAC,cAAc;AAC5D;AAEO,SAAS,cAAc,IAAiB;IAC3C,OAAO,KAAK,YAAY,CAAC,yIAAA,CAAA,mBAAgB,CAAC,uBAAuB;AACrE;AAEO,SAAS,SAAS,IAAiB;IACtC,OAAO,KAAK,YAAY,CAAC,yIAAA,CAAA,mBAAgB,CAAC,kBAAkB;AAChE;AAMO,SAAS,YAAY,IAAY,EAAE,SAAiB;IACvD,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,KAAK,UAAU,CAAC;QAC7B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;IAChC;IAEA,8DAA8D;IAC9D,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;IAEvC,mCAAmC;IACnC,MAAM,SAAS,OAAO,QAAQ,CAAC,WAAW;IAE1C,oCAAoC;IACpC,OAAO,OAAO,KAAK,CAAC,CAAC;AACzB", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/font.ts"], "sourcesContent": ["import { DEFAULT, VARIANTS } from '@onlook/fonts';\nimport { camelCase } from 'lodash';\n\n/**\n * Extracts the actual font name from a font file name\n * Removes file extensions, weight/style indicators, and other common suffixes\n */\n\ninterface FontParts {\n    family: string;\n    weight: string;\n    style: string;\n}\n\nfunction extractFontParts(fileName: string): FontParts {\n    // Remove file extension\n    let name = fileName.replace(/\\.[^/.]+$/, '');\n\n    // Define common font weight and style terms\n    const weightTerms = [\n        'extra light',\n        'ultra light',\n        'extra black',\n        'ultra black',\n        'extralight',\n        'ultralight',\n        'extra bold',\n        'ultra bold',\n        'extrablack',\n        'ultrablack',\n        'semi bold',\n        'demi bold',\n        'extrabold',\n        'ultrabold',\n        'hairline',\n        'semibold',\n        'demibold',\n        'regular',\n        'medium',\n        'normal',\n        'light',\n        'black',\n        'heavy',\n        'thin',\n        'bold',\n        'book',\n    ];\n\n    const styleTerms = ['italic', 'oblique', 'slanted', 'kursiv'];\n\n    let family = '';\n    let weight = '';\n    let style = '';\n\n    // Special case: If the name contains spaces without hyphens or underscores, return as title case\n    if (/\\s/.test(name) && !/[-_]/.test(name)) {\n        family = toTitleCase(name);\n        return { family, weight: '', style: '' }; // Exit early for names with spaces but no delimiters\n    }\n\n    const parts = name.split(/[-_\\s]+/);\n\n    // Filter out weight terms, style terms, and numeric weights\n    const filteredParts = parts.filter((part) => {\n        const lowerPart = part.toLowerCase();\n        // Check if part is a weight term or style term\n        if (weightTerms.includes(lowerPart)) {\n            weight = lowerPart;\n            return false;\n        }\n\n        if (styleTerms.includes(lowerPart)) {\n            style = lowerPart;\n            return false;\n        }\n\n        // Check for combined terms like \"BoldItalic\"\n        for (const weightTerm of weightTerms) {\n            for (const styleTerm of styleTerms) {\n                const combined = weightTerm + styleTerm;\n                if (lowerPart === combined) {\n                    weight = weightTerm;\n                    style = styleTerm;\n                    return false;\n                }\n            }\n        }\n\n        // Check for numeric weights (e.g., 100, 300, 700)\n        if (/^\\d+(?:wt|weight)?$/.test(part)) {\n            weight = part.replace(/[^\\d]/g, '');\n            return false;\n        }\n\n        return true;\n    });\n\n    if (!family) {\n        family = filteredParts.join(' ');\n        family = family.replace(/([a-z])([A-Z])/g, '$1 $2');\n        family = toTitleCase(family);\n    }\n\n    // Convert weight to numeric value\n    if (weight) {\n        let match = VARIANTS.find((variant) => variant.name.toLowerCase() === weight.toLowerCase());\n\n        if (!match && /^\\d+$/.test(weight)) {\n            match = VARIANTS.find((variant) => variant.value === weight);\n        }\n\n        if (!match) {\n            const weightLower = weight.toLowerCase();\n            const weightNormalized = weightLower.replace(/\\s+/g, '');\n\n            // First try to find exact matches (normalized for spaces)\n            match = VARIANTS.find((variant) => {\n                const variantLower = variant.name.toLowerCase();\n                const variantNormalized = variantLower.replace(/\\s+/g, '');\n\n                return weightNormalized === variantNormalized || weightLower === variantLower;\n            });\n\n            // If no exact match found, then try partial matches\n            if (!match) {\n                match = VARIANTS.find((variant) => {\n                    const variantLower = variant.name.toLowerCase();\n\n                    return (\n                        weightLower.includes(variantLower) &&\n                        // Only allow partial matches for single-word variants\n                        !variantLower.includes(' ')\n                    );\n                });\n            }\n        }\n\n        weight = match?.value || weight;\n    }\n\n    if (!style) {\n        style = DEFAULT.STYLE;\n    }\n\n    if (!weight) {\n        weight = DEFAULT.WEIGHT;\n    }\n\n    return { family, weight, style };\n}\n\nfunction toTitleCase(str: string): string {\n    return str\n        .split(' ')\n        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n        .join(' ');\n}\n\n/**\n * Gets a descriptive name for a font file based on its weight and style\n */\nfunction getFontFileName(baseName: string, weight: string, style: string): string {\n    const weightMap: Record<string, string> = {\n        '100': 'Thin',\n        '200': 'ExtraLight',\n        '300': 'Light',\n        '400': 'Regular',\n        '500': 'Medium',\n        '600': 'SemiBold',\n        '700': 'Bold',\n        '800': 'ExtraBold',\n        '900': 'Black',\n    };\n\n    const weightName = weightMap[weight] || weight;\n    const styleName =\n        style.toLowerCase() === 'normal' ? '' : style.charAt(0).toUpperCase() + style.slice(1);\n\n    return `${baseName.replace(/\\s+/g, '')}${weightName}${styleName}`;\n}\n\n/**\n * Converts a font string like \"__Advent_Pro_[hash], __Advent_Pro_Fallback_[hash], sans-serif\" to \"adventPro\"\n */\nfunction convertFontString(fontString: string): string {\n    if (!fontString) {\n        return '';\n    }\n\n    const firstFont = fontString.split(',')[0]?.trim();\n    const cleanFont = firstFont?.replace(/^__/, '').replace(/_[a-f0-9]+$/, '');\n    const withoutFallback = cleanFont?.replace(/_Fallback$/, '');\n\n    return camelCase(withoutFallback);\n}\n\n/**\n * Converts a font weight string to a human-readable name\n * @param weight - The weight value to convert\n * @returns The human-readable name of the weight, or the original weight if no match is found\n */\n\nfunction convertFontWeight(weight: string): string {\n    return VARIANTS.find((variant) => variant.value === weight)?.name ?? weight;\n}\nexport { convertFontString, convertFontWeight, extractFontParts, getFontFileName };\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AACA;;;AAaA,SAAS,iBAAiB,QAAgB;IACtC,wBAAwB;IACxB,IAAI,OAAO,SAAS,OAAO,CAAC,aAAa;IAEzC,4CAA4C;IAC5C,MAAM,cAAc;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IAED,MAAM,aAAa;QAAC;QAAU;QAAW;QAAW;KAAS;IAE7D,IAAI,SAAS;IACb,IAAI,SAAS;IACb,IAAI,QAAQ;IAEZ,iGAAiG;IACjG,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,OAAO;QACvC,SAAS,YAAY;QACrB,OAAO;YAAE;YAAQ,QAAQ;YAAI,OAAO;QAAG,GAAG,qDAAqD;IACnG;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC;IAEzB,4DAA4D;IAC5D,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC;QAChC,MAAM,YAAY,KAAK,WAAW;QAClC,+CAA+C;QAC/C,IAAI,YAAY,QAAQ,CAAC,YAAY;YACjC,SAAS;YACT,OAAO;QACX;QAEA,IAAI,WAAW,QAAQ,CAAC,YAAY;YAChC,QAAQ;YACR,OAAO;QACX;QAEA,6CAA6C;QAC7C,KAAK,MAAM,cAAc,YAAa;YAClC,KAAK,MAAM,aAAa,WAAY;gBAChC,MAAM,WAAW,aAAa;gBAC9B,IAAI,cAAc,UAAU;oBACxB,SAAS;oBACT,QAAQ;oBACR,OAAO;gBACX;YACJ;QACJ;QAEA,kDAAkD;QAClD,IAAI,sBAAsB,IAAI,CAAC,OAAO;YAClC,SAAS,KAAK,OAAO,CAAC,UAAU;YAChC,OAAO;QACX;QAEA,OAAO;IACX;IAEA,IAAI,CAAC,QAAQ;QACT,SAAS,cAAc,IAAI,CAAC;QAC5B,SAAS,OAAO,OAAO,CAAC,mBAAmB;QAC3C,SAAS,YAAY;IACzB;IAEA,kCAAkC;IAClC,IAAI,QAAQ;QACR,IAAI,QAAQ,uIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,WAAW;QAExF,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS;YAChC,QAAQ,uIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,KAAK,KAAK;QACzD;QAEA,IAAI,CAAC,OAAO;YACR,MAAM,cAAc,OAAO,WAAW;YACtC,MAAM,mBAAmB,YAAY,OAAO,CAAC,QAAQ;YAErD,0DAA0D;YAC1D,QAAQ,uIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,CAAC;gBACnB,MAAM,eAAe,QAAQ,IAAI,CAAC,WAAW;gBAC7C,MAAM,oBAAoB,aAAa,OAAO,CAAC,QAAQ;gBAEvD,OAAO,qBAAqB,qBAAqB,gBAAgB;YACrE;YAEA,oDAAoD;YACpD,IAAI,CAAC,OAAO;gBACR,QAAQ,uIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,CAAC;oBACnB,MAAM,eAAe,QAAQ,IAAI,CAAC,WAAW;oBAE7C,OACI,YAAY,QAAQ,CAAC,iBACrB,sDAAsD;oBACtD,CAAC,aAAa,QAAQ,CAAC;gBAE/B;YACJ;QACJ;QAEA,SAAS,OAAO,SAAS;IAC7B;IAEA,IAAI,CAAC,OAAO;QACR,QAAQ,sIAAA,CAAA,UAAO,CAAC,KAAK;IACzB;IAEA,IAAI,CAAC,QAAQ;QACT,SAAS,sIAAA,CAAA,UAAO,CAAC,MAAM;IAC3B;IAEA,OAAO;QAAE;QAAQ;QAAQ;IAAM;AACnC;AAEA,SAAS,YAAY,GAAW;IAC5B,OAAO,IACF,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACtE,IAAI,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,gBAAgB,QAAgB,EAAE,MAAc,EAAE,KAAa;IACpE,MAAM,YAAoC;QACtC,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IAEA,MAAM,aAAa,SAAS,CAAC,OAAO,IAAI;IACxC,MAAM,YACF,MAAM,WAAW,OAAO,WAAW,KAAK,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;IAExF,OAAO,GAAG,SAAS,OAAO,CAAC,QAAQ,MAAM,aAAa,WAAW;AACrE;AAEA;;CAEC,GACD,SAAS,kBAAkB,UAAkB;IACzC,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IAEA,MAAM,YAAY,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IAC5C,MAAM,YAAY,WAAW,QAAQ,OAAO,IAAI,QAAQ,eAAe;IACvE,MAAM,kBAAkB,WAAW,QAAQ,cAAc;IAEzD,OAAO,CAAA,GAAA,sIAAA,CAAA,UAAS,AAAD,EAAE;AACrB;AAEA;;;;CAIC,GAED,SAAS,kBAAkB,MAAc;IACrC,OAAO,uIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,KAAK,KAAK,SAAS,QAAQ;AACzE", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/image.ts"], "sourcesContent": ["import imageCompression from 'browser-image-compression';\n\n// Browser-side image compression\nexport async function compressImageInBrowser(file: File): Promise<string | undefined> {\n    const options = {\n        maxSizeMB: 2,\n        maxWidthOrHeight: 2048,\n    };\n\n    try {\n        const compressedFile = await imageCompression(file, options);\n        const base64URL = imageCompression.getDataUrlFromFile(compressedFile);\n        console.log(`Image size reduced from ${file.size} to ${compressedFile.size} (bytes)`);\n        return base64URL;\n    } catch (error) {\n        console.error('Error compressing image:', error);\n    }\n}\n\nexport function base64ToBlob(base64: string, mimeType: string): Blob {\n    const byteString = atob(base64.split(',')[1] ?? '');\n    const ab = new ArrayBuffer(byteString.length);\n    const ia = new Uint8Array(ab);\n    for (let i = 0; i < byteString.length; i++) {\n        ia[i] = byteString.charCodeAt(i);\n    }\n    return new Blob([ab], { type: mimeType });\n}\n\nexport function addBase64Prefix(mimeType: string, base64: string): string {\n    if (base64.startsWith('data:')) {\n        // If the base64 already has a prefix, return it\n        return base64;\n    }\n    return `data:${mimeType};base64,${base64}`;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAGO,eAAe,uBAAuB,IAAU;IACnD,MAAM,UAAU;QACZ,WAAW;QACX,kBAAkB;IACtB;IAEA,IAAI;QACA,MAAM,iBAAiB,MAAM,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,MAAM;QACpD,MAAM,YAAY,8LAAA,CAAA,UAAgB,CAAC,kBAAkB,CAAC;QACtD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,QAAQ,CAAC;QACpF,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,4BAA4B;IAC9C;AACJ;AAEO,SAAS,aAAa,MAAc,EAAE,QAAgB;IACzD,MAAM,aAAa,KAAK,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;IAChD,MAAM,KAAK,IAAI,YAAY,WAAW,MAAM;IAC5C,MAAM,KAAK,IAAI,WAAW;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QACxC,EAAE,CAAC,EAAE,GAAG,WAAW,UAAU,CAAC;IAClC;IACA,OAAO,IAAI,KAAK;QAAC;KAAG,EAAE;QAAE,MAAM;IAAS;AAC3C;AAEO,SAAS,gBAAgB,QAAgB,EAAE,MAAc;IAC5D,IAAI,OAAO,UAAU,CAAC,UAAU;QAC5B,gDAAgD;QAChD,OAAO;IACX;IACA,OAAO,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,QAAQ;AAC9C", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/initials.ts"], "sourcesContent": ["export const getInitials = (name: string) => {\n    return name\n        ?.split(' ')\n        ?.map((word) => word[0])\n        ?.join('')\n        ?.toUpperCase();\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,cAAc,CAAC;IACxB,OAAO,MACD,MAAM,MACN,IAAI,CAAC,OAAS,IAAI,CAAC,EAAE,GACrB,KAAK,KACL;AACV", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/null.ts"], "sourcesContent": ["export function isNullOrUndefined(value: any) {\n    return value === null || value === undefined;\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,kBAAkB,KAAU;IACxC,OAAO,UAAU,QAAQ,UAAU;AACvC", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/path.ts"], "sourcesContent": ["import isSubdir from 'is-subdir';\nimport path from 'path';\n\n// Utility to normalize paths for comparison (handles Windows and POSIX)\nfunction normalize(p: string): string {\n    if (typeof p !== 'string' || !p) return '';\n    let np = p.replace(/\\\\/g, '/');\n    // Lowercase drive letter for Windows\n    if (typeof np === 'string' && np.length > 0 && /^[A-Za-z]:\\//.test(np)) {\n        np = np[0]?.toLowerCase() + np.slice(1);\n    }\n    return np;\n}\n\n// See: https://www.npmjs.com/package/is-subdir\n// isSubdir(parentDir, subdir) returns true if subdir is the same as or inside parentDir\nexport function isSubdirectory(filePath: string, directories: string[]): boolean {\n    const absFilePath = path.resolve(filePath);\n    const normFilePath = normalize(absFilePath);\n    for (const directory of directories) {\n        const absDirectory = path.resolve(directory);\n        const normDirectory = normalize(absDirectory);\n        // Standard is-subdir check\n        if (isSubdir(normDirectory, normFilePath)) {\n            return true;\n        }\n        // If directory is a simple name (like 'foo' or '.git'), check if filePath contains it as a segment\n        if (\n            !directory.includes(path.sep) &&\n            !directory.includes('/') &&\n            !directory.includes('\\\\')\n        ) {\n            const segments = normFilePath.split('/');\n            if (segments.includes(directory)) {\n                return true;\n            }\n        }\n        // Enhanced: handle mixed absolute/relative by checking if directory segments appear in file path\n        const dirSegments = normalize(directory).split('/').filter(Boolean);\n        const fileSegments = normFilePath.split('/').filter(Boolean);\n        if (dirSegments.length > 0 && fileSegments.length >= dirSegments.length) {\n            for (let i = 0; i <= fileSegments.length - dirSegments.length; i++) {\n                let match = true;\n                for (let j = 0; j < dirSegments.length; j++) {\n                    if (fileSegments[i + j] !== dirSegments[j]) {\n                        match = false;\n                        break;\n                    }\n                }\n                if (match) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wEAAwE;AACxE,SAAS,UAAU,CAAS;IACxB,IAAI,OAAO,MAAM,YAAY,CAAC,GAAG,OAAO;IACxC,IAAI,KAAK,EAAE,OAAO,CAAC,OAAO;IAC1B,qCAAqC;IACrC,IAAI,OAAO,OAAO,YAAY,GAAG,MAAM,GAAG,KAAK,eAAe,IAAI,CAAC,KAAK;QACpE,KAAK,EAAE,CAAC,EAAE,EAAE,gBAAgB,GAAG,KAAK,CAAC;IACzC;IACA,OAAO;AACX;AAIO,SAAS,eAAe,QAAgB,EAAE,WAAqB;IAClE,MAAM,cAAc,0KAAA,CAAA,UAAI,CAAC,OAAO,CAAC;IACjC,MAAM,eAAe,UAAU;IAC/B,KAAK,MAAM,aAAa,YAAa;QACjC,MAAM,eAAe,0KAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAClC,MAAM,gBAAgB,UAAU;QAChC,2BAA2B;QAC3B,IAAI,CAAA,GAAA,wIAAA,CAAA,UAAQ,AAAD,EAAE,eAAe,eAAe;YACvC,OAAO;QACX;QACA,mGAAmG;QACnG,IACI,CAAC,UAAU,QAAQ,CAAC,0KAAA,CAAA,UAAI,CAAC,GAAG,KAC5B,CAAC,UAAU,QAAQ,CAAC,QACpB,CAAC,UAAU,QAAQ,CAAC,OACtB;YACE,MAAM,WAAW,aAAa,KAAK,CAAC;YACpC,IAAI,SAAS,QAAQ,CAAC,YAAY;gBAC9B,OAAO;YACX;QACJ;QACA,iGAAiG;QACjG,MAAM,cAAc,UAAU,WAAW,KAAK,CAAC,KAAK,MAAM,CAAC;QAC3D,MAAM,eAAe,aAAa,KAAK,CAAC,KAAK,MAAM,CAAC;QACpD,IAAI,YAAY,MAAM,GAAG,KAAK,aAAa,MAAM,IAAI,YAAY,MAAM,EAAE;YACrE,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,MAAM,GAAG,YAAY,MAAM,EAAE,IAAK;gBAChE,IAAI,QAAQ;gBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;oBACzC,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC,EAAE,EAAE;wBACxC,QAAQ;wBACR;oBACJ;gBACJ;gBACA,IAAI,OAAO;oBACP,OAAO;gBACX;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/screenshot.ts"], "sourcesContent": ["export function getScreenshotPath(projectId: string, mimeType: string) {\n    const extension = mimeType.split('/')[1];\n    return `public/${projectId}/${Date.now()}.${extension}`;\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,kBAAkB,SAAiB,EAAE,QAAgB;IACjE,MAAM,YAAY,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE;IACxC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,WAAW;AAC3D", "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/string.ts"], "sourcesContent": ["import { camelCase } from 'lodash';\n\nexport function isEmptyString(str: string): boolean {\n    return str.trim() === '';\n}\n\nexport function isString(value: any): boolean {\n    return typeof value === 'string';\n}\n\nexport function toNormalCase(str: string): string {\n    if (!str) {\n        return '';\n    }\n\n    // Handle already normal case strings (words separated by spaces, first letter of each word capitalized)\n    if (/^[A-Z][a-z]*( [A-Z][a-z]*)*$/.test(str)) {\n        return str;\n    }\n\n    // Skip if the string is fully uppercase\n    if (str === str.toUpperCase()) {\n        return str;\n    }\n\n    // For other cases, convert camelCase/PascalCase to normal case\n\n    return str\n        .replace(/([A-Z])/g, ' $1')\n        .trim()\n        .replace(/^\\w/, (c) => c.toUpperCase());\n}\n\nexport function capitalizeFirstLetter(string: string) {\n    return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\n/**\n * Generates a unique name by appending a number to the base name until it doesn't conflict with existing names.\n * The comparison is done using camelCase to ensure consistent name formatting.\n * @param baseName The base name to start with\n * @param existingNames Array of existing names to check against\n * @param transformFn Optional function to transform the name before comparison (defaults to camelCase)\n * @returns A unique name that doesn't conflict with existing names\n */\nexport function generateUniqueName(\n    baseName: string,\n    existingNames: string[],\n    transformFn: (str: string) => string = camelCase,\n): string {\n    let counter = 1;\n    let newName = `${baseName} ${counter}`;\n    let transformedName = transformFn(newName);\n\n    while (existingNames.includes(transformedName)) {\n        counter++;\n        newName = `${baseName} ${counter}`;\n        transformedName = transformFn(newName);\n    }\n\n    return newName;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,SAAS,cAAc,GAAW;IACrC,OAAO,IAAI,IAAI,OAAO;AAC1B;AAEO,SAAS,SAAS,KAAU;IAC/B,OAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,aAAa,GAAW;IACpC,IAAI,CAAC,KAAK;QACN,OAAO;IACX;IAEA,wGAAwG;IACxG,IAAI,+BAA+B,IAAI,CAAC,MAAM;QAC1C,OAAO;IACX;IAEA,wCAAwC;IACxC,IAAI,QAAQ,IAAI,WAAW,IAAI;QAC3B,OAAO;IACX;IAEA,+DAA+D;IAE/D,OAAO,IACF,OAAO,CAAC,YAAY,OACpB,IAAI,GACJ,OAAO,CAAC,OAAO,CAAC,IAAM,EAAE,WAAW;AAC5C;AAEO,SAAS,sBAAsB,MAAc;IAChD,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACzD;AAUO,SAAS,mBACZ,QAAgB,EAChB,aAAuB,EACvB,cAAuC,sIAAA,CAAA,UAAS;IAEhD,IAAI,UAAU;IACd,IAAI,UAAU,GAAG,SAAS,CAAC,EAAE,SAAS;IACtC,IAAI,kBAAkB,YAAY;IAElC,MAAO,cAAc,QAAQ,CAAC,iBAAkB;QAC5C;QACA,UAAU,GAAG,SAAS,CAAC,EAAE,SAAS;QAClC,kBAAkB,YAAY;IAClC;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/tailwind.ts"], "sourcesContent": ["// @ts-nocheck\n\nexport interface ResultCode {\n    selectorName: string;\n    resultVal: string;\n}\n\nexport const specialAttribute = ['@charset', '@font-face', '@import', '@keyframes'];\n\nlet useAllDefaultValues = false;\nlet customTheme: CustomTheme = {};\n\nconst hasNegative = (val: string): ['-' | '', string] => [\n    val[0] === '-' ? '-' : '',\n    val[0] === '-' ? val.slice(1) : val,\n];\nconst getCustomVal = (val: string) => {\n    val = val.replace(/\\s/g, '_');\n    for (let index = 1; index < val.length; index) {\n        const char = val[index];\n        if (char === '_' && char === val[index - 1]) {\n            val = val.slice(0, index) + val.slice(index + 1);\n        } else {\n            index++;\n        }\n    }\n    return val;\n};\n\nconst isColor = (str: string, joinLinearGradient = false) => {\n    const namedColors = [\n        'initial',\n        'inherit',\n        'currentColor',\n        'currentcolor',\n        'transparent',\n        'aliceblue',\n        'antiquewhite',\n        'aqua',\n        'aquamarine',\n        'azure',\n        'beige',\n        'bisque',\n        'black',\n        'blanchedalmond',\n        'blue',\n        'blueviolet',\n        'brown',\n        'burlywood',\n        'cadetblue',\n        'chartreuse',\n        'chocolate',\n        'coral',\n        'cornflowerblue',\n        'cornsilk',\n        'crimson',\n        'cyan',\n        'darkblue',\n        'darkcyan',\n        'darkgoldenrod',\n        'darkgray',\n        'darkgrey',\n        'darkgreen',\n        'darkkhaki',\n        'darkmagenta',\n        'darkolivegreen',\n        'darkorange',\n        'darkorchid',\n        'darkred',\n        'darksalmon',\n        'darkseagreen',\n        'darkslateblue',\n        'darkslategray',\n        'darkslategrey',\n        'darkturquoise',\n        'darkviolet',\n        'deeppink',\n        'deepskyblue',\n        'dimgray',\n        'dimgrey',\n        'dodgerblue',\n        'firebrick',\n        'floralwhite',\n        'forestgreen',\n        'fuchsia',\n        'gainsboro',\n        'ghostwhite',\n        'gold',\n        'goldenrod',\n        'gray',\n        'grey',\n        'green',\n        'greenyellow',\n        'honeydew',\n        'hotpink',\n        'indianred',\n        'indigo',\n        'ivory',\n        'khaki',\n        'lavender',\n        'lavenderblush',\n        'lawngreen',\n        'lemonchiffon',\n        'lightblue',\n        'lightcoral',\n        'lightcyan',\n        'lightgoldenrodyellow',\n        'lightgray',\n        'lightgrey',\n        'lightgreen',\n        'lightpink',\n        'lightsalmon',\n        'lightseagreen',\n        'lightskyblue',\n        'lightslategray',\n        'lightslategrey',\n        'lightsteelblue',\n        'lightyellow',\n        'lime',\n        'limegreen',\n        'linen',\n        'magenta',\n        'maroon',\n        'mediumaquamarine',\n        'mediumblue',\n        'mediumorchid',\n        'mediumpurple',\n        'mediumseagreen',\n        'mediumslateblue',\n        'mediumspringgreen',\n        'mediumturquoise',\n        'mediumvioletred',\n        'midnightblue',\n        'mintcream',\n        'mistyrose',\n        'moccasin',\n        'navajowhite',\n        'navy',\n        'oldlace',\n        'olive',\n        'olivedrab',\n        'orange',\n        'orangered',\n        'orchid',\n        'palegoldenrod',\n        'palegreen',\n        'paleturquoise',\n        'palevioletred',\n        'papayawhip',\n        'peachpuff',\n        'peru',\n        'pink',\n        'plum',\n        'powderblue',\n        'purple',\n        'rebeccapurple',\n        'red',\n        'rosybrown',\n        'royalblue',\n        'saddlebrown',\n        'salmon',\n        'sandybrown',\n        'seagreen',\n        'seashell',\n        'sienna',\n        'silver',\n        'skyblue',\n        'slateblue',\n        'slategray',\n        'slategrey',\n        'snow',\n        'springgreen',\n        'steelblue',\n        'tan',\n        'teal',\n        'thistle',\n        'tomato',\n        'turquoise',\n        'violet',\n        'wheat',\n        'white',\n        'whitesmoke',\n        'yellow',\n        'yellowgreen',\n    ];\n    const regexp =\n        /^\\s*#([0-9a-fA-F]{3}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\s*$|^\\s*rgb\\(\\s*(\\d{1,3}|[a-z]+)\\s*,\\s*(\\d{1,3}|[a-z]+)\\s*,\\s*(\\d{1,3}|[a-z]+)\\s*\\)\\s*$|^\\s*rgba\\(\\s*(\\d{1,3}|[a-z]+)\\s*,\\s*(\\d{1,3}|[a-z]+)\\s*,\\s*(\\d{1,3}|[a-z]+)\\s*,\\s*(\\d*(\\.\\d+)?)\\s*\\)\\s*$|^\\s*hsl\\(\\s*(\\d+)\\s*,\\s*(\\d*(\\.\\d+)?%)\\s*,\\s*(\\d*(\\.\\d+)?%)\\)\\s*$|^\\s*hsla\\((\\d+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%\\s*,\\s*(\\d*(\\.\\d+)?)\\)\\s*$/i;\n    return (\n        regexp.test(str) ||\n        namedColors.includes(str) ||\n        (joinLinearGradient && /^\\s*linear-gradient\\([\\w\\W]+?\\)\\s*$/.test(str))\n    );\n};\n\nconst isUnit = (str: string) => {\n    if (str.length === 0) {\n        return false;\n    }\n    return (\n        [\n            'em',\n            'ex',\n            'ch',\n            'rem',\n            'vw',\n            'vh',\n            'vmin',\n            'vmax',\n            'cm',\n            'mm',\n            'in',\n            'pt',\n            'pc',\n            'px',\n            'min-content',\n            'max-content',\n            'fit-content',\n            'deg',\n            'grad',\n            'rad',\n            'turn',\n            'ms',\n            's',\n            'Hz',\n            'kHz',\n            '%',\n            'length',\n            'inherit',\n            'thick',\n            'medium',\n            'thin',\n            'initial',\n            'auto',\n        ].includes(str.replace(/[.\\d\\s-]/g, '')) ||\n        /^[-.\\d]+$/.test(str.trim()) ||\n        /^var\\(.+\\)$/.test(str)\n    );\n};\n\nenum CustomSelect {\n    auto = 'auto',\n    vh = '100vh',\n    vw = '100vw',\n}\n\nconst getUnitMetacharactersVal = (\n    val: string,\n    excludes: CustomSelect[] = [],\n): string | undefined => {\n    if (/^\\d+\\.[1-9]{2,}%$/.test(val)) {\n        val = `${Number(val.slice(0, -1))\n            .toFixed(6)\n            .replace(/(\\.[1-9]{2})\\d+/, '$1')}%`;\n    }\n    const config: Record<string, string> = {\n        auto: 'auto',\n        '50%': '1/2',\n        '33.33%': '1/3',\n        '66.66%': '2/3',\n        '25%': '1/4',\n        '75%': '3/4',\n        '20%': '1/5',\n        '40%': '2/5',\n        '60%': '3/5',\n        '80%': '4/5',\n        '16.66%': '1/6',\n        '83.33%': '5/6',\n        '8.33%': '1/12',\n        '41.66%': '5/12',\n        '58.33%': '7/12',\n        '91.66%': '11/12',\n        '100%': 'full',\n        '100vw': 'screen',\n        '100vh': 'screen',\n        'min-content': 'min',\n        'max-content': 'max',\n    };\n    excludes.forEach((key) => {\n        delete config[key];\n    });\n    return config[val];\n};\n\nconst getRemDefaultVal = (val: string) => {\n    return {\n        '0px': '0',\n        '1px': 'px',\n        '0.125rem': '0.5',\n        '0.25rem': '1',\n        '0.375rem': '1.5',\n        '0.5rem': '2',\n        '0.625rem': '2.5',\n        '0.75rem': '3',\n        '0.875rem': '3.5',\n        '1rem': '4',\n        '1.25rem': '5',\n        '1.5rem': '6',\n        '1.75rem': '7',\n        '2rem': '8',\n        '2.25rem': '9',\n        '2.5rem': '10',\n        '2.75rem': '11',\n        '3rem': '12',\n        '3.5rem': '14',\n        '4rem': '16',\n        '5rem': '20',\n        '6rem': '24',\n        '7rem': '28',\n        '8rem': '32',\n        '9rem': '36',\n        '10rem': '40',\n        '11rem': '44',\n        '12rem': '48',\n        '13rem': '52',\n        '14rem': '56',\n        '15rem': '60',\n        '16rem': '64',\n        '18rem': '72',\n        '20rem': '80',\n        '24rem': '96',\n    }[val];\n};\n\nconst getBorderRadiusDefaultVal = (val: string) => {\n    return {\n        '0px': '-none',\n        '0.125rem': '-sm',\n        '0.25rem': 'null',\n        '0.375rem': '-md',\n        '0.5rem': '-lg',\n        '0.75rem': '-xl',\n        '1rem': '-2xl',\n        '1.5rem': '-3xl',\n        '9999px': '-full',\n    }[val];\n};\n\nconst getFilterDefaultVal = (val: string) => {\n    return {\n        'blur(0)': 'blur-none',\n        'blur(4px)': 'blur-sm',\n        'blur(8px)': 'blur',\n        'blur(12px)': 'blur-md',\n        'blur(16px)': 'blur-lg',\n        'blur(24px)': 'blur-xl',\n        'blur(40px)': 'blur-2xl',\n        'blur(64px)': 'blur-3xl',\n        'brightness(0)': 'brightness-0',\n        'brightness(.5)': 'brightness-50',\n        'brightness(.75)': 'brightness-75',\n        'brightness(.9)': 'brightness-90',\n        'brightness(.95)': 'brightness-95',\n        'brightness(1)': 'brightness-100',\n        'brightness(1.05)': 'brightness-105',\n        'brightness(1.1)': 'brightness-110',\n        'brightness(1.25)': 'brightness-125',\n        'brightness(1.5)': 'brightness-150',\n        'brightness(2)': 'brightness-200',\n        'contrast(0)': 'contrast-0',\n        'contrast(.5)': 'contrast-50',\n        'contrast(.75)': 'contrast-75',\n        'contrast(1)': 'contrast-100',\n        'contrast(1.25)': 'contrast-125',\n        'contrast(1.5)': 'contrast-150',\n        'contrast(2)': 'contrast-200',\n        'drop-shadow(0 1px 1px rgba(0,0,0,0.05))': 'drop-shadow-sm',\n        'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) drop-shadow(0 1px 1px rgba(0, 0, 0, 0.06))':\n            'drop-shadow',\n        'drop-shadow(0 4px 3px rgba(0, 0, 0, 0.07)) drop-shadow(0 2px 2px rgba(0, 0, 0, 0.06))':\n            'drop-shadow-md',\n        'drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04)) drop-shadow(0 4px 3px rgba(0, 0, 0, 0.1))':\n            'drop-shadow-lg',\n        'drop-shadow(0 20px 13px rgba(0, 0, 0, 0.03)) drop-shadow(0 8px 5px rgba(0, 0, 0, 0.08))':\n            'drop-shadow-xl',\n        'drop-shadow(0 25px 25px rgba(0, 0, 0, 0.15))': 'drop-shadow-2xl',\n        'drop-shadow(0 0 #0000)': 'drop-shadow-none',\n        'grayscale(0)': 'grayscale-0',\n        'grayscale(1)': 'grayscale',\n        'hue-rotate(-180deg)': '-hue-rotate-180',\n        'hue-rotate(-90deg)': '-hue-rotate-90',\n        'hue-rotate(-60deg)': '-hue-rotate-60',\n        'hue-rotate(-30deg)': '-hue-rotate-30',\n        'hue-rotate(-15deg)': '-hue-rotate-15',\n        'hue-rotate(0deg)': 'hue-rotate-0',\n        'hue-rotate(15deg)': 'hue-rotate-15',\n        'hue-rotate(30deg)': 'hue-rotate-30',\n        'hue-rotate(60deg)': 'hue-rotate-60',\n        'hue-rotate(90deg)': 'hue-rotate-90',\n        'hue-rotate(180deg)': 'hue-rotate-180',\n        'invert(0)': 'invert-0',\n        'invert(1)': 'invert',\n        'saturate(0)': 'saturate-0',\n        'saturate(.5)': 'saturate-50',\n        'saturate(1)': 'saturate-100',\n        'saturate(1.5)': 'saturate-150',\n        'saturate(2)': 'saturate-200',\n        'sepia(0)': 'sepia-0',\n        'sepia(1)': 'sepia',\n    }[val];\n};\n\nexport const propertyMap: Map<\n    string,\n    Record<string, string> | ((val: string, isCustom?: boolean) => string)\n> = new Map<string, Record<string, string> | ((val: string, isCustom?: boolean) => string)>([\n    [\n        'align-content',\n        {\n            center: 'content-center',\n            'flex-start': 'content-start',\n            'flex-end': 'content-end',\n            'space-between': 'content-between',\n            'space-around': 'content-around',\n            'space-evenly': 'content-evenly',\n        },\n    ],\n    [\n        'align-items',\n        {\n            'flex-start': 'items-start',\n            'flex-end': 'items-end',\n            center: 'items-center',\n            baseline: 'items-baseline',\n            stretch: 'items-stretch',\n        },\n    ],\n    [\n        'align-self',\n        {\n            auto: 'self-auto',\n            'flex-start': 'self-start',\n            'flex-end': 'self-end',\n            center: 'self-center',\n            stretch: 'self-stretch',\n            baseline: 'self-baseline',\n        },\n    ],\n    [\n        'all',\n        {\n            initial: '[all:initial]',\n            inherit: '[all:inherit]',\n            unset: '[all:unset]',\n        },\n    ],\n    ['animation', (val) => ({ none: 'animate-none' })[val] ?? `animate-[${getCustomVal(val)}]`],\n    ['animation-delay', (val) => `[animation-delay:${getCustomVal(val)}]`],\n    ['animation-direction', (val) => `[animation-direction:${getCustomVal(val)}]`],\n    ['animation-duration', (val) => `[animation-duration:${getCustomVal(val)}]`],\n    ['animation-fill-mode', (val) => `[animation-fill-mode:${getCustomVal(val)}]`],\n    ['animation-iteration-count', (val) => `[animation-iteration-count:${getCustomVal(val)}]`],\n    ['animation-name', (val) => `[animation-name:${getCustomVal(val)}]`],\n    ['animation-play-state', (val) => `[animation-play-state:${getCustomVal(val)}]`],\n    ['animation-timing-function', (val) => `[animation-timing-function:${getCustomVal(val)}]`],\n    [\n        'appearance',\n        (val) => ({ none: 'appearance-none' })[val] ?? `[appearance:${getCustomVal(val)}]`,\n    ],\n    ['aspect-ratio', (val) => `[aspect-ratio:${getCustomVal(val)}]`],\n    [\n        'backdrop-filter',\n        (val) => {\n            const defaultVal = { none: 'backdrop-filter-none' }[val];\n            if (defaultVal) {\n                return defaultVal;\n            }\n\n            const backdropFilterValConfig: Record<string, (v: string) => string> = {\n                blur: (v: string) =>\n                    `backdrop-blur-${customTheme['backdrop-blur']?.[v] ?? `[${v}]`}`,\n                brightness: (v: string) =>\n                    `backdrop-brightness-${customTheme['backdrop-brightness']?.[v] ?? `[${v}]`}`,\n                contrast: (v: string) =>\n                    `backdrop-contrast-${customTheme['backdrop-contrast']?.[v] ?? `[${v}]`}`,\n                grayscale: (v: string) =>\n                    `backdrop-grayscale-${customTheme['backdrop-grayscale']?.[v] ?? `[${v}]`}`,\n                'hue-rotate': (v: string) => {\n                    const t = hasNegative(v);\n                    return `${t[0]}backdrop-hue-rotate-${customTheme['backdrop-grayscale']?.[t[1]] ?? `[${t[1]}]`}`;\n                },\n                invert: (v: string) =>\n                    `backdrop-invert-${customTheme['backdrop-invert']?.[v] ?? `[${v}]`}`,\n                opacity: (v: string) =>\n                    `backdrop-opacity-${customTheme['backdrop-opacity']?.[v] ?? `[${v}]`}`,\n                saturate: (v: string) =>\n                    `backdrop-saturate-${customTheme['backdrop-saturate']?.[v] ?? `[${v}]`}`,\n                sepia: (v: string) =>\n                    `backdrop-sepia-${customTheme['backdrop-sepia']?.[v] ?? `[${v}]`}`,\n            };\n            const vals = getCustomVal(val)\n                .replace(/\\(.+?\\)/g, (v) => v.replace(/_/g, ''))\n                .split(')_')\n                .map((v) => `${v})`);\n            vals[vals.length - 1] = vals[vals.length - 1].slice(0, -1);\n\n            let canUse = true;\n            const res = vals.map((v) => {\n                let canUsePipeV = false;\n                let pipeV = '';\n                if (useAllDefaultValues) {\n                    pipeV =\n                        (getFilterDefaultVal(v) ||\n                            {\n                                'opacity(0)': 'backdrop-opacity-0',\n                                'opacity(0.05)': 'backdrop-opacity-5',\n                                'opacity(0.1)': 'backdrop-opacity-10',\n                                'opacity(0.2)': 'backdrop-opacity-20',\n                                'opacity(0.25)': 'backdrop-opacity-25',\n                                'opacity(0.3)': 'backdrop-opacity-30',\n                                'opacity(0.4)': 'backdrop-opacity-40',\n                                'opacity(0.5)': 'backdrop-opacity-50',\n                                'opacity(0.6)': 'backdrop-opacity-60',\n                                'opacity(0.7)': 'backdrop-opacity-70',\n                                'opacity(0.75)': 'backdrop-opacity-75',\n                                'opacity(0.8)': 'backdrop-opacity-80',\n                                'opacity(0.9)': 'backdrop-opacity-90',\n                                'opacity(0.95)': 'backdrop-opacity-95',\n                                'opacity(1)': 'backdrop-opacity-100',\n                            }[v]) ??\n                        '';\n                    if (pipeV.length > 0) {\n                        pipeV = pipeV.startsWith('backdrop-opacity') ? pipeV : `backdrop-${pipeV}`;\n                        canUsePipeV = true;\n                    }\n                }\n                pipeV =\n                    pipeV.length > 0\n                        ? pipeV\n                        : v.replace(/^([a-zA-Z0-9_-]+)\\((.+?)\\)$/, (r, k: string, v) => {\n                              canUsePipeV = true;\n                              return backdropFilterValConfig[k]?.(v) ?? (canUse = false);\n                          });\n                return canUsePipeV ? pipeV : '';\n            });\n            return canUse\n                ? `backdrop-filter ${[...new Set(res)].join(' ')}`\n                : `[backdrop-filter:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'backface-visibility',\n        {\n            visible: '[backface-visibility:visible]',\n            hidden: '[backface-visibility:hidden]',\n        },\n    ],\n    [\n        'background',\n        (val) => {\n            const legalConfig: Record<string, string> = {\n                ...propertyMap.get('background-attachment'),\n                ...propertyMap.get('background-repeat'),\n                transparent: 'bg-transparent',\n                currentColor: 'bg-current',\n                currentcolor: 'bg-current',\n                none: 'bg-none',\n                bottom: 'bg-bottom',\n                center: 'bg-center',\n                left: 'bg-left',\n                'left bottom': 'bg-left-bottom',\n                'left top': 'bg-left-top',\n                right: 'bg-right',\n                'right bottom': 'bg-right-bottom',\n                'right top': 'bg-right-top',\n                top: 'bg-top',\n                auto: 'bg-auto',\n                cover: 'bg-cover',\n                contain: 'bg-contain',\n            };\n            return legalConfig[val] ?? `bg-[${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'background-attachment',\n        {\n            fixed: 'bg-fixed',\n            local: 'bg-local',\n            scroll: 'bg-scroll',\n        },\n    ],\n    [\n        'background-blend-mode',\n        {\n            normal: 'bg-blend-normal',\n            multiply: 'bg-blend-multiply',\n            screen: 'bg-blend-screen',\n            overlay: 'bg-blend-overlay',\n            darken: 'bg-blend-darken',\n            lighten: 'bg-blend-lighten',\n            'color-dodge': 'bg-blend-color-dodge',\n            'color-burn': 'bg-blend-color-burn',\n            'hard-light': 'bg-blend-hard-light',\n            'soft-light': 'bg-blend-soft-light',\n            difference: 'bg-blend-difference',\n            exclusion: 'bg-blend-exclusion',\n            hue: 'bg-blend-hue',\n            saturation: 'bg-blend-saturation',\n            color: 'bg-blend-color',\n            luminosity: 'bg-blend-luminosity',\n        },\n    ],\n    [\n        'background-clip',\n        {\n            'border-box': 'bg-clip-border',\n            'padding-box': 'bg-clip-padding',\n            'content-box': 'bg-clip-content',\n            text: 'bg-clip-text',\n        },\n    ],\n    [\n        'background-color',\n        (val, isCustom = false) =>\n            ({\n                transparent: 'bg-transparent',\n                currentColor: 'bg-current',\n                currentcolor: 'bg-current',\n            })[val] ??\n            (isCustom ? `bg-${val}` : isColor(val, true) ? `bg-[${getCustomVal(val)}]` : ''),\n    ],\n    ['background-image', (val) => ({ none: 'bg-none' })[val] ?? `bg-[${getCustomVal(val)}]`],\n    [\n        'background-origin',\n        {\n            'border-box': 'bg-origin-border',\n            'padding-box': 'bg-origin-padding',\n            'content-box': 'bg-origin-content',\n        },\n    ],\n    [\n        'background-position',\n        (val) =>\n            ({\n                bottom: 'bg-bottom',\n                center: 'bg-center',\n                left: 'bg-left',\n                'left bottom': 'bg-left-bottom',\n                'left top': 'bg-left-top',\n                right: 'bg-right',\n                'right bottom': 'bg-right-bottom',\n                'right top': 'bg-right-top',\n                top: 'bg-top',\n            })[val] ?? `bg-[${getCustomVal(val)}]`,\n    ],\n    [\n        'background-repeat',\n        {\n            repeat: 'bg-repeat',\n            'no-repeat': 'bg-no-repeat',\n            'repeat-x': 'bg-repeat-x',\n            'repeat-y': 'bg-repeat-y',\n            round: 'bg-repeat-round',\n            space: 'bg-repeat-space',\n        },\n    ],\n    [\n        'background-size',\n        (val) =>\n            ({\n                auto: 'bg-auto',\n                cover: 'bg-cover',\n                contain: 'bg-contain',\n            })[val] ?? `[background-size:${getCustomVal(val)}]`,\n    ],\n    [\n        'border',\n        (val) => {\n            val = val.replace(/\\(.+?\\)/, (v) => v.replace(/\\s/g, ''));\n            const vals: string = val\n                .split(' ')\n                .filter((v) => v !== '')\n                .map((v) =>\n                    isUnit(v) || isColor(v)\n                        ? ({\n                              transparent: 'border-transparent',\n                              currentColor: 'border-current',\n                              currentcolor: 'border-current',\n                          }[val] ??\n                          (propertyMap.get('border-style') as Record<string, string>)[v] ??\n                          `border-[${v}]`)\n                        : ((propertyMap.get('border-style') as Record<string, string>)[v] ?? ''),\n                )\n                .filter((v) => v !== '')\n                .join(' ');\n            return vals;\n        },\n    ],\n    [\n        'border-bottom',\n        (val) => {\n            return `[border-bottom:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'border-bottom-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[border-bottom-color:${val}]`\n                : isColor(val, true)\n                  ? `[border-bottom-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    [\n        'border-bottom-left-radius',\n        (val) =>\n            ({ '0': 'rounded-bl-none', '0px': 'rounded-bl-none' })[val] ??\n            (isUnit(val)\n                ? `rounded-bl${((useAllDefaultValues && getBorderRadiusDefaultVal(val)) || `-[${getCustomVal(val)}]`).replace(/null$/, '')}`\n                : ''),\n    ],\n    [\n        'border-bottom-right-radius',\n        (val) =>\n            ({ '0': 'rounded-br-none', '0px': 'rounded-br-none' })[val] ??\n            (isUnit(val)\n                ? `rounded-br${((useAllDefaultValues && getBorderRadiusDefaultVal(val)) || `-[${getCustomVal(val)}]`).replace(/null$/, '')}`\n                : ''),\n    ],\n    [\n        'border-bottom-style',\n        (val) =>\n            (propertyMap.get('border-style') as Record<string, string>)[val]\n                ? `[border-bottom-style:${val}]`\n                : '',\n    ],\n    ['border-bottom-width', (val) => (isUnit(val) ? `border-b-[${getCustomVal(val)}]` : '')],\n    [\n        'border-collapse',\n        {\n            collapse: 'border-collapse',\n            separate: 'border-separate',\n        },\n    ],\n    [\n        'border-color',\n        (val, isCustom = false) =>\n            ({\n                transparent: 'border-transparent',\n                currentColor: 'border-current',\n                currentcolor: 'border-current',\n            })[val] ??\n            (isCustom\n                ? `border-${val}`\n                : isColor(val, true)\n                  ? `border-[${getCustomVal(val)}]`\n                  : ''),\n    ],\n    ['border-image', (val) => `[border-image:${getCustomVal(val)}]`],\n    ['border-image-outset', (val) => `[border-image-outset:${getCustomVal(val)}]`],\n    ['border-image-repeat', (val) => `[border-image-repeat:${getCustomVal(val)}]`],\n    ['border-image-slice', (val) => `[border-image-slice:${getCustomVal(val)}]`],\n    ['border-image-source', (val) => `[border-image-source:${getCustomVal(val)}]`],\n    [\n        'border-image-width',\n        (val) => (isUnit(val) ? `[border-image-width:${getCustomVal(val)}]` : ''),\n    ],\n    [\n        'border-left',\n        (val) => {\n            return `[border-left:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'border-left-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[border-left-color:${val}]`\n                : isColor(val, true)\n                  ? `[border-left-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    [\n        'border-left-style',\n        (val) =>\n            (propertyMap.get('border-style') as Record<string, string>)[val]\n                ? `[border-left-style:${val}]`\n                : '',\n    ],\n    ['border-left-width', (val) => (isUnit(val) ? `border-l-[${getCustomVal(val)}]` : '')],\n    [\n        'border-radius',\n        (val) => {\n            const r = { '0': 'rounded-none', '0px': 'rounded-none' }[val];\n            if (r) {\n                return r;\n            }\n            if (val.includes('/')) {\n                return `rounded-[${getCustomVal(val)}]`;\n            }\n            let vals = val.split(' ').filter((v) => v !== '');\n            if (vals.filter((v) => !isUnit(v)).length > 0) {\n                return '';\n            }\n            vals = vals.map((v) =>\n                ((useAllDefaultValues && getBorderRadiusDefaultVal(v)) || `-[${v}]`).replace(\n                    /null$/,\n                    '',\n                ),\n            );\n            if (vals.length === 1) {\n                return `rounded${vals[0]}`;\n            } else if (vals.length === 2) {\n                return `rounded-tl${vals[0]} rounded-br${vals[0]} rounded-tr${vals[1]} rounded-bl${vals[1]}`;\n            } else if (vals.length === 3) {\n                return `rounded-tl${vals[0]} rounded-br${vals[2]} rounded-tr${vals[1]} rounded-bl${vals[1]}`;\n            } else if (vals.length === 4) {\n                return `rounded-tl${vals[0]} rounded-br${vals[2]} rounded-tr${vals[1]} rounded-bl${vals[3]}`;\n            }\n            return '';\n        },\n    ],\n    [\n        'border-right',\n        (val) => {\n            return `[border-right:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'border-right-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[border-right-color:${val}]`\n                : isColor(val, true)\n                  ? `[border-right-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    [\n        'border-right-style',\n        (val) =>\n            (propertyMap.get('border-style') as Record<string, string>)[val]\n                ? `[border-right-style:${val}]`\n                : '',\n    ],\n    ['border-right-width', (val) => (isUnit(val) ? `border-r-[${getCustomVal(val)}]` : '')],\n    ['border-spacing', (val) => (isUnit(val) ? `[border-spacing:${getCustomVal(val)}]` : '')],\n    [\n        'border-style',\n        {\n            solid: 'border-solid',\n            dashed: 'border-dashed',\n            dotted: 'border-dotted',\n            double: 'border-double',\n            none: 'border-none',\n        },\n    ],\n    [\n        'border-top',\n        (val) => {\n            return `[border-top:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'border-top-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[border-top-color:${val}]`\n                : isColor(val, true)\n                  ? `[border-top-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    [\n        'border-top-left-radius',\n        (val) =>\n            ({ '0': 'rounded-tl-none', '0px': 'rounded-tl-none' })[val] ??\n            (isUnit(val)\n                ? `rounded-tl${((useAllDefaultValues && getBorderRadiusDefaultVal(val)) || `-[${getCustomVal(val)}]`).replace(/null$/, '')}`\n                : ''),\n    ],\n    [\n        'border-top-right-radius',\n        (val) =>\n            ({ '0': 'rounded-tr-none', '0px': 'rounded-tr-none' })[val] ??\n            (isUnit(val)\n                ? `rounded-tr${((useAllDefaultValues && getBorderRadiusDefaultVal(val)) || `-[${getCustomVal(val)}]`).replace(/null$/, '')}`\n                : ''),\n    ],\n    [\n        'border-top-style',\n        (val) =>\n            (propertyMap.get('border-style') as Record<string, string>)[val]\n                ? `[border-top-style:${val}]`\n                : '',\n    ],\n    ['border-top-width', (val) => (isUnit(val) ? `border-t-[${getCustomVal(val)}]` : '')],\n    ['border-width', (val) => (isUnit(val) ? `border-[${getCustomVal(val)}]` : '')],\n    [\n        'bottom',\n        (val) => {\n            const t = hasNegative(val);\n            return isUnit(val)\n                ? `${t[0]}bottom-${getUnitMetacharactersVal(t[1], [CustomSelect.vw, CustomSelect.vh]) || `[${t[1]}]`}`\n                : '';\n        },\n    ],\n    [\n        'box-align',\n        {\n            initial: '[box-align:initial]',\n            start: '[box-align:inherit]',\n            end: '[box-align:unset]',\n            center: '[box-align:unset]',\n            baseline: '[box-align:unset]',\n            stretch: '[box-align:unset]',\n        },\n    ],\n    [\n        'box-decoration-break',\n        {\n            slice: 'decoration-slice',\n            clone: 'decoration-clone',\n        },\n    ],\n    [\n        'box-direction',\n        {\n            initial: '[box-direction:initial]',\n            normal: '[box-direction:normal]',\n            reverse: '[box-direction:reverse]',\n            inherit: '[box-direction:inherit]',\n        },\n    ],\n    ['box-flex', (val) => `[box-flex:${getCustomVal(val)}]`],\n    ['box-flex-group', (val) => `[box-flex-group:${getCustomVal(val)}]`],\n    [\n        'box-lines',\n        {\n            single: '[box-lines:single]',\n            multiple: '[box-lines:multiple]',\n            initial: '[box-lines:initial]',\n        },\n    ],\n    ['box-ordinal-group', (val) => `[box-ordinal-group:${getCustomVal(val)}]`],\n    [\n        'box-orient',\n        {\n            horizontal: '[box-orient:horizontal]',\n            vertical: '[box-orient:vertical]',\n            'inline-axis': '[box-orient:inline-axis]',\n            'block-axis': '[box-orient:block-axis]',\n            inherit: '[box-orient:inherit]',\n            initial: '[box-orient:initial]',\n        },\n    ],\n    [\n        'box-pack',\n        {\n            start: '[box-pack:start]',\n            end: '[box-pack:end]',\n            center: '[box-pack:center]',\n            justify: '[box-pack:justify]',\n            initial: '[box-pack:initial]',\n        },\n    ],\n    ['box-shadow', (val) => `[box-shadow:${getCustomVal(val)}]`],\n    [\n        'box-sizing',\n        {\n            'border-box': 'box-border',\n            'content-box': 'box-content',\n        },\n    ],\n    [\n        'caption-side',\n        {\n            top: '[caption-side:top]',\n            bottom: '[caption-side:bottom]',\n            inherit: '[caption-side:inherit]',\n            initial: '[caption-side:initial]',\n        },\n    ],\n    [\n        'clear',\n        {\n            left: 'clear-left',\n            right: 'clear-right',\n            both: 'clear-both',\n            none: 'clear-none',\n        },\n    ],\n    ['clip', (val) => `[clip:${getCustomVal(val)}]`],\n    ['clip-path', (val) => `[clip-path:${getCustomVal(val)}]`],\n    [\n        'color',\n        (val, isCustom = false) =>\n            ({\n                transparent: 'text-transparent',\n                currentColor: 'text-current',\n                currentcolor: 'text-current',\n            })[val] ??\n            (isCustom ? `text-${val}` : isColor(val, true) ? `text-[${getCustomVal(val)}]` : ''),\n    ],\n    ['color-scheme', (val) => `[color-scheme:${getCustomVal(val)}]`],\n    ['column-count', (val) => `[column-count:${getCustomVal(val)}]`],\n    [\n        'column-fill',\n        {\n            balance: '[column-fill:balance]',\n            auto: '[column-fill:auto]',\n            initial: '[column-fill:initial]',\n        },\n    ],\n    ['column-gap', (val) => ({ '0': 'gap-x-0' })[val] ?? (isUnit(val) ? `gap-x-[${val}]` : '')],\n    ['column-rule', (val) => `[column-rule:${getCustomVal(val)}]`],\n    [\n        'column-rule-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[column-rule-color:${val}]`\n                : isColor(val, true)\n                  ? `[column-rule-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    [\n        'column-rule-style',\n        {\n            none: '[column-rule-style:none]',\n            hidden: '[column-rule-style:hidden]',\n            dotted: '[column-rule-style:dotted]',\n            dashed: '[column-rule-style:dashed]',\n            solid: '[column-rule-style:solid]',\n            double: '[column-rule-style:double]',\n            groove: '[column-rule-style:groove]',\n            ridge: '[column-rule-style:ridge]',\n            inset: '[column-rule-style:inset]',\n            outset: '[column-rule-style:outset]',\n            initial: '[column-rule-style:initial]',\n        },\n    ],\n    ['column-rule-width', (val) => (isUnit(val) ? `[column-rule-width:${val}]` : '')],\n    ['column-span', (val) => `[column-span:${getCustomVal(val)}]`],\n    ['column-width', (val) => (isUnit(val) ? `[column-width:${val}]` : '')],\n    ['columns', (val) => `[columns:${getCustomVal(val)}]`],\n    ['contain-intrinsic-size', (val) => `[contain-intrinsic-size:${getCustomVal(val)}]`],\n    ['content', (val) => `content-[${getCustomVal(val)}]`],\n    ['content-visibility', (val) => `[content-visibility:${getCustomVal(val)}]`],\n    ['counter-increment', (val) => `[content-increment:${getCustomVal(val)}]`],\n    ['counter-reset', (val) => `[counter-reset:${getCustomVal(val)}]`],\n    ['counter-set', (val) => `[counter-set:${getCustomVal(val)}]`],\n    [\n        'cursor',\n        {\n            auto: 'cursor-auto',\n            default: 'cursor-default',\n            pointer: 'cursor-pointer',\n            wait: 'cursor-wait',\n            text: 'cursor-text',\n            move: 'cursor-move',\n            help: 'cursor-help',\n            'not-allowed': 'cursor-not-allowed',\n        },\n    ],\n    [\n        'direction',\n        {\n            ltr: '[direction:ltr]',\n            rtl: '[direction:rtl]',\n            inherit: '[direction:inherit]',\n            initial: '[direction:initial]',\n        },\n    ],\n    [\n        'display',\n        {\n            block: 'block',\n            'inline-block': 'inline-block',\n            inline: 'inline',\n            flex: 'flex',\n            'inline-flex': 'inline-flex',\n            table: 'table',\n            'inline-table': 'inline-table',\n            'table-caption': 'table-caption',\n            'table-cell': 'table-cell',\n            'table-column': 'table-column',\n            'table-column-group': 'table-column-group',\n            'table-footer-group': 'table-footer-group',\n            'table-header-group': 'table-header-group',\n            'table-row-group': 'table-row-group',\n            'table-row': 'table-row',\n            'flow-root': 'flow-root',\n            grid: 'grid',\n            'inline-grid': 'inline-grid',\n            contents: 'contents',\n            'list-item': 'list-item',\n            none: 'hidden',\n        },\n    ],\n    [\n        'empty-cells',\n        {\n            hide: '[empty-cells:hide]',\n            show: '[empty-cells:show]',\n            inherit: '[empty-cells:inherit]',\n            initial: '[empty-cells:initial]',\n        },\n    ],\n    [\n        'fill',\n        (val, isCustom = false) =>\n            ({ currentColor: 'fill-current', currentcolor: 'fill-current' })[val] ??\n            (isCustom ? `fill-${val}` : isColor(val, true) ? `fill-[${getCustomVal(val)}]` : ''),\n    ],\n    [\n        'filter',\n        (val) => {\n            const defaultVal = { none: 'filter-none' }[val];\n            if (defaultVal) {\n                return defaultVal;\n            }\n            const filterValConfig: Record<string, (v: string) => string> = {\n                blur: (v: string) => `blur-${customTheme['blur']?.[v] ?? `[${v}]`}`,\n                brightness: (v: string) =>\n                    `brightness-${customTheme['brightness']?.[v] ?? `[${v}]`}`,\n                contrast: (v: string) => `contrast-${customTheme['contrast']?.[v] ?? `[${v}]`}`,\n                grayscale: (v: string) => `grayscale-${customTheme['grayscale']?.[v] ?? `[${v}]`}`,\n                'hue-rotate': (v: string) => {\n                    const t = hasNegative(v);\n                    return `${t[0]}hue-rotate-${customTheme['grayscale']?.[t[1]] ?? `[${t[1]}]`}`;\n                },\n                invert: (v: string) => `invert-${customTheme['invert']?.[v] ?? `[${v}]`}`,\n                saturate: (v: string) => `saturate-${customTheme['saturate']?.[v] ?? `[${v}]`}`,\n                sepia: (v: string) => `sepia-${customTheme['sepia']?.[v] ?? `[${v}]`}`,\n            };\n            const vals = getCustomVal(val)\n                .replace(/\\(.+?\\)/g, (v) => v.replace(/_/g, ''))\n                .split(')_')\n                .map((v) => `${v})`);\n            vals[vals.length - 1] = vals[vals.length - 1].slice(0, -1);\n\n            let canUse = true;\n            const res = vals.map((v) => {\n                let canUsePipeV = false;\n                let pipeV = '';\n                if (useAllDefaultValues) {\n                    pipeV = getFilterDefaultVal(v) ?? '';\n                    if (pipeV.length > 0) {\n                        canUsePipeV = true;\n                    }\n                }\n                pipeV =\n                    pipeV.length > 0\n                        ? pipeV\n                        : v.replace(/^([a-zA-Z0-9_-]+)\\((.+?)\\)$/, (r, k: string, v) => {\n                              canUsePipeV = true;\n                              return filterValConfig[k]?.(v) ?? (canUse = false);\n                          });\n                return canUsePipeV ? pipeV : '';\n            });\n            return canUse\n                ? `filter ${[...new Set(res)].join(' ')}`\n                : `[filter:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'flex',\n        (val) =>\n            ({\n                '1 1 0%': 'flex-1',\n                '1 1 auto': 'flex-auto',\n                '0 1 auto': 'flex-initial',\n                none: 'flex-none',\n            })[val] ?? `flex-[${getCustomVal(val)}]`,\n    ],\n    ['flex-basis', (val) => (isUnit(val) ? `[flex-basis:${val}]` : '')],\n    [\n        'flex-direction',\n        {\n            row: 'flex-row',\n            'row-reverse': 'flex-row-reverse',\n            column: 'flex-col',\n            'column-reverse': 'flex-col-reverse',\n        },\n    ],\n    ['flex-flow', (val) => `[flex-flow:${getCustomVal(val)}]`],\n    [\n        'flex-grow',\n        (val) =>\n            isUnit(val)\n                ? ({ '0': 'flex-grow-0', '1': 'flex-grow' }[val] ?? `flex-grow-[${val}]`)\n                : '',\n    ],\n    [\n        'flex-shrink',\n        (val) =>\n            isUnit(val)\n                ? ({ '0': 'flex-shrink-0', '1': 'flex-shrink' }[val] ?? `flex-shrink-[${val}]`)\n                : '',\n    ],\n    [\n        'flex-wrap',\n        {\n            wrap: 'flex-wrap',\n            'wrap-reverse': 'flex-wrap-reverse',\n            nowrap: 'flex-nowrap',\n        },\n    ],\n    [\n        'float',\n        {\n            right: 'float-right',\n            left: 'float-left',\n            none: 'float-none',\n        },\n    ],\n    ['font', (val) => `[font:${getCustomVal(val)}]`],\n    ['font-family', (val) => `font-[${getCustomVal(val)}]`],\n    ['font-size', (val) => (isUnit(val) ? `text-[${val}]` : '')],\n    ['font-size-adjust', (val) => (isUnit(val) ? `[font-size-adjust:${val}]` : '')],\n    [\n        '-webkit-font-smoothing',\n        {\n            antialiased: 'antialiased',\n            auto: 'subpixel-antialiased',\n        },\n    ],\n    [\n        '-moz-osx-font-smoothing',\n        {\n            grayscale: 'antialiased',\n            auto: 'subpixel-antialiased',\n        },\n    ],\n    [\n        'font-stretch',\n        {\n            wider: '[font-stretch:wider]',\n            narrower: '[font-stretch:narrower]',\n            'ultra-condensed': '[font-stretch:ultra-condensed]',\n            'extra-condensed': '[font-stretch:extra-condensed]',\n            condensed: '[font-stretch:condensed]',\n            'semi-condensed': '[font-stretch:semi-condensed]',\n            normal: '[font-stretch:normal]',\n            'semi-expanded': '[font-stretch:semi-expanded]',\n            expanded: '[font-stretch:expanded]',\n            'extra-expanded': '[font-stretch:extra-expanded]',\n            'ultra-expanded': '[font-stretch:ultra-expanded]',\n            inherit: '[font-stretch:inherit]',\n            initial: '[font-stretch:initial]',\n        },\n    ],\n    [\n        'font-style',\n        {\n            italic: 'italic',\n            normal: 'not-italic',\n        },\n    ],\n    [\n        'font-variant',\n        {\n            normal: '[font-variant:normal]',\n            'small-caps': '[font-variant:small-caps]',\n            inherit: '[font-variant:inherit]',\n            initial: '[font-variant:initial]',\n        },\n    ],\n    [\n        'font-variant-numeric',\n        {\n            normal: 'normal-nums',\n            ordinal: 'ordinal',\n            'slashed-zero': 'slashed-zero',\n            'lining-nums': 'lining-nums',\n            'oldstyle-nums': 'oldstyle-nums',\n            'proportional-nums': 'proportional-nums',\n            'tabular-nums': 'tabular-nums',\n            'diagonal-fractions': 'diagonal-fractions',\n            'stacked-fractions': 'stacked-fractions',\n        },\n    ],\n    ['font-variation-settings', (val) => `[font-variation-settings:${getCustomVal(val)}]`],\n    ['font-weight', (val) => (isUnit(val) ? `font-[${val}]` : '')],\n    ['gap', (val) => ({ '0': 'gap-0' })[val] ?? (isUnit(val) ? `gap-[${val}]` : '')],\n    ['grid', (val) => `[grid:${getCustomVal(val)}]`],\n    ['grid-area', (val) => `[grid-area:${getCustomVal(val)}]`],\n    [\n        'grid-auto-columns',\n        (val) =>\n            ({\n                auto: 'auto-cols-auto',\n                'min-content': 'auto-cols-min',\n                'max-content': 'auto-cols-max',\n                'minmax(0, 1fr)': 'auto-cols-fr',\n            })[val] ?? `auto-cols-[${getCustomVal(val)}]`,\n    ],\n    [\n        'grid-auto-flow',\n        (val) =>\n            ({\n                row: 'grid-flow-row',\n                column: 'grid-flow-col',\n                row_dense: 'grid-flow-row-dense',\n                column_dense: 'grid-flow-col-dense',\n            })[getCustomVal(val)] ?? '',\n    ],\n    [\n        'grid-auto-rows',\n        (val) =>\n            ({\n                auto: 'auto-rows-auto',\n                'min-content': 'auto-rows-min',\n                'max-content': 'auto-rows-max',\n                'minmax(0, 1fr)': 'auto-rows-fr',\n            })[val] ?? `auto-rows-[${getCustomVal(val)}]`,\n    ],\n    [\n        'grid-column',\n        (val) =>\n            ({\n                auto: 'col-auto',\n                'span 1 / span 1': 'col-span-1',\n                'span 2 / span 2': 'col-span-2',\n                'span 3 / span 3': 'col-span-3',\n                'span 4 / span 4': 'col-span-4',\n                'span 5 / span 5': 'col-span-5',\n                'span 6 / span 6': 'col-span-6',\n                'span 7 / span 7': 'col-span-7',\n                'span 8 / span 8': 'col-span-8',\n                'span 9 / span 9': 'col-span-9',\n                'span 10 / span 10': 'col-span-10',\n                'span 11 / span 11': 'col-span-11',\n                'span 12 / span 12': 'col-span-12',\n                '1 / -1': 'col-span-full',\n            })[val] ?? `col-[${getCustomVal(val)}]`,\n    ],\n    [\n        'grid-column-end',\n        (val) =>\n            ({\n                '1': 'col-end-1',\n                '2': 'col-end-2',\n                '3': 'col-end-3',\n                '4': 'col-end-4',\n                '5': 'col-end-5',\n                '6': 'col-end-6',\n                '7': 'col-end-7',\n                '8': 'col-end-8',\n                '9': 'col-end-9',\n                '10': 'col-end-10',\n                '11': 'col-end-11',\n                '12': 'col-end-12',\n                '13': 'col-end-13',\n                auto: 'col-end-auto',\n            })[val] ?? `col-end-[${getCustomVal(val)}]`,\n    ],\n    [\n        'grid-column-gap',\n        (val) => ({ '0': 'gap-x-0' })[val] ?? (isUnit(val) ? `gap-x-[${val}]` : ''),\n    ],\n    [\n        'grid-column-start',\n        (val) =>\n            ({\n                '1': 'col-start-1',\n                '2': 'col-start-2',\n                '3': 'col-start-3',\n                '4': 'col-start-4',\n                '5': 'col-start-5',\n                '6': 'col-start-6',\n                '7': 'col-start-7',\n                '8': 'col-start-8',\n                '9': 'col-start-9',\n                '10': 'col-start-10',\n                '11': 'col-start-11',\n                '12': 'col-start-12',\n                '13': 'col-start-13',\n                auto: 'col-start-auto',\n            })[val] ?? `col-start-[${getCustomVal(val)}]`,\n    ],\n    ['grid-gap', (val) => ({ '0': 'gap-0' })[val] ?? (isUnit(val) ? `gap-[${val}]` : '')],\n    [\n        'grid-row',\n        (val) =>\n            ({\n                auto: 'row-auto',\n                'span 1 / span 1': 'row-span-1',\n                'span 2 / span 2': 'row-span-2',\n                'span 3 / span 3': 'row-span-3',\n                'span 4 / span 4': 'row-span-4',\n                'span 5 / span 5': 'row-span-5',\n                'span 6 / span 6': 'row-span-6',\n                '1 / -1': 'row-span-full',\n            })[val] ?? `row-[${getCustomVal(val)}]`,\n    ],\n    [\n        'grid-row-end',\n        (val) =>\n            ({\n                '1': 'row-end-1',\n                '2': 'row-end-2',\n                '3': 'row-end-3',\n                '4': 'row-end-4',\n                '5': 'row-end-5',\n                '6': 'row-end-6',\n                '7': 'row-end-7',\n                auto: 'row-end-auto',\n            })[val] ?? `row-end-[${getCustomVal(val)}]`,\n    ],\n    ['grid-row-gap', (val) => ({ '0': 'gap-y-0' })[val] ?? (isUnit(val) ? `gap-y-[${val}]` : '')],\n    [\n        'grid-row-start',\n        (val) =>\n            ({\n                '1': 'row-start-1',\n                '2': 'row-start-2',\n                '3': 'row-start-3',\n                '4': 'row-start-4',\n                '5': 'row-start-5',\n                '6': 'row-start-6',\n                '7': 'row-start-7',\n                auto: 'row-start-auto',\n            })[val] ?? `row-start-[${getCustomVal(val)}]`,\n    ],\n    ['grid-rows', (val) => `[grid-rows:${getCustomVal(val)}]`],\n    ['grid-template', (val) => `[grid-template:${getCustomVal(val)}]`],\n    ['grid-template-areas', (val) => `[grid-template-areas:${getCustomVal(val)}]`],\n    [\n        'grid-template-columns',\n        (val) =>\n            ({\n                'repeat(1,minmax(0,1fr))': 'grid-cols-1',\n                'repeat(2,minmax(0,1fr))': 'grid-cols-2',\n                'repeat(3,minmax(0,1fr))': 'grid-cols-3',\n                'repeat(4,minmax(0,1fr))': 'grid-cols-4',\n                'repeat(5,minmax(0,1fr))': 'grid-cols-5',\n                'repeat(6,minmax(0,1fr))': 'grid-cols-6',\n                'repeat(7,minmax(0,1fr))': 'grid-cols-7',\n                'repeat(8,minmax(0,1fr))': 'grid-cols-8',\n                'repeat(9,minmax(0,1fr))': 'grid-cols-9',\n                'repeat(10,minmax(0,1fr))': 'grid-cols-10',\n                'repeat(11,minmax(0,1fr))': 'grid-cols-11',\n                'repeat(12,minmax(0,1fr))': 'grid-cols-12',\n                none: 'grid-cols-none',\n            })[getCustomVal(val).replace(/_/g, '')] ?? `grid-cols-[${getCustomVal(val)}]`,\n    ],\n    [\n        'grid-template-rows',\n        (val) =>\n            ({\n                'repeat(1,minmax(0,1fr))': 'grid-rows-1',\n                'repeat(2,minmax(0,1fr))': 'grid-rows-2',\n                'repeat(3,minmax(0,1fr))': 'grid-rows-3',\n                'repeat(4,minmax(0,1fr))': 'grid-rows-4',\n                'repeat(5,minmax(0,1fr))': 'grid-rows-5',\n                'repeat(6,minmax(0,1fr))': 'grid-rows-6',\n                none: 'grid-rows-none',\n            })[getCustomVal(val).replace(/_/g, '')] ?? `grid-rows-[${getCustomVal(val)}]`,\n    ],\n    [\n        'hanging-punctuation',\n        {\n            none: '[hanging-punctuation:none]',\n            first: '[hanging-punctuation:first]',\n            last: '[hanging-punctuation:last]',\n            'allow-end': '[hanging-punctuation:allow-end]',\n            'force-end': '[hanging-punctuation:force-end]',\n            initial: '[hanging-punctuation:initial]',\n        },\n    ],\n    [\n        'height',\n        (val) =>\n            isUnit(val)\n                ? `h-${(useAllDefaultValues && getRemDefaultVal(val)) || getUnitMetacharactersVal(val, [CustomSelect.vw]) || `[${val}]`}`\n                : '',\n    ],\n    ['icon', (val) => `[icon:${getCustomVal(val)}]`],\n    ['image-orientation', (val) => `[image-orientation:${getCustomVal(val)}]`],\n    [\n        'justify-content',\n        {\n            'flex-start': 'justify-start',\n            'flex-end': 'justify-end',\n            center: 'justify-center',\n            'space-between': 'justify-between',\n            'space-around': 'justify-around',\n            'space-evenly': 'justify-evenly',\n        },\n    ],\n    [\n        'justify-items',\n        {\n            start: 'justify-items-start',\n            end: 'justify-items-end',\n            center: 'justify-items-center',\n            stretch: 'justify-items-stretch',\n        },\n    ],\n    [\n        'justify-self',\n        {\n            auto: 'justify-self-auto',\n            start: 'justify-self-start',\n            end: 'justify-self-end',\n            center: 'justify-self-center',\n            stretch: 'justify-self-stretch',\n        },\n    ],\n    [\n        'left',\n        (val) => {\n            const t = hasNegative(val);\n            return isUnit(val)\n                ? `${t[0]}left-${getUnitMetacharactersVal(t[1], [CustomSelect.vw, CustomSelect.vh]) || `[${t[1]}]`}`\n                : '';\n        },\n    ],\n    [\n        'letter-spacing',\n        (val) =>\n            ({\n                '-0.05em': 'tracking-tighter',\n                '-0.025em': 'tracking-tight',\n                '0em': 'tracking-normal',\n                '0.025em': 'tracking-wide',\n                '0.05em': 'tracking-wider',\n                '0.1em': 'tracking-widest',\n            })[val] ?? (isUnit(val) ? `tracking-[${val}]` : ''),\n    ],\n    [\n        'line-height',\n        (val) =>\n            ({\n                '1': 'leading-none',\n                '2': 'leading-loose',\n                '1.25': 'leading-tight',\n                '1.375': 'leading-snug',\n                '1.5': 'leading-normal',\n                '1.625': 'leading-relaxed',\n            })[val] ?? (isUnit(val) ? `leading-[${val}]` : ''),\n    ],\n    ['list-style', (val) => `[list-style:${getCustomVal(val)}]`],\n    ['list-style-image', (val) => `[list-style-image:${getCustomVal(val)}]`],\n    [\n        'list-style-position',\n        (val) =>\n            ({\n                inside: 'list-inside',\n                outside: 'list-outside',\n            })[val] ?? `[list-style-position:${getCustomVal(val)}]`,\n    ],\n    [\n        'list-style-type',\n        (val) =>\n            ({\n                none: 'list-none',\n                disc: 'list-disc',\n                decimal: 'list-decimal',\n            })[val] ?? `list-[${getCustomVal(val)}]`,\n    ],\n    ['logical-height', (val) => (isUnit(val) ? `[logical-height:${val}]` : '')],\n    ['logical-width', (val) => (isUnit(val) ? `[logical-width:${val}]` : '')],\n    [\n        'isolation',\n        {\n            isolate: 'isolate',\n            auto: 'isolation-auto',\n        },\n    ],\n    [\n        'margin',\n        (val) => {\n            const getPipeVal = (val: string) => {\n                const r = { '0': 'm_0', '0px': 'm_0', auto: 'm_auto' }[val];\n                if (r) {\n                    return r;\n                }\n                let vals = val.split(' ').filter((v) => v !== '');\n                if (vals.filter((v) => !isUnit(v)).length > 0) {\n                    return '';\n                }\n                if (useAllDefaultValues) {\n                    vals = vals.map((v) => getRemDefaultVal(v) ?? `[${v}]`);\n                } else {\n                    vals = vals.map((v) => `[${v}]`);\n                }\n                if (vals.length === 1 || new Set(vals).size === 1) {\n                    return `m_${vals[0]}`;\n                } else if (vals.length === 2) {\n                    return `mx_${vals[1]} my_${vals[0]}`;\n                } else if (vals.length === 3) {\n                    if (vals[0] === vals[2]) {\n                        return `mx_${vals[1]} my_${vals[0]}`;\n                    }\n                    return `mt_${vals[0]} mx_${vals[1]} mb_${vals[2]}`;\n                } else if (vals.length === 4) {\n                    if (vals[0] === vals[2]) {\n                        if (vals[1] === vals[3]) {\n                            return `mx_${vals[1]} my_${vals[0]}`;\n                        }\n                        return `ml_${vals[3]} mr_${vals[1]} my_${vals[0]}`;\n                    }\n                    if (vals[1] === vals[3]) {\n                        if (vals[0] === vals[2]) {\n                            return `mx_${vals[1]} my_${vals[0]}`;\n                        }\n                        return `ml_${vals[3]} mr_${vals[1]} my_${vals[0]}`;\n                    }\n                    return `mt_${vals[0]} mr_${vals[1]} mb_${vals[2]} ml_${vals[3]}`;\n                }\n                return '';\n            };\n            const v = getPipeVal(val);\n            return v === ''\n                ? ''\n                : v\n                      .split(' ')\n                      .map((t) =>\n                          t.includes('-')\n                              ? `-${t.replace('-', '').replace('_', '-')}`\n                              : t.replace('_', '-'),\n                      )\n                      .join(' ');\n        },\n    ],\n    [\n        'margin-bottom',\n        (val) => {\n            const t = hasNegative(val);\n            return (\n                { '0': 'mb-0', '0px': 'mb-0', auto: 'mb-auto' }[val] ??\n                (isUnit(val)\n                    ? `${t[0]}mb-${(useAllDefaultValues && getRemDefaultVal(t[1])) || `[${t[1]}]`}`\n                    : '')\n            );\n        },\n    ],\n    [\n        'margin-left',\n        (val) => {\n            const t = hasNegative(val);\n            return (\n                { '0': 'ml-0', '0px': 'ml-0', auto: 'ml-auto' }[val] ??\n                (isUnit(val)\n                    ? `${t[0]}ml-${(useAllDefaultValues && getRemDefaultVal(t[1])) || `[${t[1]}]`}`\n                    : '')\n            );\n        },\n    ],\n    [\n        'margin-right',\n        (val) => {\n            const t = hasNegative(val);\n            return (\n                { '0': 'mr-0', '0px': 'mr-0', auto: 'mr-auto' }[val] ??\n                (isUnit(val)\n                    ? `${t[0]}mr-${(useAllDefaultValues && getRemDefaultVal(t[1])) || `[${t[1]}]`}`\n                    : '')\n            );\n        },\n    ],\n    [\n        'margin-top',\n        (val) => {\n            const t = hasNegative(val);\n            return (\n                { '0': 'mt-0', '0px': 'mt-0', auto: 'mt-auto' }[val] ??\n                (isUnit(val)\n                    ? `${t[0]}mt-${(useAllDefaultValues && getRemDefaultVal(t[1])) || `[${t[1]}]`}`\n                    : '')\n            );\n        },\n    ],\n    ['mask', (val) => `[mask:${getCustomVal(val)}]`],\n    ['mask-clip', (val) => `[mask-clip:${getCustomVal(val)}]`],\n    ['mask-composite', (val) => `[mask-composite:${getCustomVal(val)}]`],\n    ['mask-image', (val) => `[mask-image:${getCustomVal(val)}]`],\n    ['mask-origin', (val) => `[mask-origin:${getCustomVal(val)}]`],\n    ['mask-position', (val) => `[mask-position:${getCustomVal(val)}]`],\n    ['mask-repeat', (val) => `[mask-repeat:${getCustomVal(val)}]`],\n    ['mask-size', (val) => `[mask-size:${getCustomVal(val)}]`],\n    [\n        'max-height',\n        (val) =>\n            isUnit(val)\n                ? ({ '0px': 'max-h-0', '100%': 'max-h-full', '100vh': 'max-h-screen' }[val] ??\n                  `max-h-[${val}]`)\n                : '',\n    ],\n    [\n        'max-width',\n        (val) =>\n            isUnit(val)\n                ? ({\n                      none: 'max-w-none',\n                      '100%': 'max-w-full',\n                      'min-content': 'max-w-min',\n                      'max-content': 'max-w-max',\n                  }[val] ?? `max-w-[${val}]`)\n                : '',\n    ],\n    [\n        'min-height',\n        (val) =>\n            isUnit(val)\n                ? ({ '0px': 'min-h-0', '100%': 'min-h-full', '100vh': 'min-h-screen' }[val] ??\n                  `min-h-[${val}]`)\n                : '',\n    ],\n    [\n        'min-width',\n        (val) =>\n            isUnit(val)\n                ? ({\n                      '0px': 'min-w-0',\n                      '100%': 'min-w-full',\n                      'min-content': 'min-w-min',\n                      'max-content': 'min-w-max',\n                  }[val] ?? `min-w-[${val}]`)\n                : '',\n    ],\n    [\n        'mix-blend-mode',\n        {\n            normal: 'mix-blend-normal',\n            multiply: 'mix-blend-multiply',\n            screen: 'mix-blend-screen',\n            overlay: 'mix-blend-overlay',\n            darken: 'mix-blend-darken',\n            lighten: 'mix-blend-lighten',\n            'color-dodge': 'mix-blend-color-dodge',\n            'color-burn': 'mix-blend-color-burn',\n            'hard-light': 'mix-blend-hard-light',\n            'soft-light': 'mix-blend-soft-light',\n            difference: 'mix-blend-difference',\n            exclusion: 'mix-blend-exclusion',\n            hue: 'mix-blend-hue',\n            saturation: 'mix-blend-saturation',\n            color: 'mix-blend-color',\n            luminosity: 'mix-blend-luminosity',\n        },\n    ],\n    ['nav-down', (val) => `[nav-down:${getCustomVal(val)}]`],\n    ['nav-index', (val) => (isUnit(val) ? `[nav-index:${val}]` : '')],\n    ['nav-left', (val) => (isUnit(val) ? `[nav-left:${val}]` : '')],\n    ['nav-right', (val) => (isUnit(val) ? `[nav-right:${val}]` : '')],\n    ['nav-up', (val) => (isUnit(val) ? `[nav-up:${val}]` : '')],\n    [\n        'object-fit',\n        {\n            contain: 'object-contain',\n            cover: 'object-cover',\n            fill: 'object-fill',\n            none: 'object-none',\n            'scale-down': 'object-scale-down',\n        },\n    ],\n    [\n        'object-position',\n        (val) =>\n            ({\n                bottom: 'object-bottom',\n                center: 'object-center',\n                left: 'object-left',\n                left_bottom: 'object-left-bottom',\n                left_top: 'object-left-top',\n                right: 'object-right',\n                right_bottom: 'object-right-bottom',\n                right_top: 'object-right-top',\n                top: 'object-top',\n            })[getCustomVal(val)] ?? '',\n    ],\n    [\n        'opacity',\n        (val) =>\n            ({\n                '0': 'opacity-0',\n                '1': 'opacity-100',\n                '0.05': 'opacity-5',\n                '0.1': 'opacity-10',\n                '0.2': 'opacity-20',\n                '0.25': 'opacity-25',\n                '0.3': 'opacity-30',\n                '0.4': 'opacity-40',\n                '0.5': 'opacity-50',\n                '0.6': 'opacity-60',\n                '0.7': 'opacity-70',\n                '0.75': 'opacity-75',\n                '0.8': 'opacity-80',\n                '0.9': 'opacity-90',\n                '0.95': 'opacity-95',\n            })[val] ?? (isUnit(val) ? `opacity-[${val}]` : ''),\n    ],\n    [\n        'order',\n        (val) =>\n            ({\n                '0': 'order-none',\n                '1': 'order-1',\n                '2': 'order-2',\n                '3': 'order-3',\n                '4': 'order-4',\n                '5': 'order-5',\n                '6': 'order-6',\n                '7': 'order-7',\n                '8': 'order-8',\n                '9': 'order-9',\n                '10': 'order-10',\n                '11': 'order-11',\n                '12': 'order-12',\n                '9999': 'order-last',\n                '-9999': 'order-first',\n            })[val] ?? (isUnit(val) ? `order-[${val}]` : ''),\n    ],\n    ['outline', (val) => `outline-[${getCustomVal(val)}]`],\n    [\n        'outline-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `outline-${val}`\n                : isColor(val, true)\n                  ? `outline-[${getCustomVal(val)}]`\n                  : '',\n    ],\n    ['outline-offset', (val) => (isUnit(val) ? `outline-offset-[${val}]` : '')],\n    [\n        'outline-style',\n        {\n            none: 'outline-[none]',\n            dotted: 'outline-dotted',\n            dashed: 'outline-dashed',\n            solid: '[outline-style:solid]',\n            double: 'outline-double',\n            groove: '[outline-style:groove]',\n            ridge: '[outline-style:ridge]',\n            inset: '[outline-style:inset]',\n            outset: '[outline-style:outset]',\n        },\n    ],\n    ['outline-width', (val) => (isUnit(val) ? `outline-[${val}]` : '')],\n    [\n        'overflow',\n        {\n            auto: 'overflow-auto',\n            hidden: 'overflow-hidden',\n            visible: 'overflow-visible',\n            scroll: 'overflow-scroll',\n        },\n    ],\n    ['overflow-anchor', (val) => `[overflow-anchor:${getCustomVal(val)}]`],\n    [\n        'overflow-wrap',\n        (val) => ({ 'break-word': 'break-words' })[val] ?? `[overflow-wrap:${getCustomVal(val)}]`,\n    ],\n    [\n        'overflow-x',\n        {\n            auto: 'overflow-x-auto',\n            hidden: 'overflow-x-hidden',\n            visible: 'overflow-x-visible',\n            scroll: 'overflow-x-scroll',\n        },\n    ],\n    [\n        'overflow-y',\n        {\n            auto: 'overflow-y-auto',\n            hidden: 'overflow-y-hidden',\n            visible: 'overflow-y-visible',\n            scroll: 'overflow-y-scroll',\n        },\n    ],\n    [\n        'overscroll-behavior',\n        {\n            auto: 'overscroll-auto',\n            contain: 'overscroll-contain',\n            none: 'overscroll-none',\n        },\n    ],\n    [\n        'overscroll-behavior-x',\n        {\n            auto: 'overscroll-x-auto',\n            contain: 'overscroll-x-contain',\n            none: 'overscroll-x-none',\n        },\n    ],\n    [\n        'overscroll-behavior-y',\n        {\n            auto: 'overscroll-y-auto',\n            contain: 'overscroll-y-contain',\n            none: 'overscroll-y-none',\n        },\n    ],\n    [\n        'padding',\n        (val) => {\n            const r = { '0': 'p-0', '0px': 'p-0' }[val];\n            if (r) {\n                return r;\n            }\n            let vals = val.split(' ').filter((v) => v !== '');\n            if (vals.filter((v) => !isUnit(v)).length > 0) {\n                return '';\n            }\n            if (useAllDefaultValues) {\n                vals = vals.map((v) => getRemDefaultVal(v) ?? `[${v}]`);\n            } else {\n                vals = vals.map((v) => `[${v}]`);\n            }\n            if (vals.length === 1 || new Set(vals).size === 1) {\n                return `p-${vals[0]}`;\n            } else if (vals.length === 2) {\n                return `px-${vals[1]} py-${vals[0]}`;\n            } else if (vals.length === 3) {\n                if (vals[0] === vals[2]) {\n                    return `px-${vals[1]} py-${vals[0]}`;\n                }\n                return `pt-${vals[0]} px-${vals[1]} pb-${vals[2]}`;\n            } else if (vals.length === 4) {\n                if (vals[0] === vals[2]) {\n                    if (vals[1] === vals[3]) {\n                        return `px-${vals[1]} py-${vals[0]}`;\n                    }\n                    return `pl-${vals[3]} pr-${vals[1]} py-${vals[0]}`;\n                }\n                if (vals[1] === vals[3]) {\n                    if (vals[0] === vals[2]) {\n                        return `px-${vals[1]} py-${vals[0]}`;\n                    }\n                    return `pl-${vals[3]} pr-${vals[1]} py-${vals[0]}`;\n                }\n                return `pt-${vals[0]} pr-${vals[1]} pb-${vals[2]} pl-${vals[3]}`;\n            }\n            return '';\n        },\n    ],\n    [\n        'padding-bottom',\n        (val) =>\n            ({ '0': 'pb-0', '0px': 'pb-0' })[val] ??\n            (isUnit(val)\n                ? `pb-${(useAllDefaultValues && getRemDefaultVal(val)) || `[${val}]`}`\n                : ''),\n    ],\n    [\n        'padding-left',\n        (val) =>\n            ({ '0': 'pl-0', '0px': 'pl-0' })[val] ??\n            (isUnit(val)\n                ? `pl-${(useAllDefaultValues && getRemDefaultVal(val)) || `[${val}]`}`\n                : ''),\n    ],\n    [\n        'padding-right',\n        (val) =>\n            ({ '0': 'pr-0', '0px': 'pr-0' })[val] ??\n            (isUnit(val)\n                ? `pr-${(useAllDefaultValues && getRemDefaultVal(val)) || `[${val}]`}`\n                : ''),\n    ],\n    [\n        'padding-top',\n        (val) =>\n            ({ '0': 'pt-0', '0px': 'pt-0' })[val] ??\n            (isUnit(val)\n                ? `pt-${(useAllDefaultValues && getRemDefaultVal(val)) || `[${val}]`}`\n                : ''),\n    ],\n    [\n        'page-break-after',\n        {\n            auto: '[page-break-after:auto]',\n            always: '[page-break-after:always]',\n            avoid: '[page-break-after:avoid]',\n            left: '[page-break-after:left]',\n            right: '[page-break-after:right]',\n            inherit: '[page-break-after:inherit]',\n            initial: '[page-break-after:initial]',\n        },\n    ],\n    [\n        'page-break-before',\n        {\n            auto: '[page-break-before:auto]',\n            always: '[page-break-before:always]',\n            avoid: '[page-break-before:avoid]',\n            left: '[page-break-before:left]',\n            right: '[page-break-before:right]',\n            inherit: '[page-break-before:inherit]',\n            initial: '[page-break-before:initial]',\n        },\n    ],\n    [\n        'page-break-inside',\n        {\n            auto: '[page-break-inside:auto]',\n            avoid: '[page-break-inside:avoid]',\n            inherit: '[page-break-inside:inherit]',\n            initial: '[page-break-inside:initial]',\n        },\n    ],\n    ['perspective', (val) => (isUnit(val) ? `[perspective:${val}]` : '')],\n    ['perspective-origin', (val) => `[perspective-origin:${getCustomVal(val)}]`],\n    [\n        'place-content',\n        {\n            center: 'place-content-center',\n            start: 'place-content-start',\n            end: 'place-content-end',\n            'space-between': 'place-content-between',\n            'space-around': 'place-content-around',\n            'space-evenly': 'place-content-evenly',\n            stretch: 'place-content-stretch',\n        },\n    ],\n    [\n        'place-items',\n        {\n            start: 'place-items-start',\n            end: 'place-items-end',\n            center: 'place-items-center',\n            stretch: 'place-items-stretch',\n        },\n    ],\n    [\n        'place-self',\n        {\n            auto: 'place-self-auto',\n            start: 'place-self-start',\n            end: 'place-self-end',\n            center: 'place-self-center',\n            stretch: 'place-self-stretch',\n        },\n    ],\n    [\n        'pointer-events',\n        {\n            none: 'pointer-events-none',\n            auto: 'pointer-events-auto',\n        },\n    ],\n    [\n        'position',\n        {\n            static: 'static',\n            fixed: 'fixed',\n            absolute: 'absolute',\n            relative: 'relative',\n            sticky: 'sticky',\n        },\n    ],\n    [\n        'punctuation-trim',\n        {\n            none: '[punctuation-trim:none]',\n            start: '[punctuation-trim:start]',\n            end: '[punctuation-trim:end]',\n            'allow-end': '[punctuation-trim:allow-end]',\n            adjacent: '[punctuation-trim:adjacent]',\n            initial: '[punctuation-trim:initial]',\n        },\n    ],\n    ['quotes', (val) => `[quotes:${getCustomVal(val)}]`],\n    [\n        'resize',\n        {\n            none: 'resize-none',\n            vertical: 'resize-y',\n            horizontal: 'resize-x',\n            both: 'resize',\n        },\n    ],\n    [\n        'right',\n        (val) => {\n            const t = hasNegative(val);\n            return isUnit(val)\n                ? `${t[0]}right-${getUnitMetacharactersVal(t[1], [CustomSelect.vw, CustomSelect.vh]) || `[${t[1]}]`}`\n                : '';\n        },\n    ],\n    ['rotate', (val) => `[rotate:${getCustomVal(val)}]`],\n    ['row-gap', (val) => ({ '0': 'gap-y-0' })[val] ?? (isUnit(val) ? `gap-y-[${val}]` : '')],\n    ['scroll-snap-align', (val) => `[scroll-snap-align:${getCustomVal(val)}]`],\n    ['scroll-snap-stop', (val) => `[scroll-snap-stop:${getCustomVal(val)}]`],\n    ['scroll-snap-type', (val) => `[scroll-snap-type:${getCustomVal(val)}]`],\n    ['scrollbar-width', (val) => (isUnit(val) ? `[scrollbar-width:${val}]` : '')],\n    ['shape-image-threshold', (val) => `[shape-image-threshold:${getCustomVal(val)}]`],\n    ['shape-margin', (val) => `[shape-margin:${getCustomVal(val)}]`],\n    ['shape-outside', (val) => `[shape-outside:${getCustomVal(val)}]`],\n    [\n        'stroke',\n        (val, isCustom = false) =>\n            (({\n                currentColor: 'stroke-current',\n                currentcolor: 'stroke-current',\n            })[val] ?? isCustom)\n                ? `stroke-${val}`\n                : isColor(val, true)\n                  ? `stroke-[${getCustomVal(val)}]`\n                  : '',\n    ],\n    ['stroke-width', (val) => (isUnit(val) ? `stroke-[${val}]` : '')],\n    ['tab-size', (val) => (isUnit(val) ? `[tab-size:${val}]` : '')],\n    [\n        'table-layout',\n        {\n            auto: 'table-auto',\n            fixed: 'table-fixed',\n        },\n    ],\n    ['target', (val) => `[target:${getCustomVal(val)}]`],\n    ['target-name', (val) => `[target-name:${getCustomVal(val)}]`],\n    [\n        'target-new',\n        {\n            window: '[target-new:window]',\n            tab: '[target-new:tab]',\n            none: '[target-new:none]',\n            initial: '[target-new:initial]',\n        },\n    ],\n    [\n        'target-position',\n        {\n            above: '[target-position:above]',\n            behind: '[target-position:behind]',\n            front: '[target-position:front]',\n            back: '[target-position:back]',\n            initial: '[target-position:initial]',\n        },\n    ],\n    [\n        'text-align',\n        {\n            left: 'text-left',\n            center: 'text-center',\n            right: 'text-right',\n            justify: 'text-justify',\n            start: 'text-start',\n            end: 'text-end',\n        },\n    ],\n    [\n        'text-align-last',\n        {\n            auto: '[text-align-last:auto]',\n            left: '[text-align-last:left]',\n            right: '[text-align-last:right]',\n            center: '[text-align-last:center]',\n            justify: '[text-align-last:justify]',\n            start: '[text-align-last:start]',\n            end: '[text-align-last:end]',\n            initial: '[text-align-last:initial]',\n            inherit: '[text-align-last:inherit]',\n        },\n    ],\n    [\n        'text-decoration',\n        {\n            underline: 'underline',\n            'line-through': 'line-through',\n            none: 'no-underline',\n        },\n    ],\n    [\n        'text-decoration-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[text-decoration-color:${val}]`\n                : isColor(val, true)\n                  ? `[text-decoration-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    [\n        'text-decoration-line',\n        {\n            none: '[text-decoration-line:none]',\n            underline: '[text-decoration-line:underline]',\n            overline: '[text-decoration-line:overline]',\n            'line-through': '[text-decoration-line:line-through]',\n            initial: '[text-decoration-line:initial]',\n            inherit: '[text-decoration-line:inherit]',\n        },\n    ],\n    ['text-decoration-skip-ink', (val) => `[text-decoration-skip-ink:${getCustomVal(val)}]`],\n    [\n        'text-decoration-style',\n        {\n            solid: '[text-decoration-style:solid]',\n            double: '[text-decoration-style:double]',\n            dotted: '[text-decoration-style:dotted]',\n            dashed: '[text-decoration-style:dashed]',\n            wavy: '[text-decoration-style:wavy]',\n            initial: '[text-decoration-style:initial]',\n            inherit: '[text-decoration-style:inherit]',\n        },\n    ],\n    [\n        'text-emphasis-color',\n        (val, isCustom = false) =>\n            isCustom\n                ? `[text-emphasis-color:${val}]`\n                : isColor(val, true)\n                  ? `[text-emphasis-color:${getCustomVal(val)}]`\n                  : '',\n    ],\n    ['text-emphasis-position', (val) => `[text-emphasis-position:${getCustomVal(val)}]`],\n    ['text-emphasis-style', (val) => `[text-emphasis-style:${getCustomVal(val)}]`],\n    ['text-indent', (val) => (isUnit(val) ? `[text-indent:${val}]` : '')],\n    [\n        'text-justify',\n        {\n            auto: '[text-justify:auto]',\n            none: '[text-justify:none]',\n            'inter-word': '[text-justify:inter-word]',\n            'inter-ideograph': '[text-justify:inter-ideograph]',\n            'inter-cluster': '[text-justify:inter-cluster]',\n            distribute: '[text-justify:distribute]',\n            kashida: '[text-justify:kashida]',\n            initial: '[text-justify:initial]',\n        },\n    ],\n    ['text-orientation', (val) => `[text-orientation:${getCustomVal(val)}]`],\n    ['text-outline', (val) => `[text-outline:${getCustomVal(val)}]`],\n    [\n        'text-overflow',\n        (val) =>\n            ({\n                ellipsis: 'overflow-ellipsis',\n                clip: 'overflow-clip',\n            })[val] ?? `[text-overflow:${getCustomVal(val)}]`,\n    ],\n    ['text-shadow', (val) => `[text-shadow:${getCustomVal(val)}]`],\n    [\n        'text-transform',\n        {\n            uppercase: 'uppercase',\n            lowercase: 'lowercase',\n            capitalize: 'capitalize',\n            none: 'normal-case',\n        },\n    ],\n    ['text-underline-offset', (val) => `[text-underline-offset:${getCustomVal(val)}]`],\n    ['text-underline-position', (val) => `[text-underline-position:${getCustomVal(val)}]`],\n    [\n        'text-wrap',\n        {\n            normal: '[text-wrap:normal]',\n            none: '[text-wrap:none]',\n            unrestricted: '[text-wrap:unrestricted]',\n            suppress: '[text-wrap:suppress]',\n            initial: '[text-wrap:initial]',\n        },\n    ],\n    [\n        'top',\n        (val) => {\n            const t = hasNegative(val);\n            return isUnit(val)\n                ? `${t[0]}top-${getUnitMetacharactersVal(t[1], [CustomSelect.vw, CustomSelect.vh]) || `[${t[1]}]`}`\n                : '';\n        },\n    ],\n    [\n        'transform',\n        (val) => {\n            const defaultVal = { none: 'transform-none' }[val];\n            if (defaultVal) {\n                return defaultVal;\n            }\n\n            const scaleDefaultVs: Record<string, string> = {\n                '0': '0',\n                '1': '100',\n                '.5': '50',\n                '.75': '75',\n                '.9': '90',\n                '.95': '95',\n                '1.05': '105',\n                '1.1': '110',\n                '1.25': '125',\n                '1.5': '150',\n            };\n            const rotateDefaultVs: Record<string, string> = {\n                '0deg': '0',\n                '1deg': '1',\n                '2deg': '2',\n                '3deg': '3',\n                '6deg': '6',\n                '12deg': '12',\n                '45deg': '45',\n                '90deg': '90',\n                '180deg': '180',\n            };\n            const skewDefaultVs: Record<string, string> = {\n                '0deg': '0',\n                '1deg': '1',\n                '2deg': '2',\n                '3deg': '3',\n                '6deg': '6',\n                '12deg': '12',\n            };\n            const translateDefaultVs: Record<string, string> = {\n                '0px': '0',\n                '1px': 'px',\n                '0.125rem': '0.5',\n                '0.25rem': '1',\n                '0.375rem': '1.5',\n                '0.5rem': '2',\n                '0.625rem': '2.5',\n                '0.75rem': '3',\n                '0.875rem': '3.5',\n                '1rem': '4',\n                '1.25rem': '5',\n                '1.5rem': '6',\n                '1.75rem': '7',\n                '2rem': '8',\n                '2.25rem': '9',\n                '2.5rem': '10',\n                '2.75rem': '11',\n                '3rem': '12',\n                '3.5rem': '14',\n                '4rem': '16',\n                '5rem': '20',\n                '6rem': '24',\n                '7rem': '28',\n                '8rem': '32',\n                '9rem': '36',\n                '10rem': '40',\n                '11rem': '44',\n                '12rem': '48',\n                '13rem': '52',\n                '14rem': '56',\n                '15rem': '60',\n                '16rem': '64',\n                '18rem': '72',\n                '20rem': '80',\n                '24rem': '96',\n                '50%': '1/2',\n                '33.33%': '1/3',\n                '66.66%': '2/3',\n                '25%': '1/4',\n                '75%': '3/4',\n                '100%': 'full',\n            };\n            const transformValConfig: Record<string, (v: string) => string | undefined> = {\n                scale: (v: string) => {\n                    const vs = v.split(',');\n                    if (vs.length === 3) {\n                        return undefined;\n                    }\n                    if (vs[0] === vs[1] || vs.length === 1) {\n                        return `scale-${customTheme.scale?.[vs[0]] || (useAllDefaultValues && scaleDefaultVs[vs[0]]) || `[${vs[0]}]`}`;\n                    }\n                    return vs\n                        .map((v, idx) => {\n                            return `scale-${idx === 0 ? 'x' : 'y'}-${customTheme.scale?.[v] || (useAllDefaultValues && scaleDefaultVs[v]) || `[${v}]`}`;\n                        })\n                        .join(' ');\n                },\n                scaleX: (v: string) =>\n                    `scale-x-${customTheme.scale?.[v] || (useAllDefaultValues && scaleDefaultVs[v]) || `[${v}]`}`,\n                scaleY: (v: string) =>\n                    `scale-y-${customTheme.scale?.[v] || (useAllDefaultValues && scaleDefaultVs[v]) || `[${v}]`}`,\n                rotate: (v: string) => {\n                    const vs = v.split(',');\n                    if (vs.length > 1) {\n                        if (\n                            vs.length === 3 &&\n                            ['0', '0deg'].findIndex((v) => v === vs[0]) > -1 &&\n                            ['0', '0deg'].findIndex((v) => v === vs[1]) > -1\n                        ) {\n                            const t = hasNegative(vs[2]);\n                            return `${t[0]}rotate-${customTheme.rotate?.[t[1]] || (useAllDefaultValues && rotateDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                        }\n                        return undefined;\n                    }\n                    const t = hasNegative(vs[0]);\n                    return `${t[0]}rotate-${customTheme.rotate?.[t[1]] || (useAllDefaultValues && rotateDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                },\n                rotateZ: (v: string) => {\n                    const t = hasNegative(v);\n                    return `${t[0]}rotate-${customTheme.rotate?.[t[1]] || (useAllDefaultValues && rotateDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                },\n                translate: (v: string) => {\n                    const vs = v.split(',');\n                    if (vs.length === 3) {\n                        return undefined;\n                    }\n                    return vs\n                        .map((v, idx) => {\n                            const t = hasNegative(v);\n                            if (/^\\d+\\.[1-9]{2,}%$/.test(t[1])) {\n                                t[1] = `${Number(t[1].slice(0, -1))\n                                    .toFixed(6)\n                                    .replace(/(\\.[1-9]{2})\\d+/, '$1')}%`;\n                            }\n                            return `${t[0]}translate-${idx === 0 ? 'x' : 'y'}-${customTheme.translate?.[t[1]] || (useAllDefaultValues && translateDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                        })\n                        .join(' ');\n                },\n                translateX: (v: string) => {\n                    const t = hasNegative(v);\n                    if (/^\\d+\\.[1-9]{2,}%$/.test(t[1])) {\n                        t[1] = `${Number(t[1].slice(0, -1))\n                            .toFixed(6)\n                            .replace(/(\\.[1-9]{2})\\d+/, '$1')}%`;\n                    }\n                    return `${t[0]}translate-x-${customTheme.translate?.[t[1]] || (useAllDefaultValues && translateDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                },\n                translateY: (v: string) => {\n                    const t = hasNegative(v);\n                    if (/^\\d+\\.[1-9]{2,}%$/.test(t[1])) {\n                        t[1] = `${Number(t[1].slice(0, -1))\n                            .toFixed(6)\n                            .replace(/(\\.[1-9]{2})\\d+/, '$1')}%`;\n                    }\n                    return `${t[0]}translate-y-${customTheme.translate?.[t[1]] || (useAllDefaultValues && translateDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                },\n                skew: (v: string) => {\n                    const vs = v.split(',');\n                    if (vs.length === 3) {\n                        return undefined;\n                    }\n                    return vs\n                        .map((v, idx) => {\n                            const t = hasNegative(v);\n                            return `${t[0]}skew-${idx === 0 ? 'x' : 'y'}-${customTheme.skew?.[t[1]] || (useAllDefaultValues && skewDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                        })\n                        .join(' ');\n                },\n                skewX: (v: string) => {\n                    const t = hasNegative(v);\n                    return `${t[0]}skew-x-${customTheme.skew?.[t[1]] || (useAllDefaultValues && skewDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                },\n                skewY: (v: string) => {\n                    const t = hasNegative(v);\n                    return `${t[0]}skew-y-${customTheme.skew?.[t[1]] || (useAllDefaultValues && skewDefaultVs[t[1]]) || `[${t[1]}]`}`;\n                },\n            };\n            const vals = getCustomVal(val)\n                .replace(/\\(.+?\\)/g, (v) => v.replace(/_/g, ''))\n                .split(')_')\n                .map((v) => `${v})`);\n            vals[vals.length - 1] = vals[vals.length - 1].slice(0, -1);\n\n            let canUse = true;\n            const res = vals.map((v) => {\n                let canUsePipeV = false;\n                const pipeV = v.replace(/^([a-zA-Z0-9_-]+)\\((.+?)\\)$/, (r, k: string, v) => {\n                    canUsePipeV = true;\n                    const tmpRes = transformValConfig[k]?.(v) ?? (canUse = false);\n                    return typeof tmpRes === 'string' ? tmpRes : '';\n                });\n                return canUsePipeV ? pipeV : '';\n            });\n            return canUse ? `${[...new Set(res)].join(' ')}` : `[transform:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'transform-origin',\n        (val) =>\n            ({\n                center: 'origin-center',\n                top: 'origin-top',\n                top_right: 'origin-top-right',\n                right: 'origin-right',\n                bottom_right: 'origin-bottom-right',\n                bottom: 'origin-bottom',\n                bottom_left: 'origin-bottom-left',\n                left: 'origin-left',\n                top_left: 'origin-top-left',\n            })[getCustomVal(val)] ?? `origin-[${getCustomVal(val)}]`,\n    ],\n    [\n        'transform-style',\n        {\n            flat: '[transform-style:flat]',\n            'preserve-3d': '[transform-style:preserve-3d]',\n            initial: '[transform-style:initial]',\n        },\n    ],\n    [\n        'transition',\n        (val) => {\n            if (val === 'none') {\n                return 'transition-none';\n            }\n            return `[transition:${getCustomVal(val)}]`;\n        },\n    ],\n    [\n        'transition-delay',\n        (val) => {\n            val = val.replace(\n                /^([.\\d]+)s$/,\n                (v, $1) => `${($1 * 1000).toFixed(6).replace(/\\.?0+$/, '')}ms`,\n            );\n            return (\n                {\n                    '75ms': 'delay-75',\n                    '100ms': 'delay-100',\n                    '150ms': 'delay-150',\n                    '200ms': 'delay-200',\n                    '300ms': 'delay-300',\n                    '500ms': 'delay-500',\n                    '700ms': 'delay-700',\n                    '1000ms': 'delay-1000',\n                }[val] ?? (/^[.\\d]+[ms]{1,2}$/.test(val) ? `delay-[${getCustomVal(val)}]` : '')\n            );\n        },\n    ],\n    [\n        'transition-duration',\n        (val) => {\n            val = val.replace(\n                /^([.\\d]+)s$/,\n                (v, $1) => `${($1 * 1000).toFixed(6).replace(/\\.?0+$/, '')}ms`,\n            );\n            return (\n                {\n                    '75ms': 'duration-75',\n                    '100ms': 'duration-100',\n                    '150ms': 'duration-150',\n                    '200ms': 'duration-200',\n                    '300ms': 'duration-300',\n                    '500ms': 'duration-500',\n                    '700ms': 'duration-700',\n                    '1000ms': 'duration-1000',\n                }[val] ?? (/^[.\\d]+[ms]{1,2}$/.test(val) ? `duration-[${getCustomVal(val)}]` : '')\n            );\n        },\n    ],\n    ['transition-property', (val) => `[transition-property:${getCustomVal(val)}]`],\n    [\n        'transition-timing-function',\n        (val) => {\n            val = val.replace(/\\s/g, '');\n            return (\n                {\n                    linear: 'ease-linear',\n                    'cubic-bezier(0.4,0,1,1)': 'ease-in',\n                    'cubic-bezier(0,0,0.2,1)': 'ease-out',\n                    'cubic-bezier(0.4,0,0.2,1)': 'ease-in-out',\n                    ease: 'ease-[ease]',\n                    'ease-in': 'ease-in',\n                    'ease-out': 'ease-out',\n                    'ease-in-out': 'ease-in-out',\n                }[val] ?? (val.startsWith('cubic-bezier') ? `ease-[${getCustomVal(val)}]` : '')\n            );\n        },\n    ],\n    [\n        'unicode-bidi',\n        {\n            normal: '[unicode-bidi:normal]',\n            embed: '[unicode-bidi:embed]',\n            'bidi-override': '[unicode-bidi:bidi-override]',\n            initial: '[unicode-bidi:initial]',\n            inherit: '[unicode-bidi:inherit]',\n        },\n    ],\n    [\n        'user-select',\n        {\n            none: 'select-none',\n            text: 'select-text',\n            all: 'select-all',\n            auto: 'select-auto',\n        },\n    ],\n    [\n        'vertical-align',\n        {\n            baseline: 'align-baseline',\n            top: 'align-top',\n            middle: 'align-middle',\n            bottom: 'align-bottom',\n            'text-top': 'align-text-top',\n            'text-bottom': 'align-text-bottom',\n        },\n    ],\n    [\n        'visibility',\n        {\n            visible: 'visible',\n            hidden: 'invisible',\n        },\n    ],\n    [\n        'white-space',\n        {\n            normal: 'whitespace-normal',\n            nowrap: 'whitespace-nowrap',\n            pre: 'whitespace-pre',\n            'pre-line': 'whitespace-pre-line',\n            'pre-wrap': 'whitespace-pre-wrap',\n        },\n    ],\n    [\n        'width',\n        (val) =>\n            isUnit(val)\n                ? `w-${(useAllDefaultValues && getRemDefaultVal(val)) || getUnitMetacharactersVal(val, [CustomSelect.vh]) || `[${val}]`}`\n                : '',\n    ],\n    [\n        'word-break',\n        {\n            'break-all': 'break-all',\n            normal: '[word-break:normal]',\n            'keep-all': '[word-break:keep-all]',\n            initial: '[word-break:initial]',\n        },\n    ],\n    ['word-spacing', (val) => (isUnit(val) ? `[word-spacing:${val}]` : '')],\n    [\n        'word-wrap',\n        {\n            normal: '[word-wrap:normal]',\n            'break-word': '[word-wrap:break-word]',\n            initial: '[word-wrap:initial]',\n        },\n    ],\n    ['writing-mode', (val) => `[writing-mode:${getCustomVal(val)}]`],\n    [\n        'z-index',\n        (val) =>\n            ({\n                '0': 'z-0',\n                '10': 'z-10',\n                '20': 'z-20',\n                '30': 'z-30',\n                '40': 'z-40',\n                '50': 'z-50',\n                auto: 'z-auto',\n            })[val] ?? (typeof val === 'number' ? `z-[${val}]` : ''),\n    ],\n]);\n\ninterface CssCodeParse {\n    selectorName: string;\n    cssCode: string | CssCodeParse[];\n}\n\nconst parsingCode = (code: string): CssCodeParse[] => {\n    code = code.replace(/[\\n\\r]/g, '').trim();\n    const tmpCodes: CssCodeParse[] = [];\n    let index = 0;\n    let isSelectorName = true;\n    let bracketsCount = 0;\n    for (let i = 0; i < code.length; i++) {\n        const char = code[i];\n        if (['{', '}'].includes(char)) {\n            if (char === '{') {\n                if (bracketsCount++ === 0) {\n                    isSelectorName = false;\n                } else {\n                    tmpCodes[index][isSelectorName ? 'selectorName' : 'cssCode'] += char;\n                }\n            } else {\n                if (--bracketsCount === 0) {\n                    const cssCode = tmpCodes[index].cssCode;\n                    if (typeof cssCode === 'string' && cssCode.includes('{')) {\n                        tmpCodes[index].cssCode = parsingCode(cssCode);\n                    }\n                    index++;\n                    isSelectorName = true;\n                } else {\n                    tmpCodes[index][isSelectorName ? 'selectorName' : 'cssCode'] += char;\n                }\n            }\n        } else {\n            if (!tmpCodes[index]) {\n                tmpCodes[index] = {\n                    selectorName: '',\n                    cssCode: '',\n                };\n            }\n            tmpCodes[index][isSelectorName ? 'selectorName' : 'cssCode'] += char;\n        }\n    }\n    return tmpCodes.map((v) => ({\n        selectorName: v.selectorName.trim(),\n        cssCode: typeof v.cssCode === 'string' ? v.cssCode.trim() : v.cssCode,\n    }));\n};\n\nconst moreDefaultMediaVals: Record<string, string> = {\n    '@media(min-width:640px)': 'sm',\n    '@media(min-width:768px)': 'md',\n    '@media(min-width:1024px)': 'lg',\n    '@media(min-width:1280px)': 'xl',\n    '@media(min-width:1536px)': '2xl',\n    '@media_not_all_and(min-width:640px)': 'max-sm',\n    '@media_not_all_and(min-width:768px)': 'max-md',\n    '@media_not_all_and(min-width:1024px)': 'max-lg',\n    '@media_not_all_and(min-width:1280px)': 'max-xl',\n    '@media_not_all_and(min-width:1536px)': 'max-2xl',\n};\n\nconst moreDefaultValuesMap: Record<string, Record<string, string>> = {\n    top: {\n        '0px': 'top-0',\n        '1px': 'top-px',\n        '0.125rem': 'top-0.5',\n        '0.25rem': 'top-1',\n        '0.375rem': 'top-1.5',\n        '0.5rem': 'top-2',\n        '0.625rem': 'top-2.5',\n        '0.75rem': 'top-3',\n        '0.875rem': 'top-3.5',\n        '1rem': 'top-4',\n        '1.25rem': 'top-5',\n        '1.5rem': 'top-6',\n        '1.75rem': 'top-7',\n        '2rem': 'top-8',\n        '2.25rem': 'top-9',\n        '2.5rem': 'top-10',\n        '2.75rem': 'top-11',\n        '3rem': 'top-12',\n        '3.5rem': 'top-14',\n        '4rem': 'top-16',\n        '5rem': 'top-20',\n        '6rem': 'top-24',\n        '7rem': 'top-28',\n        '8rem': 'top-32',\n        '9rem': 'top-36',\n        '10rem': 'top-40',\n        '11rem': 'top-44',\n        '12rem': 'top-48',\n        '13rem': 'top-52',\n        '14rem': 'top-56',\n        '15rem': 'top-60',\n        '16rem': 'top-64',\n        '18rem': 'top-72',\n        '20rem': 'top-80',\n        '24rem': 'top-96',\n        auto: 'top-auto',\n        '50%': 'top-2/4',\n        '33.333333%': 'top-1/3',\n        '66.666667%': 'top-2/3',\n        '25%': 'top-1/4',\n        '75%': 'top-3/4',\n        '100%': 'top-full',\n        '-1px': '-top-px',\n        '-0.125rem': '-top-0.5',\n        '-0.25rem': '-top-1',\n        '-0.375rem': '-top-1.5',\n        '-0.5rem': '-top-2',\n        '-0.625rem': '-top-2.5',\n        '-0.75rem': '-top-3',\n        '-0.875rem': '-top-3.5',\n        '-1rem': '-top-4',\n        '-1.25rem': '-top-5',\n        '-1.5rem': '-top-6',\n        '-1.75rem': '-top-7',\n        '-2rem': '-top-8',\n        '-2.25rem': '-top-9',\n        '-2.5rem': '-top-10',\n        '-2.75rem': '-top-11',\n        '-3rem': '-top-12',\n        '-3.5rem': '-top-14',\n        '-4rem': '-top-16',\n        '-5rem': '-top-20',\n        '-6rem': '-top-24',\n        '-7rem': '-top-28',\n        '-8rem': '-top-32',\n        '-9rem': '-top-36',\n        '-10rem': '-top-40',\n        '-11rem': '-top-44',\n        '-12rem': '-top-48',\n        '-13rem': '-top-52',\n        '-14rem': '-top-56',\n        '-15rem': '-top-60',\n        '-16rem': '-top-64',\n        '-18rem': '-top-72',\n        '-20rem': '-top-80',\n        '-24rem': '-top-96',\n        '-50%': '-top-2/4',\n        '-33.333333%': '-top-1/3',\n        '-66.666667%': '-top-2/3',\n        '-25%': '-top-1/4',\n        '-75%': '-top-3/4',\n        '-100%': '-top-full',\n    },\n    bottom: {\n        '0px': 'bottom-0',\n        '1px': 'bottom-px',\n        '0.125rem': 'bottom-0.5',\n        '0.25rem': 'bottom-1',\n        '0.375rem': 'bottom-1.5',\n        '0.5rem': 'bottom-2',\n        '0.625rem': 'bottom-2.5',\n        '0.75rem': 'bottom-3',\n        '0.875rem': 'bottom-3.5',\n        '1rem': 'bottom-4',\n        '1.25rem': 'bottom-5',\n        '1.5rem': 'bottom-6',\n        '1.75rem': 'bottom-7',\n        '2rem': 'bottom-8',\n        '2.25rem': 'bottom-9',\n        '2.5rem': 'bottom-10',\n        '2.75rem': 'bottom-11',\n        '3rem': 'bottom-12',\n        '3.5rem': 'bottom-14',\n        '4rem': 'bottom-16',\n        '5rem': 'bottom-20',\n        '6rem': 'bottom-24',\n        '7rem': 'bottom-28',\n        '8rem': 'bottom-32',\n        '9rem': 'bottom-36',\n        '10rem': 'bottom-40',\n        '11rem': 'bottom-44',\n        '12rem': 'bottom-48',\n        '13rem': 'bottom-52',\n        '14rem': 'bottom-56',\n        '15rem': 'bottom-60',\n        '16rem': 'bottom-64',\n        '18rem': 'bottom-72',\n        '20rem': 'bottom-80',\n        '24rem': 'bottom-96',\n        auto: 'bottom-auto',\n        '50%': 'bottom-2/4',\n        '33.333333%': 'bottom-1/3',\n        '66.666667%': 'bottom-2/3',\n        '25%': 'bottom-1/4',\n        '75%': 'bottom-3/4',\n        '100%': 'bottom-full',\n        '-1px': '-bottom-px',\n        '-0.125rem': '-bottom-0.5',\n        '-0.25rem': '-bottom-1',\n        '-0.375rem': '-bottom-1.5',\n        '-0.5rem': '-bottom-2',\n        '-0.625rem': '-bottom-2.5',\n        '-0.75rem': '-bottom-3',\n        '-0.875rem': '-bottom-3.5',\n        '-1rem': '-bottom-4',\n        '-1.25rem': '-bottom-5',\n        '-1.5rem': '-bottom-6',\n        '-1.75rem': '-bottom-7',\n        '-2rem': '-bottom-8',\n        '-2.25rem': '-bottom-9',\n        '-2.5rem': '-bottom-10',\n        '-2.75rem': '-bottom-11',\n        '-3rem': '-bottom-12',\n        '-3.5rem': '-bottom-14',\n        '-4rem': '-bottom-16',\n        '-5rem': '-bottom-20',\n        '-6rem': '-bottom-24',\n        '-7rem': '-bottom-28',\n        '-8rem': '-bottom-32',\n        '-9rem': '-bottom-36',\n        '-10rem': '-bottom-40',\n        '-11rem': '-bottom-44',\n        '-12rem': '-bottom-48',\n        '-13rem': '-bottom-52',\n        '-14rem': '-bottom-56',\n        '-15rem': '-bottom-60',\n        '-16rem': '-bottom-64',\n        '-18rem': '-bottom-72',\n        '-20rem': '-bottom-80',\n        '-24rem': '-bottom-96',\n        '-50%': '-bottom-2/4',\n        '-33.333333%': '-bottom-1/3',\n        '-66.666667%': '-bottom-2/3',\n        '-25%': '-bottom-1/4',\n        '-75%': '-bottom-3/4',\n        '-100%': '-bottom-full',\n    },\n    left: {\n        '0px': 'left-0',\n        '1px': 'left-px',\n        '0.125rem': 'left-0.5',\n        '0.25rem': 'left-1',\n        '0.375rem': 'left-1.5',\n        '0.5rem': 'left-2',\n        '0.625rem': 'left-2.5',\n        '0.75rem': 'left-3',\n        '0.875rem': 'left-3.5',\n        '1rem': 'left-4',\n        '1.25rem': 'left-5',\n        '1.5rem': 'left-6',\n        '1.75rem': 'left-7',\n        '2rem': 'left-8',\n        '2.25rem': 'left-9',\n        '2.5rem': 'left-10',\n        '2.75rem': 'left-11',\n        '3rem': 'left-12',\n        '3.5rem': 'left-14',\n        '4rem': 'left-16',\n        '5rem': 'left-20',\n        '6rem': 'left-24',\n        '7rem': 'left-28',\n        '8rem': 'left-32',\n        '9rem': 'left-36',\n        '10rem': 'left-40',\n        '11rem': 'left-44',\n        '12rem': 'left-48',\n        '13rem': 'left-52',\n        '14rem': 'left-56',\n        '15rem': 'left-60',\n        '16rem': 'left-64',\n        '18rem': 'left-72',\n        '20rem': 'left-80',\n        '24rem': 'left-96',\n        auto: 'left-auto',\n        '50%': 'left-2/4',\n        '33.333333%': 'left-1/3',\n        '66.666667%': 'left-2/3',\n        '25%': 'left-1/4',\n        '75%': 'left-3/4',\n        '100%': 'left-full',\n        '-1px': '-left-px',\n        '-0.125rem': '-left-0.5',\n        '-0.25rem': '-left-1',\n        '-0.375rem': '-left-1.5',\n        '-0.5rem': '-left-2',\n        '-0.625rem': '-left-2.5',\n        '-0.75rem': '-left-3',\n        '-0.875rem': '-left-3.5',\n        '-1rem': '-left-4',\n        '-1.25rem': '-left-5',\n        '-1.5rem': '-left-6',\n        '-1.75rem': '-left-7',\n        '-2rem': '-left-8',\n        '-2.25rem': '-left-9',\n        '-2.5rem': '-left-10',\n        '-2.75rem': '-left-11',\n        '-3rem': '-left-12',\n        '-3.5rem': '-left-14',\n        '-4rem': '-left-16',\n        '-5rem': '-left-20',\n        '-6rem': '-left-24',\n        '-7rem': '-left-28',\n        '-8rem': '-left-32',\n        '-9rem': '-left-36',\n        '-10rem': '-left-40',\n        '-11rem': '-left-44',\n        '-12rem': '-left-48',\n        '-13rem': '-left-52',\n        '-14rem': '-left-56',\n        '-15rem': '-left-60',\n        '-16rem': '-left-64',\n        '-18rem': '-left-72',\n        '-20rem': '-left-80',\n        '-24rem': '-left-96',\n        '-50%': '-left-2/4',\n        '-33.333333%': '-left-1/3',\n        '-66.666667%': '-left-2/3',\n        '-25%': '-left-1/4',\n        '-75%': '-left-3/4',\n        '-100%': '-left-full',\n    },\n    right: {\n        '0px': 'right-0',\n        '1px': 'right-px',\n        '0.125rem': 'right-0.5',\n        '0.25rem': 'right-1',\n        '0.375rem': 'right-1.5',\n        '0.5rem': 'right-2',\n        '0.625rem': 'right-2.5',\n        '0.75rem': 'right-3',\n        '0.875rem': 'right-3.5',\n        '1rem': 'right-4',\n        '1.25rem': 'right-5',\n        '1.5rem': 'right-6',\n        '1.75rem': 'right-7',\n        '2rem': 'right-8',\n        '2.25rem': 'right-9',\n        '2.5rem': 'right-10',\n        '2.75rem': 'right-11',\n        '3rem': 'right-12',\n        '3.5rem': 'right-14',\n        '4rem': 'right-16',\n        '5rem': 'right-20',\n        '6rem': 'right-24',\n        '7rem': 'right-28',\n        '8rem': 'right-32',\n        '9rem': 'right-36',\n        '10rem': 'right-40',\n        '11rem': 'right-44',\n        '12rem': 'right-48',\n        '13rem': 'right-52',\n        '14rem': 'right-56',\n        '15rem': 'right-60',\n        '16rem': 'right-64',\n        '18rem': 'right-72',\n        '20rem': 'right-80',\n        '24rem': 'right-96',\n        auto: 'right-auto',\n        '50%': 'right-2/4',\n        '33.333333%': 'right-1/3',\n        '66.666667%': 'right-2/3',\n        '25%': 'right-1/4',\n        '75%': 'right-3/4',\n        '100%': 'right-full',\n        '-1px': '-right-px',\n        '-0.125rem': '-right-0.5',\n        '-0.25rem': '-right-1',\n        '-0.375rem': '-right-1.5',\n        '-0.5rem': '-right-2',\n        '-0.625rem': '-right-2.5',\n        '-0.75rem': '-right-3',\n        '-0.875rem': '-right-3.5',\n        '-1rem': '-right-4',\n        '-1.25rem': '-right-5',\n        '-1.5rem': '-right-6',\n        '-1.75rem': '-right-7',\n        '-2rem': '-right-8',\n        '-2.25rem': '-right-9',\n        '-2.5rem': '-right-10',\n        '-2.75rem': '-right-11',\n        '-3rem': '-right-12',\n        '-3.5rem': '-right-14',\n        '-4rem': '-right-16',\n        '-5rem': '-right-20',\n        '-6rem': '-right-24',\n        '-7rem': '-right-28',\n        '-8rem': '-right-32',\n        '-9rem': '-right-36',\n        '-10rem': '-right-40',\n        '-11rem': '-right-44',\n        '-12rem': '-right-48',\n        '-13rem': '-right-52',\n        '-14rem': '-right-56',\n        '-15rem': '-right-60',\n        '-16rem': '-right-64',\n        '-18rem': '-right-72',\n        '-20rem': '-right-80',\n        '-24rem': '-right-96',\n        '-50%': '-right-2/4',\n        '-33.333333%': '-right-1/3',\n        '-66.666667%': '-right-2/3',\n        '-25%': '-right-1/4',\n        '-75%': '-right-3/4',\n        '-100%': '-right-full',\n    },\n    gap: {\n        '0px': 'gap-0',\n        '0.125rem': 'gap-0.5',\n        '0.25rem': 'gap-1',\n        '0.375rem': 'gap-1.5',\n        '0.5rem': 'gap-2',\n        '0.625rem': 'gap-2.5',\n        '0.75rem': 'gap-3',\n        '0.875rem': 'gap-3.5',\n        '1rem': 'gap-4',\n        '1.25rem': 'gap-5',\n        '1.5rem': 'gap-6',\n        '1.75rem': 'gap-7',\n        '2rem': 'gap-8',\n        '2.25rem': 'gap-9',\n        '2.5rem': 'gap-10',\n        '2.75rem': 'gap-11',\n        '3rem': 'gap-12',\n        '3.5rem': 'gap-14',\n        '4rem': 'gap-16',\n        '5rem': 'gap-20',\n        '6rem': 'gap-24',\n        '7rem': 'gap-28',\n        '8rem': 'gap-32',\n        '9rem': 'gap-36',\n        '10rem': 'gap-40',\n        '11rem': 'gap-44',\n        '12rem': 'gap-48',\n        '13rem': 'gap-52',\n        '14rem': 'gap-56',\n        '15rem': 'gap-60',\n        '16rem': 'gap-64',\n        '18rem': 'gap-72',\n        '20rem': 'gap-80',\n        '24rem': 'gap-96',\n    },\n    'column-gap': {\n        '0px': 'gap-x-0',\n        '1px': 'gap-x-px',\n        '0.125rem': 'gap-x-0.5',\n        '0.25rem': 'gap-x-1',\n        '0.375rem': 'gap-x-1.5',\n        '0.5rem': 'gap-x-2',\n        '0.625rem': 'gap-x-2.5',\n        '0.75rem': 'gap-x-3',\n        '0.875rem': 'gap-x-3.5',\n        '1rem': 'gap-x-4',\n        '1.25rem': 'gap-x-5',\n        '1.5rem': 'gap-x-6',\n        '1.75rem': 'gap-x-7',\n        '2rem': 'gap-x-8',\n        '2.25rem': 'gap-x-9',\n        '2.5rem': 'gap-x-10',\n        '2.75rem': 'gap-x-11',\n        '3rem': 'gap-x-12',\n        '3.5rem': 'gap-x-14',\n        '4rem': 'gap-x-16',\n        '5rem': 'gap-x-20',\n        '6rem': 'gap-x-24',\n        '7rem': 'gap-x-28',\n        '8rem': 'gap-x-32',\n        '9rem': 'gap-x-36',\n        '10rem': 'gap-x-40',\n        '11rem': 'gap-x-44',\n        '12rem': 'gap-x-48',\n        '13rem': 'gap-x-52',\n        '14rem': 'gap-x-56',\n        '15rem': 'gap-x-60',\n        '16rem': 'gap-x-64',\n        '18rem': 'gap-x-72',\n        '20rem': 'gap-x-80',\n        '24rem': 'gap-x-96',\n    },\n    'row-gap': {\n        '0px': 'gap-y-0',\n        '1px': 'gap-y-px',\n        '0.125rem': 'gap-y-0.5',\n        '0.25rem': 'gap-y-1',\n        '0.375rem': 'gap-y-1.5',\n        '0.5rem': 'gap-y-2',\n        '0.625rem': 'gap-y-2.5',\n        '0.75rem': 'gap-y-3',\n        '0.875rem': 'gap-y-3.5',\n        '1rem': 'gap-y-4',\n        '1.25rem': 'gap-y-5',\n        '1.5rem': 'gap-y-6',\n        '1.75rem': 'gap-y-7',\n        '2rem': 'gap-y-8',\n        '2.25rem': 'gap-y-9',\n        '2.5rem': 'gap-y-10',\n        '2.75rem': 'gap-y-11',\n        '3rem': 'gap-y-12',\n        '3.5rem': 'gap-y-14',\n        '4rem': 'gap-y-16',\n        '5rem': 'gap-y-20',\n        '6rem': 'gap-y-24',\n        '7rem': 'gap-y-28',\n        '8rem': 'gap-y-32',\n        '9rem': 'gap-y-36',\n        '10rem': 'gap-y-40',\n        '11rem': 'gap-y-44',\n        '12rem': 'gap-y-48',\n        '13rem': 'gap-y-52',\n        '14rem': 'gap-y-56',\n        '15rem': 'gap-y-60',\n        '16rem': 'gap-y-64',\n        '18rem': 'gap-y-72',\n        '20rem': 'gap-y-80',\n        '24rem': 'gap-y-96',\n    },\n    'max-width': {\n        '0rem': 'max-w-0',\n        '20rem': 'max-w-xs',\n        '24rem': 'max-w-sm',\n        '28rem': 'max-w-md',\n        '32rem': 'max-w-lg',\n        '36rem': 'max-w-xl',\n        '42rem': 'max-w-2xl',\n        '48rem': 'max-w-3xl',\n        '56rem': 'max-w-4xl',\n        '64rem': 'max-w-5xl',\n        '72rem': 'max-w-6xl',\n        '80rem': 'max-w-7xl',\n        '65ch': 'max-w-prose',\n        '640px': 'max-w-screen-sm',\n        '768px': 'max-w-screen-md',\n        '1024px': 'max-w-screen-lg',\n        '1280px': 'max-w-screen-xl',\n        '1536px': 'max-w-screen-2xl',\n    },\n    'max-height': {\n        '1px': 'max-h-px',\n        '0.125rem': 'max-h-0.5',\n        '0.25rem': 'max-h-1',\n        '0.375rem': 'max-h-1.5',\n        '0.5rem': 'max-h-2',\n        '0.625rem': 'max-h-2.5',\n        '0.75rem': 'max-h-3',\n        '0.875rem': 'max-h-3.5',\n        '1rem': 'max-h-4',\n        '1.25rem': 'max-h-5',\n        '1.5rem': 'max-h-6',\n        '1.75rem': 'max-h-7',\n        '2rem': 'max-h-8',\n        '2.25rem': 'max-h-9',\n        '2.5rem': 'max-h-10',\n        '2.75rem': 'max-h-11',\n        '3rem': 'max-h-12',\n        '3.5rem': 'max-h-14',\n        '4rem': 'max-h-16',\n        '5rem': 'max-h-20',\n        '6rem': 'max-h-24',\n        '7rem': 'max-h-28',\n        '8rem': 'max-h-32',\n        '9rem': 'max-h-36',\n        '10rem': 'max-h-40',\n        '11rem': 'max-h-44',\n        '12rem': 'max-h-48',\n        '13rem': 'max-h-52',\n        '14rem': 'max-h-56',\n        '15rem': 'max-h-60',\n        '16rem': 'max-h-64',\n        '18rem': 'max-h-72',\n        '20rem': 'max-h-80',\n        '24rem': 'max-h-96',\n    },\n    'font-family': {\n        'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"':\n            'font-sans',\n        'ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif': 'font-serif',\n        'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace':\n            'font-mono',\n    },\n    'font-weight': {\n        '100': 'font-thin',\n        '200': 'font-extralight',\n        '300': 'font-light',\n        '400': 'font-normal',\n        '500': 'font-medium',\n        '600': 'font-semibold',\n        '700': 'font-bold',\n        '800': 'font-extrabold',\n        '900': 'font-black',\n        normal: 'font-normal',\n        bold: 'font-bold',\n    },\n    'line-height': {\n        '1': 'leading-none',\n        '2': 'leading-loose',\n        '.75rem': 'leading-3',\n        '1rem': 'leading-4',\n        '1.25rem': 'leading-5',\n        '1.5rem': 'leading-6',\n        '1.75rem': 'leading-7',\n        '2rem': 'leading-8',\n        '2.25rem': 'leading-9',\n        '2.5rem': 'leading-10',\n        '1.25': 'leading-tight',\n        '1.375': 'leading-snug',\n        '1.5': 'leading-normal',\n        '1.625': 'leading-relaxed',\n    },\n    'border-width': {\n        '0px': 'border-0',\n        '2px': 'border-2',\n        '4px': 'border-4',\n        '8px': 'border-8',\n        '1px': 'border',\n    },\n    'border-top-width': {\n        '0px': 'border-t-0',\n        '2px': 'border-t-2',\n        '4px': 'border-t-4',\n        '8px': 'border-t-8',\n        '1px': 'border-t',\n    },\n    'border-right-width': {\n        '0px': 'border-r-0',\n        '2px': 'border-r-2',\n        '4px': 'border-r-4',\n        '8px': 'border-r-8',\n        '1px': 'border-r',\n    },\n    'border-bottom-width': {\n        '0px': 'border-b-0',\n        '2px': 'border-b-2',\n        '4px': 'border-b-4',\n        '8px': 'border-b-8',\n        '1px': 'border-b',\n    },\n    'border-left-width': {\n        '0px': 'border-l-0',\n        '2px': 'border-l-2',\n        '4px': 'border-l-4',\n        '8px': 'border-l-8',\n        '1px': 'border-l',\n    },\n    transition: {\n        'all 150ms cubic-bezier(0.4, 0, 0.2, 1)': 'transition-all',\n        'background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter 150ms cubic-bezier(0.4, 0, 0.2, 1)':\n            'transition',\n        'background-color, border-color, color, fill, stroke 150ms cubic-bezier(0.4, 0, 0.2, 1)':\n            'transition-colors',\n        'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)': 'transition-opacity',\n        'box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)': 'transition-shadow',\n        'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)': 'transition-transform',\n    },\n};\n\nconst getResultCode = (it: CssCodeParse, prefix = '', config: TranslatorConfig) => {\n    if (typeof it.cssCode !== 'string') {\n        return null;\n    }\n    const cssCodeList = it.cssCode.split(';').filter((v) => v !== '');\n    const resultVals = cssCodeList\n        .map((v) => {\n            let key = '';\n            let val = '';\n            for (let i = 0; i < v.length; i++) {\n                const c = v[i];\n                if (c !== ':') {\n                    key += c;\n                } else {\n                    val = v.slice(i + 1, v.length).trim();\n                    break;\n                }\n            }\n            const pipe = propertyMap.get(key.trim());\n            let hasImportant = false;\n            if (val.includes('!important')) {\n                val = val.replace('!important', '').trim();\n                hasImportant = true;\n            }\n            let pipeVal = '';\n            if (val === 'initial' || val === 'inherit') {\n                pipeVal = `[${key.trim()}:${val}]`;\n            } else {\n                config.customTheme = config.customTheme ?? {};\n                // Handle all font-family values without square brackets\n                if (key.trim() === 'font-family') {\n                    pipeVal = `font-${val}`;\n                } else {\n                    pipeVal =\n                        typeof pipe === 'function'\n                            ? config.customTheme[key.trim()]?.[val] ||\n                              (config.useAllDefaultValues &&\n                                  moreDefaultValuesMap[key.trim()]?.[val]) ||\n                              pipe(val)\n                            : config.customTheme[key.trim()]?.[val] ||\n                              (config.useAllDefaultValues &&\n                                  moreDefaultValuesMap[key.trim()]?.[val]) ||\n                              (pipe?.[val] ?? '');\n                }\n            }\n            if ((config.prefix?.length ?? 0) > 0) {\n                pipeVal = pipeVal\n                    .split(' ')\n                    .map((v) => `${v[0] === '-' ? '-' : ''}${config.prefix}${v.replace(/^-/, '')}`)\n                    .join(' ');\n            }\n            if (hasImportant) {\n                const getImportantVal = (v: string) => {\n                    if (v[0] === '[' && v[v.length - 1] === ']') {\n                        v = `${v.slice(0, -1)}!important]`;\n                    } else {\n                        v = `!${v}`;\n                    }\n                    return v;\n                };\n                if (\n                    pipeVal.includes(' ') &&\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length === 0\n                ) {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => getImportantVal(v))\n                        .join(' ');\n                } else if (pipeVal.length > 0) {\n                    pipeVal = getImportantVal(pipeVal);\n                }\n            }\n            if (it.selectorName.endsWith(':hover') && pipeVal.length > 0) {\n                if (\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length > 0\n                ) {\n                    pipeVal = `hover:${pipeVal}`;\n                } else {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => `hover:${v}`)\n                        .join(' ');\n                }\n            } else if (it.selectorName.endsWith(':focus') && pipeVal.length > 0) {\n                if (\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length > 0\n                ) {\n                    pipeVal = `focus:${pipeVal}`;\n                } else {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => `focus:${v}`)\n                        .join(' ');\n                }\n            } else if (it.selectorName.endsWith(':active') && pipeVal.length > 0) {\n                if (\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length > 0\n                ) {\n                    pipeVal = `active:${pipeVal}`;\n                } else {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => `active:${v}`)\n                        .join(' ');\n                }\n            } else if (it.selectorName.endsWith('::before') && pipeVal.length > 0) {\n                if (\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length > 0\n                ) {\n                    pipeVal = `before:${pipeVal}`;\n                } else {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => `before:${v}`)\n                        .join(' ');\n                }\n            } else if (it.selectorName.endsWith('::after') && pipeVal.length > 0) {\n                if (\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length > 0\n                ) {\n                    pipeVal = `after:${pipeVal}`;\n                } else {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => `after:${v}`)\n                        .join(' ');\n                }\n            }\n            if (prefix.length > 0) {\n                if (\n                    ['backdrop-filter', 'filter', 'transform'].filter((v) => pipeVal.startsWith(v))\n                        .length > 0\n                ) {\n                    pipeVal = `${prefix}:${pipeVal}`;\n                } else {\n                    pipeVal = pipeVal\n                        .split(' ')\n                        .map((v) => `${prefix}:${v}`)\n                        .join(' ');\n                }\n            }\n            return pipeVal;\n        })\n        .filter((v) => v !== '');\n    return {\n        selectorName: it.selectorName,\n        resultVal: [...new Set(resultVals)].join(' '),\n    };\n};\n\nexport interface CustomTheme extends Record<string, undefined | Record<string, string>> {\n    media?: Record<string, string>;\n    'backdrop-blur'?: Record<string, string>;\n    'backdrop-brightness'?: Record<string, string>;\n    'backdrop-contrast'?: Record<string, string>;\n    'backdrop-grayscale'?: Record<string, string>;\n    'backdrop-hue-rotate'?: Record<string, string>;\n    'backdrop-invert'?: Record<string, string>;\n    'backdrop-opacity'?: Record<string, string>;\n    'backdrop-saturate'?: Record<string, string>;\n    'backdrop-sepia'?: Record<string, string>;\n    blur?: Record<string, string>;\n    brightness?: Record<string, string>;\n    contrast?: Record<string, string>;\n    grayscale?: Record<string, string>;\n    'hue-rotate'?: Record<string, string>;\n    invert?: Record<string, string>;\n    saturate?: Record<string, string>;\n    sepia?: Record<string, string>;\n    scale?: Record<string, string>;\n    rotate?: Record<string, string>;\n    translate?: Record<string, string>;\n    skew?: Record<string, string>;\n}\n\nexport interface TranslatorConfig {\n    prefix?: string;\n    /**\n     * @default true\n     */\n    useAllDefaultValues?: boolean;\n    customTheme?: CustomTheme;\n}\n\nexport const defaultTranslatorConfig = {\n    prefix: '',\n    useAllDefaultValues: true,\n    customTheme: {},\n};\n\nexport const CssToTailwindTranslator = (\n    code: string,\n    config: TranslatorConfig = defaultTranslatorConfig,\n): {\n    code: 'SyntaxError' | 'OK';\n    data: ResultCode[];\n} => {\n    if (specialAttribute.map((v) => code.includes(v)).filter((v) => v).length > 0) {\n        return {\n            code: 'SyntaxError',\n            data: [],\n        };\n    }\n    useAllDefaultValues = config.useAllDefaultValues ?? defaultTranslatorConfig.useAllDefaultValues;\n    customTheme = config.customTheme ?? defaultTranslatorConfig.customTheme;\n    const dataArray: ResultCode[] = [];\n    parsingCode(code)\n        .map((it) => {\n            if (typeof it.cssCode === 'string') {\n                return getResultCode(it, '', config);\n            } else if (it.selectorName.includes('@media')) {\n                return it.cssCode.map((v) => {\n                    const mediaName = getCustomVal(\n                        it.selectorName\n                            .replace(/\\(.+\\)/g, (v) => v.replace(/\\s/g, ''))\n                            .replace(/\\s+\\(/g, '('),\n                    );\n                    const res = getResultCode(\n                        v,\n                        customTheme.media?.[it.selectorName] ||\n                            (config.useAllDefaultValues && moreDefaultMediaVals[mediaName]) ||\n                            `[${mediaName}]`,\n                        config,\n                    );\n                    return res\n                        ? {\n                              selectorName: `${it.selectorName}-->${res.selectorName}`,\n                              resultVal: res.resultVal,\n                          }\n                        : null;\n                });\n            } else {\n                return null;\n            }\n        })\n        .filter((v) => v !== null)\n        .forEach((v) => {\n            if (Array.isArray(v)) {\n                dataArray.push(...(v as ResultCode[]));\n            } else {\n                dataArray.push(v as ResultCode);\n            }\n        });\n    return {\n        code: 'OK',\n        data: dataArray,\n    };\n};\n"], "names": [], "mappings": "AAAA,cAAc;;;;;;;AAOP,MAAM,mBAAmB;IAAC;IAAY;IAAc;IAAW;CAAa;AAEnF,IAAI,sBAAsB;AAC1B,IAAI,cAA2B,CAAC;AAEhC,MAAM,cAAc,CAAC,MAAoC;QACrD,GAAG,CAAC,EAAE,KAAK,MAAM,MAAM;QACvB,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK;KACnC;AACD,MAAM,eAAe,CAAC;IAClB,MAAM,IAAI,OAAO,CAAC,OAAO;IACzB,IAAK,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,EAAE,MAAO;QAC3C,MAAM,OAAO,GAAG,CAAC,MAAM;QACvB,IAAI,SAAS,OAAO,SAAS,GAAG,CAAC,QAAQ,EAAE,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS,IAAI,KAAK,CAAC,QAAQ;QAClD,OAAO;YACH;QACJ;IACJ;IACA,OAAO;AACX;AAEA,MAAM,UAAU,CAAC,KAAa,qBAAqB,KAAK;IACpD,MAAM,cAAc;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM,SACF;IACJ,OACI,OAAO,IAAI,CAAC,QACZ,YAAY,QAAQ,CAAC,QACpB,sBAAsB,sCAAsC,IAAI,CAAC;AAE1E;AAEA,MAAM,SAAS,CAAC;IACZ,IAAI,IAAI,MAAM,KAAK,GAAG;QAClB,OAAO;IACX;IACA,OACI;QACI;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,aAAa,QACpC,YAAY,IAAI,CAAC,IAAI,IAAI,OACzB,cAAc,IAAI,CAAC;AAE3B;AAEA,IAAA,AAAK,sCAAA;;;;WAAA;EAAA;AAML,MAAM,2BAA2B,CAC7B,KACA,WAA2B,EAAE;IAE7B,IAAI,oBAAoB,IAAI,CAAC,MAAM;QAC/B,MAAM,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,IACzB,OAAO,CAAC,GACR,OAAO,CAAC,mBAAmB,MAAM,CAAC,CAAC;IAC5C;IACA,MAAM,SAAiC;QACnC,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,SAAS;QACT,SAAS;QACT,eAAe;QACf,eAAe;IACnB;IACA,SAAS,OAAO,CAAC,CAAC;QACd,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,OAAO,MAAM,CAAC,IAAI;AACtB;AAEA,MAAM,mBAAmB,CAAC;IACtB,OAAO,CAAA;QACH,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;IACb,CAAA,CAAC,CAAC,IAAI;AACV;AAEA,MAAM,4BAA4B,CAAC;IAC/B,OAAO,CAAA;QACH,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,UAAU;IACd,CAAA,CAAC,CAAC,IAAI;AACV;AAEA,MAAM,sBAAsB,CAAC;IACzB,OAAO,CAAA;QACH,WAAW;QACX,aAAa;QACb,aAAa;QACb,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,mBAAmB;QACnB,oBAAoB;QACpB,mBAAmB;QACnB,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QACf,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,2CAA2C;QAC3C,wFACI;QACJ,yFACI;QACJ,yFACI;QACJ,2FACI;QACJ,gDAAgD;QAChD,0BAA0B;QAC1B,gBAAgB;QAChB,gBAAgB;QAChB,uBAAuB;QACvB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,sBAAsB;QACtB,aAAa;QACb,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,YAAY;QACZ,YAAY;IAChB,CAAA,CAAC,CAAC,IAAI;AACV;AAEO,MAAM,cAGT,IAAI,IAAoF;IACxF;QACI;QACA;YACI,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,iBAAiB;YACjB,gBAAgB;YAChB,gBAAgB;QACpB;KACH;IACD;QACI;QACA;YACI,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,UAAU;YACV,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,UAAU;QACd;KACH;IACD;QACI;QACA;YACI,SAAS;YACT,SAAS;YACT,OAAO;QACX;KACH;IACD;QAAC;QAAa,CAAC,MAAQ,CAAC;gBAAE,MAAM;YAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC3F;QAAC;QAAmB,CAAC,MAAQ,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtE;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QAAC;QAAsB,CAAC,MAAQ,CAAC,oBAAoB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5E;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QAAC;QAA6B,CAAC,MAAQ,CAAC,2BAA2B,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1F;QAAC;QAAkB,CAAC,MAAQ,CAAC,gBAAgB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpE;QAAC;QAAwB,CAAC,MAAQ,CAAC,sBAAsB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChF;QAAC;QAA6B,CAAC,MAAQ,CAAC,2BAA2B,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1F;QACI;QACA,CAAC,MAAQ,CAAC;gBAAE,MAAM;YAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC,CAAC;KACrF;IACD;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QACI;QACA,CAAC;YACG,MAAM,aAAa;gBAAE,MAAM;YAAuB,CAAC,CAAC,IAAI;YACxD,IAAI,YAAY;gBACZ,OAAO;YACX;YAEA,MAAM,0BAAiE;gBACnE,MAAM,CAAC,IACH,CAAC,cAAc,EAAE,WAAW,CAAC,gBAAgB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACpE,YAAY,CAAC,IACT,CAAC,oBAAoB,EAAE,WAAW,CAAC,sBAAsB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAChF,UAAU,CAAC,IACP,CAAC,kBAAkB,EAAE,WAAW,CAAC,oBAAoB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC5E,WAAW,CAAC,IACR,CAAC,mBAAmB,EAAE,WAAW,CAAC,qBAAqB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC9E,cAAc,CAAC;oBACX,MAAM,IAAI,YAAY;oBACtB,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,oBAAoB,EAAE,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACnG;gBACA,QAAQ,CAAC,IACL,CAAC,gBAAgB,EAAE,WAAW,CAAC,kBAAkB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACxE,SAAS,CAAC,IACN,CAAC,iBAAiB,EAAE,WAAW,CAAC,mBAAmB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC1E,UAAU,CAAC,IACP,CAAC,kBAAkB,EAAE,WAAW,CAAC,oBAAoB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC5E,OAAO,CAAC,IACJ,CAAC,eAAe,EAAE,WAAW,CAAC,iBAAiB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;YAC1E;YACA,MAAM,OAAO,aAAa,KACrB,OAAO,CAAC,YAAY,CAAC,IAAM,EAAE,OAAO,CAAC,MAAM,KAC3C,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAExD,IAAI,SAAS;YACb,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC;gBAClB,IAAI,cAAc;gBAClB,IAAI,QAAQ;gBACZ,IAAI,qBAAqB;oBACrB,QACI,CAAC,oBAAoB,MACjB,CAAA;wBACI,cAAc;wBACd,iBAAiB;wBACjB,gBAAgB;wBAChB,gBAAgB;wBAChB,iBAAiB;wBACjB,gBAAgB;wBAChB,gBAAgB;wBAChB,gBAAgB;wBAChB,gBAAgB;wBAChB,gBAAgB;wBAChB,iBAAiB;wBACjB,gBAAgB;wBAChB,gBAAgB;wBAChB,iBAAiB;wBACjB,cAAc;oBAClB,CAAA,CAAC,CAAC,EAAE,KACR;oBACJ,IAAI,MAAM,MAAM,GAAG,GAAG;wBAClB,QAAQ,MAAM,UAAU,CAAC,sBAAsB,QAAQ,CAAC,SAAS,EAAE,OAAO;wBAC1E,cAAc;oBAClB;gBACJ;gBACA,QACI,MAAM,MAAM,GAAG,IACT,QACA,EAAE,OAAO,CAAC,+BAA+B,CAAC,GAAG,GAAW;oBACpD,cAAc;oBACd,OAAO,uBAAuB,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,KAAK;gBAC7D;gBACV,OAAO,cAAc,QAAQ;YACjC;YACA,OAAO,SACD,CAAC,gBAAgB,EAAE;mBAAI,IAAI,IAAI;aAAK,CAAC,IAAI,CAAC,MAAM,GAChD,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,CAAC;QAClD;KACH;IACD;QACI;QACA;YACI,SAAS;YACT,QAAQ;QACZ;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,cAAsC;gBACxC,GAAG,YAAY,GAAG,CAAC,wBAAwB;gBAC3C,GAAG,YAAY,GAAG,CAAC,oBAAoB;gBACvC,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,OAAO;gBACP,gBAAgB;gBAChB,aAAa;gBACb,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS;YACb;YACA,OAAO,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,aAAa,KAAK,CAAC,CAAC;QAC1D;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,OAAO;YACP,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,eAAe;YACf,cAAc;YACd,cAAc;YACd,cAAc;YACd,YAAY;YACZ,WAAW;YACX,KAAK;YACL,YAAY;YACZ,OAAO;YACP,YAAY;QAChB;KACH;IACD;QACI;QACA;YACI,cAAc;YACd,eAAe;YACf,eAAe;YACf,MAAM;QACV;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,CAAC;gBACG,aAAa;gBACb,cAAc;gBACd,cAAc;YAClB,CAAC,CAAC,CAAC,IAAI,IACP,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,GAAG,QAAQ,KAAK,QAAQ,CAAC,IAAI,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG,EAAE;KACtF;IACD;QAAC;QAAoB,CAAC,MAAQ,CAAC;gBAAE,MAAM;YAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxF;QACI;QACA;YACI,cAAc;YACd,eAAe;YACf,eAAe;QACnB;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,eAAe;gBACf,YAAY;gBACZ,OAAO;gBACP,gBAAgB;gBAChB,aAAa;gBACb,KAAK;YACT,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,aAAa,KAAK,CAAC,CAAC;KAC7C;IACD;QACI;QACA;YACI,QAAQ;YACR,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,OAAO;QACX;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,MAAM;gBACN,OAAO;gBACP,SAAS;YACb,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,CAAC;KAC1D;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,OAAO,CAAC,WAAW,CAAC,IAAM,EAAE,OAAO,CAAC,OAAO;YACrD,MAAM,OAAe,IAChB,KAAK,CAAC,KACN,MAAM,CAAC,CAAC,IAAM,MAAM,IACpB,GAAG,CAAC,CAAC,IACF,OAAO,MAAM,QAAQ,KACd,CAAA;oBACG,aAAa;oBACb,cAAc;oBACd,cAAc;gBAClB,CAAA,CAAC,CAAC,IAAI,IACN,AAAC,YAAY,GAAG,CAAC,eAA0C,CAAC,EAAE,IAC9D,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,GACd,AAAC,YAAY,GAAG,CAAC,eAA0C,CAAC,EAAE,IAAI,IAE5E,MAAM,CAAC,CAAC,IAAM,MAAM,IACpB,IAAI,CAAC;YACV,OAAO;QACX;KACH;IACD;QACI;QACA,CAAC;YACG,OAAO,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;QACjD;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,GAC9B,QAAQ,KAAK,QACX,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC,GAC5C;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAmB,OAAO;YAAkB,CAAC,CAAC,CAAC,IAAI,IAC3D,CAAC,OAAO,OACF,CAAC,UAAU,EAAE,CAAC,AAAC,uBAAuB,0BAA0B,QAAS,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,KAAK,GAC1H,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAmB,OAAO;YAAkB,CAAC,CAAC,CAAC,IAAI,IAC3D,CAAC,OAAO,OACF,CAAC,UAAU,EAAE,CAAC,AAAC,uBAAuB,0BAA0B,QAAS,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,KAAK,GAC1H,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,AAAC,YAAY,GAAG,CAAC,eAA0C,CAAC,IAAI,GAC1D,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,GAC9B;KACb;IACD;QAAC;QAAuB,CAAC,MAAS,OAAO,OAAO,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KAAI;IACxF;QACI;QACA;YACI,UAAU;YACV,UAAU;QACd;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,CAAC;gBACG,aAAa;gBACb,cAAc;gBACd,cAAc;YAClB,CAAC,CAAC,CAAC,IAAI,IACP,CAAC,WACK,CAAC,OAAO,EAAE,KAAK,GACf,QAAQ,KAAK,QACX,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC,GAC/B,EAAE;KACjB;IACD;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QAAC;QAAsB,CAAC,MAAQ,CAAC,oBAAoB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5E;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QACI;QACA,CAAC,MAAS,OAAO,OAAO,CAAC,oBAAoB,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KACzE;IACD;QACI;QACA,CAAC;YACG,OAAO,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;QAC/C;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAC5B,QAAQ,KAAK,QACX,CAAC,mBAAmB,EAAE,aAAa,KAAK,CAAC,CAAC,GAC1C;KACf;IACD;QACI;QACA,CAAC,MACG,AAAC,YAAY,GAAG,CAAC,eAA0C,CAAC,IAAI,GAC1D,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAC5B;KACb;IACD;QAAC;QAAqB,CAAC,MAAS,OAAO,OAAO,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KAAI;IACtF;QACI;QACA,CAAC;YACG,MAAM,IAAI;gBAAE,KAAK;gBAAgB,OAAO;YAAe,CAAC,CAAC,IAAI;YAC7D,IAAI,GAAG;gBACH,OAAO;YACX;YACA,IAAI,IAAI,QAAQ,CAAC,MAAM;gBACnB,OAAO,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;YAC3C;YACA,IAAI,OAAO,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;YAC9C,IAAI,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;gBAC3C,OAAO;YACX;YACA,OAAO,KAAK,GAAG,CAAC,CAAC,IACb,CAAC,AAAC,uBAAuB,0BAA0B,MAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,OAAO,CACxE,SACA;YAGR,IAAI,KAAK,MAAM,KAAK,GAAG;gBACnB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAC9B,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;gBAC1B,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;YAChG,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;gBAC1B,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;YAChG,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;gBAC1B,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;YAChG;YACA,OAAO;QACX;KACH;IACD;QACI;QACA,CAAC;YACG,OAAO,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;QAChD;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,GAC7B,QAAQ,KAAK,QACX,CAAC,oBAAoB,EAAE,aAAa,KAAK,CAAC,CAAC,GAC3C;KACf;IACD;QACI;QACA,CAAC,MACG,AAAC,YAAY,GAAG,CAAC,eAA0C,CAAC,IAAI,GAC1D,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,GAC7B;KACb;IACD;QAAC;QAAsB,CAAC,MAAS,OAAO,OAAO,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KAAI;IACvF;QAAC;QAAkB,CAAC,MAAS,OAAO,OAAO,CAAC,gBAAgB,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KAAI;IACzF;QACI;QACA;YACI,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,MAAM;QACV;KACH;IACD;QACI;QACA,CAAC;YACG,OAAO,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC,CAAC;QAC9C;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,GAC3B,QAAQ,KAAK,QACX,CAAC,kBAAkB,EAAE,aAAa,KAAK,CAAC,CAAC,GACzC;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAmB,OAAO;YAAkB,CAAC,CAAC,CAAC,IAAI,IAC3D,CAAC,OAAO,OACF,CAAC,UAAU,EAAE,CAAC,AAAC,uBAAuB,0BAA0B,QAAS,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,KAAK,GAC1H,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAmB,OAAO;YAAkB,CAAC,CAAC,CAAC,IAAI,IAC3D,CAAC,OAAO,OACF,CAAC,UAAU,EAAE,CAAC,AAAC,uBAAuB,0BAA0B,QAAS,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,KAAK,GAC1H,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,AAAC,YAAY,GAAG,CAAC,eAA0C,CAAC,IAAI,GAC1D,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,GAC3B;KACb;IACD;QAAC;QAAoB,CAAC,MAAS,OAAO,OAAO,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KAAI;IACrF;QAAC;QAAgB,CAAC,MAAS,OAAO,OAAO,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG;KAAI;IAC/E;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OAAO,OAAO,OACR,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAAC,EAAE,EAAE;;;aAAkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GACpG;QACV;KACH;IACD;QACI;QACA;YACI,SAAS;YACT,OAAO;YACP,KAAK;YACL,QAAQ;YACR,UAAU;YACV,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,OAAO;QACX;KACH;IACD;QACI;QACA;YACI,SAAS;YACT,QAAQ;YACR,SAAS;YACT,SAAS;QACb;KACH;IACD;QAAC;QAAY,CAAC,MAAQ,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxD;QAAC;QAAkB,CAAC,MAAQ,CAAC,gBAAgB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpE;QACI;QACA;YACI,QAAQ;YACR,UAAU;YACV,SAAS;QACb;KACH;IACD;QAAC;QAAqB,CAAC,MAAQ,CAAC,mBAAmB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1E;QACI;QACA;YACI,YAAY;YACZ,UAAU;YACV,eAAe;YACf,cAAc;YACd,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;YACT,SAAS;QACb;KACH;IACD;QAAC;QAAc,CAAC,MAAQ,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5D;QACI;QACA;YACI,cAAc;YACd,eAAe;QACnB;KACH;IACD;QACI;QACA;YACI,KAAK;YACL,QAAQ;YACR,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACV;KACH;IACD;QAAC;QAAQ,CAAC,MAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChD;QAAC;QAAa,CAAC,MAAQ,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1D;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,CAAC;gBACG,aAAa;gBACb,cAAc;gBACd,cAAc;YAClB,CAAC,CAAC,CAAC,IAAI,IACP,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,QAAQ,KAAK,QAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG,EAAE;KAC1F;IACD;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QACI;QACA;YACI,SAAS;YACT,MAAM;YACN,SAAS;QACb;KACH;IACD;QAAC;QAAc,CAAC,MAAQ,CAAC;gBAAE,KAAK;YAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAAE;IAC3F;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAC5B,QAAQ,KAAK,QACX,CAAC,mBAAmB,EAAE,aAAa,KAAK,CAAC,CAAC,GAC1C;KACf;IACD;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;QACb;KACH;IACD;QAAC;QAAqB,CAAC,MAAS,OAAO,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACjF;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QAAC;QAAgB,CAAC,MAAS,OAAO,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACvE;QAAC;QAAW,CAAC,MAAQ,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtD;QAAC;QAA0B,CAAC,MAAQ,CAAC,wBAAwB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpF;QAAC;QAAW,CAAC,MAAQ,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtD;QAAC;QAAsB,CAAC,MAAQ,CAAC,oBAAoB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5E;QAAC;QAAqB,CAAC,MAAQ,CAAC,mBAAmB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1E;QAAC;QAAiB,CAAC,MAAQ,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAClE;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QACI;QACA;YACI,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,eAAe;QACnB;KACH;IACD;QACI;QACA;YACI,KAAK;YACL,KAAK;YACL,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,gBAAgB;YAChB,QAAQ;YACR,MAAM;YACN,eAAe;YACf,OAAO;YACP,gBAAgB;YAChB,iBAAiB;YACjB,cAAc;YACd,gBAAgB;YAChB,sBAAsB;YACtB,sBAAsB;YACtB,sBAAsB;YACtB,mBAAmB;YACnB,aAAa;YACb,aAAa;YACb,MAAM;YACN,eAAe;YACf,UAAU;YACV,aAAa;YACb,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,CAAC;gBAAE,cAAc;gBAAgB,cAAc;YAAe,CAAC,CAAC,CAAC,IAAI,IACrE,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,QAAQ,KAAK,QAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG,EAAE;KAC1F;IACD;QACI;QACA,CAAC;YACG,MAAM,aAAa;gBAAE,MAAM;YAAc,CAAC,CAAC,IAAI;YAC/C,IAAI,YAAY;gBACZ,OAAO;YACX;YACA,MAAM,kBAAyD;gBAC3D,MAAM,CAAC,IAAc,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACnE,YAAY,CAAC,IACT,CAAC,WAAW,EAAE,WAAW,CAAC,aAAa,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC9D,UAAU,CAAC,IAAc,CAAC,SAAS,EAAE,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC/E,WAAW,CAAC,IAAc,CAAC,UAAU,EAAE,WAAW,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAClF,cAAc,CAAC;oBACX,MAAM,IAAI,YAAY;oBACtB,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACjF;gBACA,QAAQ,CAAC,IAAc,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACzE,UAAU,CAAC,IAAc,CAAC,SAAS,EAAE,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC/E,OAAO,CAAC,IAAc,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;YAC1E;YACA,MAAM,OAAO,aAAa,KACrB,OAAO,CAAC,YAAY,CAAC,IAAM,EAAE,OAAO,CAAC,MAAM,KAC3C,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAExD,IAAI,SAAS;YACb,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC;gBAClB,IAAI,cAAc;gBAClB,IAAI,QAAQ;gBACZ,IAAI,qBAAqB;oBACrB,QAAQ,oBAAoB,MAAM;oBAClC,IAAI,MAAM,MAAM,GAAG,GAAG;wBAClB,cAAc;oBAClB;gBACJ;gBACA,QACI,MAAM,MAAM,GAAG,IACT,QACA,EAAE,OAAO,CAAC,+BAA+B,CAAC,GAAG,GAAW;oBACpD,cAAc;oBACd,OAAO,eAAe,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,KAAK;gBACrD;gBACV,OAAO,cAAc,QAAQ;YACjC;YACA,OAAO,SACD,CAAC,OAAO,EAAE;mBAAI,IAAI,IAAI;aAAK,CAAC,IAAI,CAAC,MAAM,GACvC,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC;QACzC;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAC/C;IACD;QAAC;QAAc,CAAC,MAAS,OAAO,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACnE;QACI;QACA;YACI,KAAK;YACL,eAAe;YACf,QAAQ;YACR,kBAAkB;QACtB;KACH;IACD;QAAC;QAAa,CAAC,MAAQ,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1D;QACI;QACA,CAAC,MACG,OAAO,OACA,CAAA;gBAAE,KAAK;gBAAe,KAAK;YAAY,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,GACtE;KACb;IACD;QACI;QACA,CAAC,MACG,OAAO,OACA,CAAA;gBAAE,KAAK;gBAAiB,KAAK;YAAc,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,GAC5E;KACb;IACD;QACI;QACA;YACI,MAAM;YACN,gBAAgB;YAChB,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,MAAM;YACN,MAAM;QACV;KACH;IACD;QAAC;QAAQ,CAAC,MAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChD;QAAC;QAAe,CAAC,MAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACvD;QAAC;QAAa,CAAC,MAAS,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC5D;QAAC;QAAoB,CAAC,MAAS,OAAO,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC/E;QACI;QACA;YACI,aAAa;YACb,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,WAAW;YACX,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,UAAU;YACV,mBAAmB;YACnB,mBAAmB;YACnB,WAAW;YACX,kBAAkB;YAClB,QAAQ;YACR,iBAAiB;YACjB,UAAU;YACV,kBAAkB;YAClB,kBAAkB;YAClB,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,cAAc;YACd,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;QACzB;KACH;IACD;QAAC;QAA2B,CAAC,MAAQ,CAAC,yBAAyB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtF;QAAC;QAAe,CAAC,MAAS,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC9D;QAAC;QAAO,CAAC,MAAQ,CAAC;gBAAE,KAAK;YAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAAE;IAChF;QAAC;QAAQ,CAAC,MAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChD;QAAC;QAAa,CAAC,MAAQ,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1D;QACI;QACA,CAAC,MACG,CAAC;gBACG,MAAM;gBACN,eAAe;gBACf,eAAe;gBACf,kBAAkB;YACtB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KACpD;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,QAAQ;gBACR,WAAW;gBACX,cAAc;YAClB,CAAC,CAAC,CAAC,aAAa,KAAK,IAAI;KAChC;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,MAAM;gBACN,eAAe;gBACf,eAAe;gBACf,kBAAkB;YACtB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KACpD;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,MAAM;gBACN,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,qBAAqB;gBACrB,qBAAqB;gBACrB,qBAAqB;gBACrB,UAAU;YACd,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,aAAa,KAAK,CAAC,CAAC;KAC9C;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;KAClD;IACD;QACI;QACA,CAAC,MAAQ,CAAC;gBAAE,KAAK;YAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAC7E;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KACpD;IACD;QAAC;QAAY,CAAC,MAAQ,CAAC;gBAAE,KAAK;YAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAAE;IACrF;QACI;QACA,CAAC,MACG,CAAC;gBACG,MAAM;gBACN,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,mBAAmB;gBACnB,UAAU;YACd,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,aAAa,KAAK,CAAC,CAAC;KAC9C;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;KAClD;IACD;QAAC;QAAgB,CAAC,MAAQ,CAAC;gBAAE,KAAK;YAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAAE;IAC7F;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KACpD;IACD;QAAC;QAAa,CAAC,MAAQ,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1D;QAAC;QAAiB,CAAC,MAAQ,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAClE;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QACI;QACA,CAAC,MACG,CAAC;gBACG,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,4BAA4B;gBAC5B,4BAA4B;gBAC5B,4BAA4B;gBAC5B,MAAM;YACV,CAAC,CAAC,CAAC,aAAa,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KACpF;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,2BAA2B;gBAC3B,MAAM;YACV,CAAC,CAAC,CAAC,aAAa,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KACpF;IACD;QACI;QACA;YACI,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;YACb,aAAa;YACb,SAAS;QACb;KACH;IACD;QACI;QACA,CAAC,MACG,OAAO,OACD,CAAC,EAAE,EAAE,AAAC,uBAAuB,iBAAiB,QAAS,yBAAyB,KAAK;;aAAiB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GACvH;KACb;IACD;QAAC;QAAQ,CAAC,MAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChD;QAAC;QAAqB,CAAC,MAAQ,CAAC,mBAAmB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1E;QACI;QACA;YACI,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,iBAAiB;YACjB,gBAAgB;YAChB,gBAAgB;QACpB;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;QACb;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OAAO,OAAO,OACR,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC,EAAE,EAAE;;;aAAkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAClG;QACV;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,WAAW;gBACX,YAAY;gBACZ,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,SAAS;YACb,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KACzD;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;gBACP,SAAS;YACb,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KACxD;IACD;QAAC;QAAc,CAAC,MAAQ,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5D;QAAC;QAAoB,CAAC,MAAQ,CAAC,kBAAkB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxE;QACI;QACA,CAAC,MACG,CAAC;gBACG,QAAQ;gBACR,SAAS;YACb,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAC9D;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,MAAM;gBACN,MAAM;gBACN,SAAS;YACb,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAC/C;IACD;QAAC;QAAkB,CAAC,MAAS,OAAO,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC3E;QAAC;QAAiB,CAAC,MAAS,OAAO,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACzE;QACI;QACA;YACI,SAAS;YACT,MAAM;QACV;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,aAAa,CAAC;gBAChB,MAAM,IAAI;oBAAE,KAAK;oBAAO,OAAO;oBAAO,MAAM;gBAAS,CAAC,CAAC,IAAI;gBAC3D,IAAI,GAAG;oBACH,OAAO;gBACX;gBACA,IAAI,OAAO,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;gBAC9C,IAAI,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;oBAC3C,OAAO;gBACX;gBACA,IAAI,qBAAqB;oBACrB,OAAO,KAAK,GAAG,CAAC,CAAC,IAAM,iBAAiB,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC1D,OAAO;oBACH,OAAO,KAAK,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACnC;gBACA,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,GAAG;oBAC/C,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;gBACzB,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;oBAC1B,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACxC,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;oBAC1B,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;wBACrB,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;oBACxC;oBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtD,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;oBAC1B,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;wBACrB,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;4BACrB,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACxC;wBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;oBACtD;oBACA,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;wBACrB,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;4BACrB,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACxC;wBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;oBACtD;oBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACpE;gBACA,OAAO;YACX;YACA,MAAM,IAAI,WAAW;YACrB,OAAO,MAAM,KACP,KACA,EACK,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IACF,EAAE,QAAQ,CAAC,OACL,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,MAAM,GAC1C,EAAE,OAAO,CAAC,KAAK,MAExB,IAAI,CAAC;QACpB;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OACI,CAAA;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAU,CAAA,CAAC,CAAC,IAAI,IACpD,CAAC,OAAO,OACF,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,CAAC,CAAC,EAAE,KAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAC7E,EAAE;QAEhB;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OACI,CAAA;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAU,CAAA,CAAC,CAAC,IAAI,IACpD,CAAC,OAAO,OACF,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,CAAC,CAAC,EAAE,KAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAC7E,EAAE;QAEhB;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OACI,CAAA;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAU,CAAA,CAAC,CAAC,IAAI,IACpD,CAAC,OAAO,OACF,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,CAAC,CAAC,EAAE,KAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAC7E,EAAE;QAEhB;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OACI,CAAA;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAU,CAAA,CAAC,CAAC,IAAI,IACpD,CAAC,OAAO,OACF,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,CAAC,CAAC,EAAE,KAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAC7E,EAAE;QAEhB;KACH;IACD;QAAC;QAAQ,CAAC,MAAQ,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChD;QAAC;QAAa,CAAC,MAAQ,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1D;QAAC;QAAkB,CAAC,MAAQ,CAAC,gBAAgB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpE;QAAC;QAAc,CAAC,MAAQ,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5D;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QAAC;QAAiB,CAAC,MAAQ,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAClE;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QAAC;QAAa,CAAC,MAAQ,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1D;QACI;QACA,CAAC,MACG,OAAO,OACA,CAAA;gBAAE,OAAO;gBAAW,QAAQ;gBAAc,SAAS;YAAe,CAAA,CAAC,CAAC,IAAI,IACzE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAChB;KACb;IACD;QACI;QACA,CAAC,MACG,OAAO,OACA,CAAA;gBACG,MAAM;gBACN,QAAQ;gBACR,eAAe;gBACf,eAAe;YACnB,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAC1B;KACb;IACD;QACI;QACA,CAAC,MACG,OAAO,OACA,CAAA;gBAAE,OAAO;gBAAW,QAAQ;gBAAc,SAAS;YAAe,CAAA,CAAC,CAAC,IAAI,IACzE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAChB;KACb;IACD;QACI;QACA,CAAC,MACG,OAAO,OACA,CAAA;gBACG,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,eAAe;YACnB,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAC1B;KACb;IACD;QACI;QACA;YACI,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,eAAe;YACf,cAAc;YACd,cAAc;YACd,cAAc;YACd,YAAY;YACZ,WAAW;YACX,KAAK;YACL,YAAY;YACZ,OAAO;YACP,YAAY;QAChB;KACH;IACD;QAAC;QAAY,CAAC,MAAQ,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxD;QAAC;QAAa,CAAC,MAAS,OAAO,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACjE;QAAC;QAAY,CAAC,MAAS,OAAO,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC/D;QAAC;QAAa,CAAC,MAAS,OAAO,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACjE;QAAC;QAAU,CAAC,MAAS,OAAO,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC3D;QACI;QACA;YACI,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,cAAc;QAClB;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,cAAc;gBACd,WAAW;gBACX,KAAK;YACT,CAAC,CAAC,CAAC,aAAa,KAAK,IAAI;KAChC;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,QAAQ;YACZ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KACxD;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,SAAS;YACb,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KACtD;IACD;QAAC;QAAW,CAAC,MAAQ,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,QAAQ,EAAE,KAAK,GAChB,QAAQ,KAAK,QACX,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,CAAC,GAChC;KACf;IACD;QAAC;QAAkB,CAAC,MAAS,OAAO,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC3E;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,OAAO;YACP,QAAQ;QACZ;KACH;IACD;QAAC;QAAiB,CAAC,MAAS,OAAO,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACnE;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;QACZ;KACH;IACD;QAAC;QAAmB,CAAC,MAAQ,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtE;QACI;QACA,CAAC,MAAQ,CAAC;gBAAE,cAAc;YAAc,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;KAC5F;IACD;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,SAAS;YACT,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,SAAS;YACT,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,SAAS;YACT,MAAM;QACV;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI;gBAAE,KAAK;gBAAO,OAAO;YAAM,CAAC,CAAC,IAAI;YAC3C,IAAI,GAAG;gBACH,OAAO;YACX;YACA,IAAI,OAAO,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;YAC9C,IAAI,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;gBAC3C,OAAO;YACX;YACA,IAAI,qBAAqB;gBACrB,OAAO,KAAK,GAAG,CAAC,CAAC,IAAM,iBAAiB,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,OAAO;gBACH,OAAO,KAAK,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACnC;YACA,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,GAAG;gBAC/C,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;gBAC1B,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACxC,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;gBAC1B,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;oBACrB,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACxC;gBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACtD,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;gBAC1B,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;oBACrB,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;wBACrB,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;oBACxC;oBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtD;gBACA,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;oBACrB,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;wBACrB,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;oBACxC;oBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtD;gBACA,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACpE;YACA,OAAO;QACX;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAQ,OAAO;YAAO,CAAC,CAAC,CAAC,IAAI,IACrC,CAAC,OAAO,OACF,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,QAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GACpE,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAQ,OAAO;YAAO,CAAC,CAAC,CAAC,IAAI,IACrC,CAAC,OAAO,OACF,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,QAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GACpE,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAQ,OAAO;YAAO,CAAC,CAAC,CAAC,IAAI,IACrC,CAAC,OAAO,OACF,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,QAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GACpE,EAAE;KACf;IACD;QACI;QACA,CAAC,MACG,CAAC;gBAAE,KAAK;gBAAQ,OAAO;YAAO,CAAC,CAAC,CAAC,IAAI,IACrC,CAAC,OAAO,OACF,CAAC,GAAG,EAAE,AAAC,uBAAuB,iBAAiB,QAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GACpE,EAAE;KACf;IACD;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,OAAO;YACP,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,OAAO;YACP,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACb;KACH;IACD;QAAC;QAAe,CAAC,MAAS,OAAO,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACrE;QAAC;QAAsB,CAAC,MAAQ,CAAC,oBAAoB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC5E;QACI;QACA;YACI,QAAQ;YACR,OAAO;YACP,KAAK;YACL,iBAAiB;YACjB,gBAAgB;YAChB,gBAAgB;YAChB,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,OAAO;YACP,UAAU;YACV,UAAU;YACV,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,OAAO;YACP,KAAK;YACL,aAAa;YACb,UAAU;YACV,SAAS;QACb;KACH;IACD;QAAC;QAAU,CAAC,MAAQ,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpD;QACI;QACA;YACI,MAAM;YACN,UAAU;YACV,YAAY;YACZ,MAAM;QACV;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OAAO,OAAO,OACR,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC,EAAE,EAAE;;;aAAkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GACnG;QACV;KACH;IACD;QAAC;QAAU,CAAC,MAAQ,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpD;QAAC;QAAW,CAAC,MAAQ,CAAC;gBAAE,KAAK;YAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAAE;IACxF;QAAC;QAAqB,CAAC,MAAQ,CAAC,mBAAmB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC1E;QAAC;QAAoB,CAAC,MAAQ,CAAC,kBAAkB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxE;QAAC;QAAoB,CAAC,MAAQ,CAAC,kBAAkB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxE;QAAC;QAAmB,CAAC,MAAS,OAAO,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC7E;QAAC;QAAyB,CAAC,MAAQ,CAAC,uBAAuB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAClF;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QAAC;QAAiB,CAAC,MAAQ,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAClE;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,AAAC,CAAC;gBACE,cAAc;gBACd,cAAc;YAClB,CAAC,CAAC,CAAC,IAAI,IAAI,WACL,CAAC,OAAO,EAAE,KAAK,GACf,QAAQ,KAAK,QACX,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC,GAC/B;KACf;IACD;QAAC;QAAgB,CAAC,MAAS,OAAO,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACjE;QAAC;QAAY,CAAC,MAAS,OAAO,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IAC/D;QACI;QACA;YACI,MAAM;YACN,OAAO;QACX;KACH;IACD;QAAC;QAAU,CAAC,MAAQ,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpD;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QACI;QACA;YACI,QAAQ;YACR,KAAK;YACL,MAAM;YACN,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM;YACN,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,QAAQ;YACR,OAAO;YACP,SAAS;YACT,OAAO;YACP,KAAK;QACT;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,KAAK;YACL,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,WAAW;YACX,gBAAgB;YAChB,MAAM;QACV;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,GAChC,QAAQ,KAAK,QACX,CAAC,uBAAuB,EAAE,aAAa,KAAK,CAAC,CAAC,GAC9C;KACf;IACD;QACI;QACA;YACI,MAAM;YACN,WAAW;YACX,UAAU;YACV,gBAAgB;YAChB,SAAS;YACT,SAAS;QACb;KACH;IACD;QAAC;QAA4B,CAAC,MAAQ,CAAC,0BAA0B,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxF;QACI;QACA;YACI,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA,CAAC,KAAK,WAAW,KAAK,GAClB,WACM,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,GAC9B,QAAQ,KAAK,QACX,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC,GAC5C;KACf;IACD;QAAC;QAA0B,CAAC,MAAQ,CAAC,wBAAwB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACpF;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QAAC;QAAe,CAAC,MAAS,OAAO,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACrE;QACI;QACA;YACI,MAAM;YACN,MAAM;YACN,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YACjB,YAAY;YACZ,SAAS;YACT,SAAS;QACb;KACH;IACD;QAAC;QAAoB,CAAC,MAAQ,CAAC,kBAAkB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACxE;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QACI;QACA,CAAC,MACG,CAAC;gBACG,UAAU;gBACV,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC;KACxD;IACD;QAAC;QAAe,CAAC,MAAQ,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9D;QACI;QACA;YACI,WAAW;YACX,WAAW;YACX,YAAY;YACZ,MAAM;QACV;KACH;IACD;QAAC;QAAyB,CAAC,MAAQ,CAAC,uBAAuB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAClF;QAAC;QAA2B,CAAC,MAAQ,CAAC,yBAAyB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IACtF;QACI;QACA;YACI,QAAQ;YACR,MAAM;YACN,cAAc;YACd,UAAU;YACV,SAAS;QACb;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,YAAY;YACtB,OAAO,OAAO,OACR,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAC,EAAE,EAAE;;;aAAkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GACjG;QACV;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,aAAa;gBAAE,MAAM;YAAiB,CAAC,CAAC,IAAI;YAClD,IAAI,YAAY;gBACZ,OAAO;YACX;YAEA,MAAM,iBAAyC;gBAC3C,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,OAAO;YACX;YACA,MAAM,kBAA0C;gBAC5C,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,UAAU;YACd;YACA,MAAM,gBAAwC;gBAC1C,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,SAAS;YACb;YACA,MAAM,qBAA6C;gBAC/C,OAAO;gBACP,OAAO;gBACP,YAAY;gBACZ,WAAW;gBACX,YAAY;gBACZ,UAAU;gBACV,YAAY;gBACZ,WAAW;gBACX,YAAY;gBACZ,QAAQ;gBACR,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,QAAQ;YACZ;YACA,MAAM,qBAAwE;gBAC1E,OAAO,CAAC;oBACJ,MAAM,KAAK,EAAE,KAAK,CAAC;oBACnB,IAAI,GAAG,MAAM,KAAK,GAAG;wBACjB,OAAO;oBACX;oBACA,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,KAAK,GAAG;wBACpC,OAAO,CAAC,MAAM,EAAE,YAAY,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,uBAAuB,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;oBAClH;oBACA,OAAO,GACF,GAAG,CAAC,CAAC,GAAG;wBACL,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,YAAY,KAAK,EAAE,CAAC,EAAE,IAAK,uBAAuB,cAAc,CAAC,EAAE,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;oBAC/H,GACC,IAAI,CAAC;gBACd;gBACA,QAAQ,CAAC,IACL,CAAC,QAAQ,EAAE,YAAY,KAAK,EAAE,CAAC,EAAE,IAAK,uBAAuB,cAAc,CAAC,EAAE,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACjG,QAAQ,CAAC,IACL,CAAC,QAAQ,EAAE,YAAY,KAAK,EAAE,CAAC,EAAE,IAAK,uBAAuB,cAAc,CAAC,EAAE,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACjG,QAAQ,CAAC;oBACL,MAAM,KAAK,EAAE,KAAK,CAAC;oBACnB,IAAI,GAAG,MAAM,GAAG,GAAG;wBACf,IACI,GAAG,MAAM,KAAK,KACd;4BAAC;4BAAK;yBAAO,CAAC,SAAS,CAAC,CAAC,IAAM,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,KAC/C;4BAAC;4BAAK;yBAAO,CAAC,SAAS,CAAC,CAAC,IAAM,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,GACjD;4BACE,MAAM,IAAI,YAAY,EAAE,CAAC,EAAE;4BAC3B,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;wBACzH;wBACA,OAAO;oBACX;oBACA,MAAM,IAAI,YAAY,EAAE,CAAC,EAAE;oBAC3B,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACzH;gBACA,SAAS,CAAC;oBACN,MAAM,IAAI,YAAY;oBACtB,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACzH;gBACA,WAAW,CAAC;oBACR,MAAM,KAAK,EAAE,KAAK,CAAC;oBACnB,IAAI,GAAG,MAAM,KAAK,GAAG;wBACjB,OAAO;oBACX;oBACA,OAAO,GACF,GAAG,CAAC,CAAC,GAAG;wBACL,MAAM,IAAI,YAAY;wBACtB,IAAI,oBAAoB,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;4BAChC,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAC3B,OAAO,CAAC,GACR,OAAO,CAAC,mBAAmB,MAAM,CAAC,CAAC;wBAC5C;wBACA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,YAAY,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;oBAC3J,GACC,IAAI,CAAC;gBACd;gBACA,YAAY,CAAC;oBACT,MAAM,IAAI,YAAY;oBACtB,IAAI,oBAAoB,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;wBAChC,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAC3B,OAAO,CAAC,GACR,OAAO,CAAC,mBAAmB,MAAM,CAAC,CAAC;oBAC5C;oBACA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACpI;gBACA,YAAY,CAAC;oBACT,MAAM,IAAI,YAAY;oBACtB,IAAI,oBAAoB,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;wBAChC,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAC3B,OAAO,CAAC,GACR,OAAO,CAAC,mBAAmB,MAAM,CAAC,CAAC;oBAC5C;oBACA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACpI;gBACA,MAAM,CAAC;oBACH,MAAM,KAAK,EAAE,KAAK,CAAC;oBACnB,IAAI,GAAG,MAAM,KAAK,GAAG;wBACjB,OAAO;oBACX;oBACA,OAAO,GACF,GAAG,CAAC,CAAC,GAAG;wBACL,MAAM,IAAI,YAAY;wBACtB,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;oBAC5I,GACC,IAAI,CAAC;gBACd;gBACA,OAAO,CAAC;oBACJ,MAAM,IAAI,YAAY;oBACtB,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACrH;gBACA,OAAO,CAAC;oBACJ,MAAM,IAAI,YAAY;oBACtB,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,uBAAuB,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACrH;YACJ;YACA,MAAM,OAAO,aAAa,KACrB,OAAO,CAAC,YAAY,CAAC,IAAM,EAAE,OAAO,CAAC,MAAM,KAC3C,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAExD,IAAI,SAAS;YACb,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC;gBAClB,IAAI,cAAc;gBAClB,MAAM,QAAQ,EAAE,OAAO,CAAC,+BAA+B,CAAC,GAAG,GAAW;oBAClE,cAAc;oBACd,MAAM,SAAS,kBAAkB,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,KAAK;oBAC5D,OAAO,OAAO,WAAW,WAAW,SAAS;gBACjD;gBACA,OAAO,cAAc,QAAQ;YACjC;YACA,OAAO,SAAS,GAAG;mBAAI,IAAI,IAAI;aAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,EAAE,aAAa,KAAK,CAAC,CAAC;QACzF;KACH;IACD;QACI;QACA,CAAC,MACG,CAAC;gBACG,QAAQ;gBACR,KAAK;gBACL,WAAW;gBACX,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,aAAa;gBACb,MAAM;gBACN,UAAU;YACd,CAAC,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,CAAC;KAC/D;IACD;QACI;QACA;YACI,MAAM;YACN,eAAe;YACf,SAAS;QACb;KACH;IACD;QACI;QACA,CAAC;YACG,IAAI,QAAQ,QAAQ;gBAChB,OAAO;YACX;YACA,OAAO,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC,CAAC;QAC9C;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,OAAO,CACb,eACA,CAAC,GAAG,KAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;YAElE,OACI,CAAA;gBACI,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,UAAU;YACd,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG,EAAE;QAEtF;KACH;IACD;QACI;QACA,CAAC;YACG,MAAM,IAAI,OAAO,CACb,eACA,CAAC,GAAG,KAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;YAElE,OACI,CAAA;gBACI,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,UAAU;YACd,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG,EAAE;QAEzF;KACH;IACD;QAAC;QAAuB,CAAC,MAAQ,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAC9E;QACI;QACA,CAAC;YACG,MAAM,IAAI,OAAO,CAAC,OAAO;YACzB,OACI,CAAA;gBACI,QAAQ;gBACR,2BAA2B;gBAC3B,2BAA2B;gBAC3B,6BAA6B;gBAC7B,MAAM;gBACN,WAAW;gBACX,YAAY;gBACZ,eAAe;YACnB,CAAA,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,kBAAkB,CAAC,MAAM,EAAE,aAAa,KAAK,CAAC,CAAC,GAAG,EAAE;QAEtF;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,OAAO;YACP,iBAAiB;YACjB,SAAS;YACT,SAAS;QACb;KACH;IACD;QACI;QACA;YACI,MAAM;YACN,MAAM;YACN,KAAK;YACL,MAAM;QACV;KACH;IACD;QACI;QACA;YACI,UAAU;YACV,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,eAAe;QACnB;KACH;IACD;QACI;QACA;YACI,SAAS;YACT,QAAQ;QACZ;KACH;IACD;QACI;QACA;YACI,QAAQ;YACR,QAAQ;YACR,KAAK;YACL,YAAY;YACZ,YAAY;QAChB;KACH;IACD;QACI;QACA,CAAC,MACG,OAAO,OACD,CAAC,EAAE,EAAE,AAAC,uBAAuB,iBAAiB,QAAS,yBAAyB,KAAK;;aAAiB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GACvH;KACb;IACD;QACI;QACA;YACI,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;QACb;KACH;IACD;QAAC;QAAgB,CAAC,MAAS,OAAO,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,GAAG;KAAI;IACvE;QACI;QACA;YACI,QAAQ;YACR,cAAc;YACd,SAAS;QACb;KACH;IACD;QAAC;QAAgB,CAAC,MAAQ,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,CAAC;KAAC;IAChE;QACI;QACA,CAAC,MACG,CAAC;gBACG,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;YACV,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,QAAQ,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;KAC9D;CACJ;AAOD,MAAM,cAAc,CAAC;IACjB,OAAO,KAAK,OAAO,CAAC,WAAW,IAAI,IAAI;IACvC,MAAM,WAA2B,EAAE;IACnC,IAAI,QAAQ;IACZ,IAAI,iBAAiB;IACrB,IAAI,gBAAgB;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QAClC,MAAM,OAAO,IAAI,CAAC,EAAE;QACpB,IAAI;YAAC;YAAK;SAAI,CAAC,QAAQ,CAAC,OAAO;YAC3B,IAAI,SAAS,KAAK;gBACd,IAAI,oBAAoB,GAAG;oBACvB,iBAAiB;gBACrB,OAAO;oBACH,QAAQ,CAAC,MAAM,CAAC,iBAAiB,iBAAiB,UAAU,IAAI;gBACpE;YACJ,OAAO;gBACH,IAAI,EAAE,kBAAkB,GAAG;oBACvB,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC,OAAO;oBACvC,IAAI,OAAO,YAAY,YAAY,QAAQ,QAAQ,CAAC,MAAM;wBACtD,QAAQ,CAAC,MAAM,CAAC,OAAO,GAAG,YAAY;oBAC1C;oBACA;oBACA,iBAAiB;gBACrB,OAAO;oBACH,QAAQ,CAAC,MAAM,CAAC,iBAAiB,iBAAiB,UAAU,IAAI;gBACpE;YACJ;QACJ,OAAO;YACH,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAClB,QAAQ,CAAC,MAAM,GAAG;oBACd,cAAc;oBACd,SAAS;gBACb;YACJ;YACA,QAAQ,CAAC,MAAM,CAAC,iBAAiB,iBAAiB,UAAU,IAAI;QACpE;IACJ;IACA,OAAO,SAAS,GAAG,CAAC,CAAC,IAAM,CAAC;YACxB,cAAc,EAAE,YAAY,CAAC,IAAI;YACjC,SAAS,OAAO,EAAE,OAAO,KAAK,WAAW,EAAE,OAAO,CAAC,IAAI,KAAK,EAAE,OAAO;QACzE,CAAC;AACL;AAEA,MAAM,uBAA+C;IACjD,2BAA2B;IAC3B,2BAA2B;IAC3B,4BAA4B;IAC5B,4BAA4B;IAC5B,4BAA4B;IAC5B,uCAAuC;IACvC,uCAAuC;IACvC,wCAAwC;IACxC,wCAAwC;IACxC,wCAAwC;AAC5C;AAEA,MAAM,uBAA+D;IACjE,KAAK;QACD,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,cAAc;QACd,cAAc;QACd,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,eAAe;QACf,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,SAAS;IACb;IACA,QAAQ;QACJ,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,cAAc;QACd,cAAc;QACd,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,eAAe;QACf,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,SAAS;IACb;IACA,MAAM;QACF,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,cAAc;QACd,cAAc;QACd,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,eAAe;QACf,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,SAAS;IACb;IACA,OAAO;QACH,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,cAAc;QACd,cAAc;QACd,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;QACR,eAAe;QACf,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,SAAS;IACb;IACA,KAAK;QACD,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;IACb;IACA,cAAc;QACV,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;IACb;IACA,WAAW;QACP,OAAO;QACP,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;IACb;IACA,aAAa;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;IACd;IACA,cAAc;QACV,OAAO;QACP,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;IACb;IACA,eAAe;QACX,mNACI;QACJ,+DAA+D;QAC/D,sGACI;IACR;IACA,eAAe;QACX,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM;IACV;IACA,eAAe;QACX,KAAK;QACL,KAAK;QACL,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,UAAU;QACV,QAAQ;QACR,SAAS;QACT,OAAO;QACP,SAAS;IACb;IACA,gBAAgB;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,oBAAoB;QAChB,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,sBAAsB;QAClB,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,uBAAuB;QACnB,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,qBAAqB;QACjB,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACX;IACA,YAAY;QACR,0CAA0C;QAC1C,mJACI;QACJ,0FACI;QACJ,8CAA8C;QAC9C,iDAAiD;QACjD,gDAAgD;IACpD;AACJ;AAEA,MAAM,gBAAgB,CAAC,IAAkB,SAAS,EAAE,EAAE;IAClD,IAAI,OAAO,GAAG,OAAO,KAAK,UAAU;QAChC,OAAO;IACX;IACA,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;IAC9D,MAAM,aAAa,YACd,GAAG,CAAC,CAAC;QACF,IAAI,MAAM;QACV,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;YAC/B,MAAM,IAAI,CAAC,CAAC,EAAE;YACd,IAAI,MAAM,KAAK;gBACX,OAAO;YACX,OAAO;gBACH,MAAM,EAAE,KAAK,CAAC,IAAI,GAAG,EAAE,MAAM,EAAE,IAAI;gBACnC;YACJ;QACJ;QACA,MAAM,OAAO,YAAY,GAAG,CAAC,IAAI,IAAI;QACrC,IAAI,eAAe;QACnB,IAAI,IAAI,QAAQ,CAAC,eAAe;YAC5B,MAAM,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI;YACxC,eAAe;QACnB;QACA,IAAI,UAAU;QACd,IAAI,QAAQ,aAAa,QAAQ,WAAW;YACxC,UAAU,CAAC,CAAC,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QACtC,OAAO;YACH,OAAO,WAAW,GAAG,OAAO,WAAW,IAAI,CAAC;YAC5C,wDAAwD;YACxD,IAAI,IAAI,IAAI,OAAO,eAAe;gBAC9B,UAAU,CAAC,KAAK,EAAE,KAAK;YAC3B,OAAO;gBACH,UACI,OAAO,SAAS,aACV,OAAO,WAAW,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,IACpC,OAAO,mBAAmB,IACvB,oBAAoB,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,IAC3C,KAAK,OACL,OAAO,WAAW,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,IACpC,OAAO,mBAAmB,IACvB,oBAAoB,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,IAC3C,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;YAChC;QACJ;QACA,IAAI,CAAC,OAAO,MAAM,EAAE,UAAU,CAAC,IAAI,GAAG;YAClC,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,GAAG,CAAC,CAAC,EAAE,KAAK,MAAM,MAAM,KAAK,OAAO,MAAM,GAAG,EAAE,OAAO,CAAC,MAAM,KAAK,EAC7E,IAAI,CAAC;QACd;QACA,IAAI,cAAc;YACd,MAAM,kBAAkB,CAAC;gBACrB,IAAI,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,KAAK;oBACzC,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;gBACtC,OAAO;oBACH,IAAI,CAAC,CAAC,EAAE,GAAG;gBACf;gBACA,OAAO;YACX;YACA,IACI,QAAQ,QAAQ,CAAC,QACjB;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,KAAK,GAClB;gBACE,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,gBAAgB,IAC3B,IAAI,CAAC;YACd,OAAO,IAAI,QAAQ,MAAM,GAAG,GAAG;gBAC3B,UAAU,gBAAgB;YAC9B;QACJ;QACA,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,aAAa,QAAQ,MAAM,GAAG,GAAG;YAC1D,IACI;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,GAAG,GAChB;gBACE,UAAU,CAAC,MAAM,EAAE,SAAS;YAChC,OAAO;gBACH,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,MAAM,EAAE,GAAG,EACvB,IAAI,CAAC;YACd;QACJ,OAAO,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,aAAa,QAAQ,MAAM,GAAG,GAAG;YACjE,IACI;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,GAAG,GAChB;gBACE,UAAU,CAAC,MAAM,EAAE,SAAS;YAChC,OAAO;gBACH,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,MAAM,EAAE,GAAG,EACvB,IAAI,CAAC;YACd;QACJ,OAAO,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,cAAc,QAAQ,MAAM,GAAG,GAAG;YAClE,IACI;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,GAAG,GAChB;gBACE,UAAU,CAAC,OAAO,EAAE,SAAS;YACjC,OAAO;gBACH,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,OAAO,EAAE,GAAG,EACxB,IAAI,CAAC;YACd;QACJ,OAAO,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,eAAe,QAAQ,MAAM,GAAG,GAAG;YACnE,IACI;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,GAAG,GAChB;gBACE,UAAU,CAAC,OAAO,EAAE,SAAS;YACjC,OAAO;gBACH,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,OAAO,EAAE,GAAG,EACxB,IAAI,CAAC;YACd;QACJ,OAAO,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,cAAc,QAAQ,MAAM,GAAG,GAAG;YAClE,IACI;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,GAAG,GAChB;gBACE,UAAU,CAAC,MAAM,EAAE,SAAS;YAChC,OAAO;gBACH,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,MAAM,EAAE,GAAG,EACvB,IAAI,CAAC;YACd;QACJ;QACA,IAAI,OAAO,MAAM,GAAG,GAAG;YACnB,IACI;gBAAC;gBAAmB;gBAAU;aAAY,CAAC,MAAM,CAAC,CAAC,IAAM,QAAQ,UAAU,CAAC,IACvE,MAAM,GAAG,GAChB;gBACE,UAAU,GAAG,OAAO,CAAC,EAAE,SAAS;YACpC,OAAO;gBACH,UAAU,QACL,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,GAAG,OAAO,CAAC,EAAE,GAAG,EAC3B,IAAI,CAAC;YACd;QACJ;QACA,OAAO;IACX,GACC,MAAM,CAAC,CAAC,IAAM,MAAM;IACzB,OAAO;QACH,cAAc,GAAG,YAAY;QAC7B,WAAW;eAAI,IAAI,IAAI;SAAY,CAAC,IAAI,CAAC;IAC7C;AACJ;AAoCO,MAAM,0BAA0B;IACnC,QAAQ;IACR,qBAAqB;IACrB,aAAa,CAAC;AAClB;AAEO,MAAM,0BAA0B,CACnC,MACA,SAA2B,uBAAuB;IAKlD,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,KAAK,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC,IAAM,GAAG,MAAM,GAAG,GAAG;QAC3E,OAAO;YACH,MAAM;YACN,MAAM,EAAE;QACZ;IACJ;IACA,sBAAsB,OAAO,mBAAmB,IAAI,wBAAwB,mBAAmB;IAC/F,cAAc,OAAO,WAAW,IAAI,wBAAwB,WAAW;IACvE,MAAM,YAA0B,EAAE;IAClC,YAAY,MACP,GAAG,CAAC,CAAC;QACF,IAAI,OAAO,GAAG,OAAO,KAAK,UAAU;YAChC,OAAO,cAAc,IAAI,IAAI;QACjC,OAAO,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,WAAW;YAC3C,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;gBACnB,MAAM,YAAY,aACd,GAAG,YAAY,CACV,OAAO,CAAC,WAAW,CAAC,IAAM,EAAE,OAAO,CAAC,OAAO,KAC3C,OAAO,CAAC,UAAU;gBAE3B,MAAM,MAAM,cACR,GACA,YAAY,KAAK,EAAE,CAAC,GAAG,YAAY,CAAC,IAC/B,OAAO,mBAAmB,IAAI,oBAAoB,CAAC,UAAU,IAC9D,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EACpB;gBAEJ,OAAO,MACD;oBACI,cAAc,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,IAAI,YAAY,EAAE;oBACxD,WAAW,IAAI,SAAS;gBAC5B,IACA;YACV;QACJ,OAAO;YACH,OAAO;QACX;IACJ,GACC,MAAM,CAAC,CAAC,IAAM,MAAM,MACpB,OAAO,CAAC,CAAC;QACN,IAAI,MAAM,OAAO,CAAC,IAAI;YAClB,UAAU,IAAI,IAAK;QACvB,OAAO;YACH,UAAU,IAAI,CAAC;QACnB;IACJ;IACJ,OAAO;QACH,MAAM;QACN,MAAM;IACV;AACJ;KAzDa", "debugId": null}}, {"offset": {"line": 4878, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/time.ts"], "sourcesContent": ["export const timeAgo = (date: string): string => {\n    const now = new Date();\n    const then = new Date(date);\n    const diff = now.getTime() - then.getTime();\n    const diffYears = Math.floor(diff / (1000 * 60 * 60 * 24 * 30 * 12));\n\n    if (diffYears > 0) {\n        return `${diffYears}y`;\n    }\n\n    const diffMonths = Math.floor(diff / (1000 * 60 * 60 * 24 * 30));\n    if (diffMonths > 0) {\n        return `${diffMonths}m`;\n    }\n\n    const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24));\n    if (diffDays > 0) {\n        return `${diffDays}d`;\n    }\n\n    const diffHours = Math.floor(diff / (1000 * 60 * 60));\n    if (diffHours > 0) {\n        return `${diffHours}h`;\n    }\n\n    const diffMinutes = Math.floor(diff / (1000 * 60));\n    if (diffMinutes > 0) {\n        return `${diffMinutes}m`;\n    }\n    const diffSeconds = Math.floor(diff / 1000);\n    return `${diffSeconds}s`;\n};\n\nexport const formatCommitDate = (\n    timeStamp: number,\n    options?: { includeDate?: boolean },\n): string => {\n    const then = new Date(timeStamp * 1000);\n    return then.toLocaleString('en-US', {\n        hour: 'numeric',\n        minute: 'numeric',\n        ...(options?.includeDate && {\n            month: 'numeric',\n            day: 'numeric',\n            year: '2-digit',\n        }),\n    });\n};\n\n/**\n * A utility class for performance logging and timing\n * Tracks elapsed time since creation and provides logging methods\n */\nexport class LogTimer {\n    private startTime: number;\n    private name: string;\n\n    constructor(name: string) {\n        this.startTime = Date.now();\n        this.name = name;\n    }\n\n    /**\n     * Logs the elapsed time for a specific step\n     * @param step - Description of the step being timed\n     */\n    log(step: string): void {\n        const elapsed = Date.now() - this.startTime;\n        console.log(`[${this.name}] ${step}: ${elapsed}ms`);\n    }\n\n    /**\n     * Gets the elapsed time in milliseconds without logging\n     * @returns Elapsed time in milliseconds\n     */\n    getElapsed(): number {\n        return Date.now() - this.startTime;\n    }\n\n    /**\n     * Resets the timer to the current time\n     */\n    reset(): void {\n        this.startTime = Date.now();\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,UAAU,CAAC;IACpB,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;IACzC,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,KAAK,KAAK,EAAE;IAElE,IAAI,YAAY,GAAG;QACf,OAAO,GAAG,UAAU,CAAC,CAAC;IAC1B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,KAAK,EAAE;IAC9D,IAAI,aAAa,GAAG;QAChB,OAAO,GAAG,WAAW,CAAC,CAAC;IAC3B;IAEA,MAAM,WAAW,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACvD,IAAI,WAAW,GAAG;QACd,OAAO,GAAG,SAAS,CAAC,CAAC;IACzB;IAEA,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IACnD,IAAI,YAAY,GAAG;QACf,OAAO,GAAG,UAAU,CAAC,CAAC;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE;IAChD,IAAI,cAAc,GAAG;QACjB,OAAO,GAAG,YAAY,CAAC,CAAC;IAC5B;IACA,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO;IACtC,OAAO,GAAG,YAAY,CAAC,CAAC;AAC5B;AAEO,MAAM,mBAAmB,CAC5B,WACA;IAEA,MAAM,OAAO,IAAI,KAAK,YAAY;IAClC,OAAO,KAAK,cAAc,CAAC,SAAS;QAChC,MAAM;QACN,QAAQ;QACR,GAAI,SAAS,eAAe;YACxB,OAAO;YACP,KAAK;YACL,MAAM;QACV,CAAC;IACL;AACJ;AAMO,MAAM;IACD,UAAkB;IAClB,KAAa;IAErB,YAAY,IAAY,CAAE;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;QACzB,IAAI,CAAC,IAAI,GAAG;IAChB;IAEA;;;KAGC,GACD,IAAI,IAAY,EAAQ;QACpB,MAAM,UAAU,KAAK,GAAG,KAAK,IAAI,CAAC,SAAS;QAC3C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAC;IACtD;IAEA;;;KAGC,GACD,aAAqB;QACjB,OAAO,KAAK,GAAG,KAAK,IAAI,CAAC,SAAS;IACtC;IAEA;;KAEC,GACD,QAAc;QACV,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;IAC7B;AACJ", "debugId": null}}, {"offset": {"line": 4957, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/unit.ts"], "sourcesContent": ["export function stringToParsedValue(val: string, percent = false): { num: number; unit: string } {\n    const matches = val.match(/([-+]?[0-9]*\\.?[0-9]+)([a-zA-Z%]*)/);\n\n    let num = matches ? Number.parseFloat(matches[1] ?? '0') : 0;\n    let unit = matches && matches[2] ? matches[2] : 'px';\n\n    if (percent && unit === '') {\n        unit = '%';\n        num = num <= 1 ? num * 100 : num;\n    }\n    return { num, unit };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,oBAAoB,GAAW,EAAE,UAAU,KAAK;IAC5D,MAAM,UAAU,IAAI,KAAK,CAAC;IAE1B,IAAI,MAAM,UAAU,OAAO,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,OAAO;IAC3D,IAAI,OAAO,WAAW,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG;IAEhD,IAAI,WAAW,SAAS,IAAI;QACxB,OAAO;QACP,MAAM,OAAO,IAAI,MAAM,MAAM;IACjC;IACA,OAAO;QAAE;QAAK;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 4982, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/urls.ts"], "sourcesContent": ["import normalizeUrl from 'normalize-url';\nimport { parse } from 'tldts';\n\nexport function getValidUrl(url: string) {\n    // If the url is not https, convert it to https\n    const prependedUrl = prependHttp(url);\n    const normalizedUrl = normalizeUrl(prependedUrl);\n    return normalizedUrl;\n}\n\nexport function isApexDomain(domain: string): {\n    isValid: boolean;\n    error?: string;\n} {\n    const parsed = parse(domain);\n    if (parsed.subdomain) {\n        return {\n            isValid: false,\n            error: 'Please enter a domain without subdomains (e.g., example.com or example.co.uk)',\n        };\n    }\n\n    if (!parsed.publicSuffix) {\n        return {\n            isValid: false,\n            error: 'Please enter a domain with suffix (e.g., example.com or example.co.uk)',\n        };\n    }\n\n    return {\n        isValid: true,\n    };\n}\n\nexport function prependHttp(url: string, { https = true } = {}) {\n    if (typeof url !== 'string') {\n        throw new TypeError(`Expected \\`url\\` to be of type \\`string\\`, got \\`${typeof url}\\``);\n    }\n\n    url = url.trim();\n\n    if (/^\\.*\\/|^(?!localhost)(\\w+?:)/.test(url)) {\n        return url;\n    }\n\n    // Special case for localhost - use http:// instead of https://\n    if (url.startsWith('localhost')) {\n        return `http://${url}`;\n    }\n\n    return url.replace(/^(?!(?:\\w+?:)?\\/\\/)/, https ? 'https://' : 'http://');\n}\n\nexport const getValidSubdomain = (subdomain: string) => {\n    // Make this a valid subdomain by:\n    // 1. Converting to lowercase\n    // 2. Replacing invalid characters with hyphens\n    // 3. Removing consecutive hyphens\n    // 4. Removing leading/trailing hyphens\n    return subdomain\n        .toLowerCase()\n        .replace(/[^a-z0-9-]/g, '-')\n        .replace(/-+/g, '-')\n        .replace(/^-|-$/g, '');\n};\n\nexport const getPublishUrls = (url: string) => {\n    // Return a list including url and www.url\n    return [url, `www.${url}`];\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,YAAY,GAAW;IACnC,+CAA+C;IAC/C,MAAM,eAAe,YAAY;IACjC,MAAM,gBAAgB,CAAA,GAAA,4IAAA,CAAA,UAAY,AAAD,EAAE;IACnC,OAAO;AACX;AAEO,SAAS,aAAa,MAAc;IAIvC,MAAM,SAAS,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE;IACrB,IAAI,OAAO,SAAS,EAAE;QAClB,OAAO;YACH,SAAS;YACT,OAAO;QACX;IACJ;IAEA,IAAI,CAAC,OAAO,YAAY,EAAE;QACtB,OAAO;YACH,SAAS;YACT,OAAO;QACX;IACJ;IAEA,OAAO;QACH,SAAS;IACb;AACJ;AAEO,SAAS,YAAY,GAAW,EAAE,EAAE,QAAQ,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1D,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,UAAU,CAAC,iDAAiD,EAAE,OAAO,IAAI,EAAE,CAAC;IAC1F;IAEA,MAAM,IAAI,IAAI;IAEd,IAAI,+BAA+B,IAAI,CAAC,MAAM;QAC1C,OAAO;IACX;IAEA,+DAA+D;IAC/D,IAAI,IAAI,UAAU,CAAC,cAAc;QAC7B,OAAO,CAAC,OAAO,EAAE,KAAK;IAC1B;IAEA,OAAO,IAAI,OAAO,CAAC,uBAAuB,QAAQ,aAAa;AACnE;AAEO,MAAM,oBAAoB,CAAC;IAC9B,kCAAkC;IAClC,6BAA6B;IAC7B,+CAA+C;IAC/C,kCAAkC;IAClC,uCAAuC;IACvC,OAAO,UACF,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,UAAU;AAC3B;AAEO,MAAM,iBAAiB,CAAC;IAC3B,0CAA0C;IAC1C,OAAO;QAAC;QAAK,CAAC,IAAI,EAAE,KAAK;KAAC;AAC9B", "debugId": null}}, {"offset": {"line": 5055, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/window-metadata.ts"], "sourcesContent": ["import { DEVICE_OPTIONS, Orientation, Theme } from '@onlook/constants';\nimport type { WindowMetadata } from '@onlook/models';\n\nexport const computeWindowMetadata = (width: string, height: string): WindowMetadata => {\n    const numericWidth = Number(width);\n    const numericHeight = Number(height);\n\n    return {\n        orientation: numericWidth > numericHeight ? Orientation.Landscape : Orientation.Portrait,\n        aspectRatioLocked: true,\n        device: computeDevice(numericWidth, numericHeight),\n        theme: Theme.System,\n        width: numericWidth,\n        height: numericHeight,\n    };\n};\n\nconst computeDevice = (width: number, height: number): string => {\n    let matchedDevice = 'Custom';\n\n    for (const category in DEVICE_OPTIONS) {\n        const devices = DEVICE_OPTIONS[category as keyof typeof DEVICE_OPTIONS];\n\n        for (const deviceName in devices) {\n            const resolution = devices[deviceName];\n            if (typeof resolution === 'string') {\n                const [w, h] = resolution.split('x').map(Number);\n                if (w === width && h === height) {\n                    matchedDevice = deviceName;\n                    break;\n                }\n            }\n        }\n\n        if (matchedDevice !== 'Custom') break;\n    }\n    return matchedDevice;\n};\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,MAAM,wBAAwB,CAAC,OAAe;IACjD,MAAM,eAAe,OAAO;IAC5B,MAAM,gBAAgB,OAAO;IAE7B,OAAO;QACH,aAAa,eAAe,gBAAgB,wIAAA,CAAA,cAAW,CAAC,SAAS,GAAG,wIAAA,CAAA,cAAW,CAAC,QAAQ;QACxF,mBAAmB;QACnB,QAAQ,cAAc,cAAc;QACpC,OAAO,wIAAA,CAAA,QAAK,CAAC,MAAM;QACnB,OAAO;QACP,QAAQ;IACZ;AACJ;AAEA,MAAM,gBAAgB,CAAC,OAAe;IAClC,IAAI,gBAAgB;IAEpB,IAAK,MAAM,YAAY,wIAAA,CAAA,iBAAc,CAAE;QACnC,MAAM,UAAU,wIAAA,CAAA,iBAAc,CAAC,SAAwC;QAEvE,IAAK,MAAM,cAAc,QAAS;YAC9B,MAAM,aAAa,OAAO,CAAC,WAAW;YACtC,IAAI,OAAO,eAAe,UAAU;gBAChC,MAAM,CAAC,GAAG,EAAE,GAAG,WAAW,KAAK,CAAC,KAAK,GAAG,CAAC;gBACzC,IAAI,MAAM,SAAS,MAAM,QAAQ;oBAC7B,gBAAgB;oBAChB;gBACJ;YACJ;QACJ;QAEA,IAAI,kBAAkB,UAAU;IACpC;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 5100, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/utility/src/index.ts"], "sourcesContent": ["export * from './assert';\nexport * from './autolayout';\nexport * from './clone';\nexport * from './color';\nexport * from './domain';\nexport * from './email';\nexport * from './errors';\nexport * from './file';\nexport * from './font';\nexport * from './id';\nexport * from './image';\nexport * from './initials';\nexport * from './math';\nexport * from './null';\nexport * from './path';\nexport * from './screenshot';\nexport * from './string';\nexport * from './tailwind';\nexport * from './time';\nexport * from './unit';\nexport * from './urls';\nexport * from './window-metadata';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}]}