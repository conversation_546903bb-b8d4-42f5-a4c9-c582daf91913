{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_133e7ea9._.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_587cc249._.js", "server/edge/chunks/[root-of-the-server]__1274d79d._.js", "server/edge/chunks/apps_web_client_edge-wrapper_0447e01a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UbnSsXifywJT6PaEeFyV81RV7w9GKP7hknsOxWW+8jA=", "__NEXT_PREVIEW_MODE_ID": "b03e905df0d7b2aaf11785a7b5b461d9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "71aa965fd8eb5e7c1b00f5c2a407b51b98660dfdade3880354d469de494247d1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d3d60d7d11ce56f64325721eb2a6d401241bf06eb2473998489de7a8420b7fee"}}}, "instrumentation": null, "functions": {}}