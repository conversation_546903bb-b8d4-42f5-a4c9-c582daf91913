{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/env.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\r\nimport { z } from 'zod';\r\n\r\nexport const env = createEnv({\r\n    /**\r\n     * Specify your server-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars.\r\n     */\r\n    server: {\r\n        NODE_ENV: z.enum(['development', 'test', 'production']),\r\n        ANTHROPIC_API_KEY: z.string(),\r\n        CSB_API_KEY: z.string(),\r\n        SUPABASE_DATABASE_URL: z.string().url(),\r\n        RESEND_API_KEY: z.string().optional(),\r\n        MORPH_API_KEY: z.string().optional(),\r\n        RELACE_API_KEY: z.string().optional(),\r\n        FREESTYLE_API_KEY: z.string().optional(),\r\n        STRIPE_WEBHOOK_SECRET: z.string().optional(),\r\n        STRIPE_SECRET_KEY: z.string().optional(),\r\n        AWS_ACCESS_KEY_ID: z.string().optional(),\r\n        AWS_SECRET_ACCESS_KEY: z.string().optional(),\r\n        AWS_REGION: z.string().optional(),\r\n    },\r\n    /**\r\n     * Specify your client-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n     * `NEXT_PUBLIC_`.\r\n     */\r\n    client: {\r\n        NEXT_PUBLIC_SITE_URL: z.string().url().default('http://localhost:3000'),\r\n        NEXT_PUBLIC_SUPABASE_URL: z.string(),\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\r\n        NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),\r\n        NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: z.boolean().default(false),\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: z.string().optional(),\r\n    },\r\n\r\n    /**\r\n     * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n     * middlewares) or client-side so we need to destruct manually.\r\n     */\r\n    runtimeEnv: {\r\n        NODE_ENV: process.env.NODE_ENV,\r\n        ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,\r\n        CSB_API_KEY: process.env.CSB_API_KEY,\r\n        RESEND_API_KEY: process.env.RESEND_API_KEY,\r\n        SUPABASE_DATABASE_URL: process.env.SUPABASE_DATABASE_URL,\r\n        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,\r\n        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\r\n        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\r\n        MORPH_API_KEY: process.env.MORPH_API_KEY,\r\n        RELACE_API_KEY: process.env.RELACE_API_KEY,\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: process.env.NEXT_PUBLIC_FEATURE_COLLABORATION,\r\n        FREESTYLE_API_KEY: process.env.FREESTYLE_API_KEY,\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: process.env.NEXT_PUBLIC_HOSTING_DOMAIN,\r\n        STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,\r\n        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,\r\n        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,\r\n        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,\r\n        AWS_REGION: process.env.AWS_REGION,\r\n    },\r\n    /**\r\n     * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n     * useful for Docker builds.\r\n     */\r\n    skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n    /**\r\n     * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n     * `SOME_VAR=''` will throw an error.\r\n     */\r\n    emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;IACzB;;;KAGC,GACD,QAAQ;QACJ,UAAU,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM;QAC3B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;QACrB,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACrC,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC;IACA;;;;KAIC,GACD,QAAQ;QACJ,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;QAC/C,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM;QAClC,+BAA+B,mLAAA,CAAA,IAAC,CAAC,MAAM;QACvC,yBAAyB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7C,mCAAmC,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACvD,4BAA4B,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnD;IAEA;;;KAGC,GACD,YAAY;QACR,QAAQ;QACR,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,wBAAwB;QACxB,6BAA6B;QAC7B,yBAAyB,QAAQ,GAAG,CAAC,uBAAuB;QAC5D,0BAA0B,QAAQ,GAAG,CAAC,wBAAwB;QAC9D,eAAe,QAAQ,GAAG,CAAC,aAAa;QACxC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,mCAAmC,QAAQ,GAAG,CAAC,iCAAiC;QAChF,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;QAClE,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,YAAY,QAAQ,GAAG,CAAC,UAAU;IACtC;IACA;;;KAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;KAGC,GACD,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/supabase/server.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { createServerClient } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function createClient() {\r\n    const cookieStore = await cookies();\r\n\r\n    // Create a server's supabase client with newly configured cookie,\r\n    // which could be used to maintain user's session\r\n    return createServerClient(\r\n        env.NEXT_PUBLIC_SUPABASE_URL,\r\n        env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        {\r\n            cookies: {\r\n                getAll() {\r\n                    return cookieStore.getAll();\r\n                },\r\n                setAll(cookiesToSet) {\r\n                    try {\r\n                        cookiesToSet.forEach(({ name, value, options }) =>\r\n                            cookieStore.set(name, value, options),\r\n                        );\r\n                    } catch {\r\n                        // The `setAll` method was called from a Server Component.\r\n                        // This can be ignored if you have middleware refreshing\r\n                        // user sessions.\r\n                    }\r\n                },\r\n            },\r\n        },\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,eAAe;IAClB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,kEAAkE;IAClE,iDAAiD;IACjD,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACpB,qIAAA,CAAA,MAAG,CAAC,wBAAwB,EAC5B,qIAAA,CAAA,MAAG,CAAC,6BAA6B,EACjC;QACI,SAAS;YACL;gBACI,OAAO,YAAY,MAAM;YAC7B;YACA,QAAO,YAAY;gBACf,IAAI;oBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC1C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAErC,EAAE,OAAM;gBACJ,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACrB;YACJ;QACJ;IACJ;AAER", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/trpc.ts"], "sourcesContent": ["/**\r\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\r\n * 1. You want to modify request context (see Part 1).\r\n * 2. You want to create a new middleware or type of procedure (see Part 3).\r\n *\r\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\r\n * need to use are documented accordingly near the end.\r\n */\r\n\r\nimport { initTRPC, TRPCError } from '@trpc/server';\r\nimport superjson from 'superjson';\r\nimport { ZodError } from 'zod';\r\nimport type { User } from '@supabase/supabase-js';\r\nimport type { SetRequiredDeep } from 'type-fest';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { db } from '@onlook/db/src/client';\r\n\r\n/**\r\n * 1. CONTEXT\r\n *\r\n * This section defines the \"contexts\" that are available in the backend API.\r\n *\r\n * These allow you to access things when processing a request, like the database, the session, etc.\r\n *\r\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\r\n * wrap this and provides the required context.\r\n *\r\n * @see https://trpc.io/docs/server/context\r\n */\r\nexport const createTRPCContext = async (opts: { headers: Headers }) => {\r\n    const supabase = await createClient();\r\n    const {\r\n        data: { user },\r\n        error,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (error) {\r\n        throw new TRPCError({ code: 'UNAUTHORIZED', message: error.message });\r\n    }\r\n\r\n    return {\r\n        db,\r\n        supabase,\r\n        user,\r\n        ...opts,\r\n    };\r\n};\r\n\r\n/**\r\n * 2. INITIALIZATION\r\n *\r\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\r\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\r\n * errors on the backend.\r\n */\r\nconst t = initTRPC.context<typeof createTRPCContext>().create({\r\n    transformer: superjson,\r\n    errorFormatter({ shape, error }) {\r\n        return {\r\n            ...shape,\r\n            data: {\r\n                ...shape.data,\r\n                zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,\r\n            },\r\n        };\r\n    },\r\n});\r\n\r\n/**\r\n * Create a server-side caller.\r\n *\r\n * @see https://trpc.io/docs/server/server-side-calls\r\n */\r\nexport const createCallerFactory = t.createCallerFactory;\r\n\r\n/**\r\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\r\n *\r\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\r\n * \"/src/server/api/routers\" directory.\r\n */\r\n\r\n/**\r\n * This is how you create new routers and sub-routers in your tRPC API.\r\n *\r\n * @see https://trpc.io/docs/router\r\n */\r\nexport const createTRPCRouter = t.router;\r\n\r\n/**\r\n * Middleware for timing procedure execution and adding an artificial delay in development.\r\n *\r\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\r\n * network latency that would occur in production but not in local development.\r\n */\r\nconst timingMiddleware = t.middleware(async ({ next, path }) => {\r\n    const start = Date.now();\r\n\r\n    if (t._config.isDev) {\r\n        // artificial delay in dev\r\n        const waitMs = Math.floor(Math.random() * 400) + 100;\r\n        await new Promise((resolve) => setTimeout(resolve, waitMs));\r\n    }\r\n\r\n    const result = await next();\r\n\r\n    const end = Date.now();\r\n    console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\r\n\r\n    return result;\r\n});\r\n\r\n/**\r\n * Public (unauthenticated) procedure\r\n *\r\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\r\n * guarantee that a user querying is authorized, but you can still access user session data if they\r\n * are logged in.\r\n */\r\nexport const publicProcedure = t.procedure.use(timingMiddleware);\r\n\r\n/**\r\n * Protected (authenticated) procedure\r\n *\r\n * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies\r\n * the session is valid and guarantees `ctx.session.user` is not null.\r\n *\r\n * @see https://trpc.io/docs/procedures\r\n */\r\nexport const protectedProcedure = t.procedure.use(timingMiddleware).use(({ ctx, next }) => {\r\n    if (!ctx.user) {\r\n        throw new TRPCError({ code: 'UNAUTHORIZED' });\r\n    }\r\n\r\n    if (!ctx.user.email) {\r\n        throw new TRPCError({\r\n            code: 'UNAUTHORIZED',\r\n            message: 'User must have an email address to access this resource',\r\n        });\r\n    }\r\n\r\n    return next({\r\n        ctx: {\r\n            // infers the `session` as non-nullable\r\n            user: ctx.user as SetRequiredDeep<User, 'email'>,\r\n            db: ctx.db,\r\n        },\r\n    });\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AAED;AAAA;AACA;AACA;AAAA;AAIA;AACA;;;;;;AAcO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACF,MAAM,EAAE,IAAI,EAAE,EACd,KAAK,EACR,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,OAAO;QACP,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAAE,MAAM;YAAgB,SAAS,MAAM,OAAO;QAAC;IACvE;IAEA,OAAO;QACH,IAAA,iIAAA,CAAA,KAAE;QACF;QACA;QACA,GAAG,IAAI;IACX;AACJ;AAEA;;;;;;CAMC,GACD,MAAM,IAAI,mKAAA,CAAA,WAAQ,CAAC,OAAO,GAA6B,MAAM,CAAC;IAC1D,aAAa,4IAAA,CAAA,UAAS;IACtB,gBAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QAC3B,OAAO;YACH,GAAG,KAAK;YACR,MAAM;gBACF,GAAG,MAAM,IAAI;gBACb,UAAU,MAAM,KAAK,YAAY,sJAAA,CAAA,WAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,KAAK;YACxE;QACJ;IACJ;AACJ;AAOO,MAAM,sBAAsB,EAAE,mBAAmB;AAcjD,MAAM,mBAAmB,EAAE,MAAM;AAExC;;;;;CAKC,GACD,MAAM,mBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACvD,MAAM,QAAQ,KAAK,GAAG;IAEtB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE;QACjB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACjD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IACvD;IAEA,MAAM,SAAS,MAAM;IAErB,MAAM,MAAM,KAAK,GAAG;IACpB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM,aAAa,CAAC;IAE7D,OAAO;AACX;AASO,MAAM,kBAAkB,EAAE,SAAS,CAAC,GAAG,CAAC;AAUxC,MAAM,qBAAqB,EAAE,SAAS,CAAC,GAAG,CAAC,kBAAkB,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE;IAClF,IAAI,CAAC,IAAI,IAAI,EAAE;QACX,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAAE,MAAM;QAAe;IAC/C;IAEA,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;QACjB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAChB,MAAM;YACN,SAAS;QACb;IACJ;IAEA,OAAO,KAAK;QACR,KAAK;YACD,uCAAuC;YACvC,MAAM,IAAI,IAAI;YACd,IAAI,IAAI,EAAE;QACd;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/code.ts"], "sourcesContent": ["import { applyCodeChange } from '@onlook/ai';\r\nimport { z } from 'zod';\r\nimport { createTR<PERSON>Router, protectedProcedure } from '../trpc';\r\n\r\nexport const codeRouter = createTRPCRouter({\r\n    applyDiff: protectedProcedure\r\n        .input(z.object({ originalCode: z.string(), updateSnippet: z.string() }))\r\n        .mutation(async ({ ctx, input }): Promise<{ result: string | null, error: string | null }> => {\r\n            try {\r\n                const result = await applyCodeChange(input.originalCode, input.updateSnippet);\r\n                if (!result) {\r\n                    throw new Error('Failed to apply code change. Please try again.');\r\n                }\r\n                return {\r\n                    result,\r\n                    error: null,\r\n                };\r\n            } catch (error) {\r\n                console.error('Failed to apply code change', error);\r\n                return {\r\n                    error: error instanceof Error ? error.message : 'Unknown error',\r\n                    result: null,\r\n                };\r\n            }\r\n        }),\r\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,WAAW,uJAAA,CAAA,qBAAkB,CACxB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACrE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,IAAI;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,YAAY,EAAE,MAAM,aAAa;YAC5E,IAAI,CAAC,QAAQ;gBACT,MAAM,IAAI,MAAM;YACpB;YACA,OAAO;gBACH;gBACA,OAAO;YACX;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBACH,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,QAAQ;YACZ;QACJ;IACJ;AACR", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/domain/freestyle.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { TRPCError } from '@trpc/server';\r\nimport { FreestyleSandboxes } from 'freestyle-sandboxes';\r\n\r\nexport const initializeFreestyleSdk = () => {\r\n    if (!env.FREESTYLE_API_KEY) {\r\n        throw new TRPCError({\r\n            code: 'PRECONDITION_FAILED',\r\n            message: 'FREESTYLE_API_KEY is not configured. Please set the environment variable to use domain publishing features.',\r\n        });\r\n    }\r\n    return new FreestyleSandboxes({\r\n        apiKey: env.FREESTYLE_API_KEY\r\n    });\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,MAAM,yBAAyB;IAClC,IAAI,CAAC,qIAAA,CAAA,MAAG,CAAC,iBAAiB,EAAE;QACxB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAChB,MAAM;YACN,SAAS;QACb;IACJ;IACA,OAAO,IAAI,0JAAA,CAAA,qBAAkB,CAAC;QAC1B,QAAQ,qIAAA,CAAA,MAAG,CAAC,iBAAiB;IACjC;AACJ", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/domain/adapters/freestyle.ts"], "sourcesContent": ["import type { FreestyleDeployWebSuccessResponseV2 } from 'freestyle-sandboxes';\r\nimport { initializeFreestyleSdk } from '../freestyle';\r\nimport type {\r\n    HostingProviderAdapter,\r\n    DeploymentRequest,\r\n    DeploymentResponse\r\n} from '@onlook/models';\r\n\r\nexport class FreestyleAdapter implements HostingProviderAdapter {\r\n    async deploy(request: DeploymentRequest): Promise<DeploymentResponse> {\r\n        const sdk = initializeFreestyleSdk();\r\n        \r\n        const res = await sdk.deployWeb(\r\n            {\r\n                files: request.files,\r\n                kind: 'files',\r\n            },\r\n            request.config\r\n        );\r\n        \r\n        const freestyleResponse = res as {\r\n            message?: string;\r\n            error?: {\r\n                message: string;\r\n            };\r\n            data?: FreestyleDeployWebSuccessResponseV2;\r\n        };\r\n        \r\n        if (freestyleResponse.error) {\r\n            throw new Error(\r\n                freestyleResponse.error.message || \r\n                freestyleResponse.message || \r\n                'Unknown error'\r\n            );\r\n        }\r\n        \r\n        return {\r\n            deploymentId: freestyleResponse.data?.deploymentId ?? '',\r\n            success: true\r\n        };\r\n    }\r\n} "], "names": [], "mappings": ";;;AACA;;AAOO,MAAM;IACT,MAAM,OAAO,OAA0B,EAA+B;QAClE,MAAM,MAAM,CAAA,GAAA,iLAAA,CAAA,yBAAsB,AAAD;QAEjC,MAAM,MAAM,MAAM,IAAI,SAAS,CAC3B;YACI,OAAO,QAAQ,KAAK;YACpB,MAAM;QACV,GACA,QAAQ,MAAM;QAGlB,MAAM,oBAAoB;QAQ1B,IAAI,kBAAkB,KAAK,EAAE;YACzB,MAAM,IAAI,MACN,kBAAkB,KAAK,CAAC,OAAO,IAC/B,kBAAkB,OAAO,IACzB;QAER;QAEA,OAAO;YACH,cAAc,kBAAkB,IAAI,EAAE,gBAAgB;YACtD,SAAS;QACb;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/domain/hosting-factory.ts"], "sourcesContent": ["import { HostingProvider, type HostingProviderAdapter } from '@onlook/models';\r\nimport { FreestyleAdapter } from './adapters/freestyle';\r\n\r\nexport class HostingProviderFactory {\r\n    static create(provider: HostingProvider = HostingProvider.FREESTYLE): HostingProviderAdapter {\r\n        switch (provider) {\r\n            case HostingProvider.FREESTYLE:\r\n                return new FreestyleAdapter();\r\n            default:\r\n                throw new Error(`Unsupported hosting provider: ${provider}`);\r\n        }\r\n    }\r\n} "], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,MAAM;IACT,OAAO,OAAO,WAA4B,+IAAA,CAAA,kBAAe,CAAC,SAAS,EAA0B;QACzF,OAAQ;YACJ,KAAK,+IAAA,CAAA,kBAAe,CAAC,SAAS;gBAC1B,OAAO,IAAI,6LAAA,CAAA,mBAAgB;YAC/B;gBACI,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,UAAU;QACnE;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/domain/preview.ts"], "sourcesContent": ["import { previewDomains, publishedDomains } from '@onlook/db';\r\nimport { HostingProvider } from '@onlook/models';\r\nimport { TRPCError } from '@trpc/server';\r\nimport { and, eq, inArray, ne } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\nimport { HostingProviderFactory } from './hosting-factory';\r\n\r\nexport const previewRouter = createTRPCRouter({\r\n    get: protectedProcedure.input(z.object({\r\n        projectId: z.string(),\r\n    })).query(async ({ ctx, input }) => {\r\n        const preview = await ctx.db.query.previewDomains.findMany({\r\n            where: eq(previewDomains.projectId, input.projectId),\r\n        });\r\n        return preview;\r\n    }),\r\n    create: protectedProcedure.input(z.object({\r\n        domain: z.string(),\r\n        projectId: z.string(),\r\n    })).mutation(async ({ ctx, input }) => {\r\n        // Check if the domain is already taken by another project\r\n        // This should never happen, but just in case\r\n        const existing = await ctx.db.query.previewDomains.findFirst({\r\n            where: and(eq(previewDomains.fullDomain, input.domain), ne(previewDomains.projectId, input.projectId)),\r\n        });\r\n\r\n        if (existing) {\r\n            throw new TRPCError({\r\n                code: 'BAD_REQUEST',\r\n                message: 'Domain already taken',\r\n            });\r\n        }\r\n\r\n        const [preview] = await ctx.db.insert(previewDomains).values({\r\n            fullDomain: input.domain,\r\n            projectId: input.projectId,\r\n        }).returning({\r\n            fullDomain: previewDomains.fullDomain,\r\n        });\r\n\r\n        if (!preview) {\r\n            throw new TRPCError({\r\n                code: 'BAD_REQUEST',\r\n                message: 'Failed to create preview domain, no preview domain returned',\r\n            });\r\n        }\r\n\r\n        return {\r\n            domain: preview.fullDomain,\r\n        }\r\n    }),\r\n    publish: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                type: z.enum(['preview', 'custom']),\r\n                projectId: z.string(),\r\n                files: z.record(z.string(), z.object({\r\n                    content: z.string(),\r\n                    encoding: z.string().optional(),\r\n                })),\r\n                config: z.object({\r\n                    domains: z.array(z.string()),\r\n                    entrypoint: z.string().optional(),\r\n                    envVars: z.record(z.string(), z.string()).optional(),\r\n                }),\r\n            }),\r\n        )\r\n        .mutation(async ({ ctx, input }) => {\r\n            if (input.type === 'preview') {\r\n                const preview = await ctx.db.query.previewDomains.findFirst({\r\n                    where: and(\r\n                        eq(previewDomains.projectId, input.projectId),\r\n                        inArray(previewDomains.fullDomain, input.config.domains),\r\n                    ),\r\n                });\r\n                if (!preview) {\r\n                    throw new TRPCError({\r\n                        code: 'BAD_REQUEST',\r\n                        message: 'No preview domain found',\r\n                    });\r\n                }\r\n            } else if (input.type === 'custom') {\r\n                const custom = await ctx.db.query.publishedDomains.findFirst({\r\n                    where: and(\r\n                        eq(publishedDomains.projectId, input.projectId),\r\n                        inArray(publishedDomains.fullDomain, input.config.domains),\r\n                    ),\r\n                });\r\n                if (!custom) {\r\n                    throw new TRPCError({\r\n                        code: 'BAD_REQUEST',\r\n                        message: 'No custom domain found',\r\n                    });\r\n                }\r\n            }\r\n\r\n            const adapter = HostingProviderFactory.create(HostingProvider.FREESTYLE);\r\n\r\n            const deploymentFiles: Record<string, { content: string; encoding?: 'utf-8' | 'base64' }> = {};\r\n            for (const [path, file] of Object.entries(input.files)) {\r\n                deploymentFiles[path] = {\r\n                    content: file.content,\r\n                    encoding: (file.encoding === 'base64' ? 'base64' : 'utf-8')\r\n                };\r\n            }\r\n\r\n            const result = await adapter.deploy({\r\n                files: deploymentFiles,\r\n                config: input.config\r\n            });\r\n\r\n            return result;\r\n        }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC1C,KAAK,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,iBAAc,CAAC,SAAS,EAAE,MAAM,SAAS;QACvD;QACA,OAAO;IACX;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;QAChB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9B,0DAA0D;QAC1D,6CAA6C;QAC7C,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;YACzD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,iBAAc,CAAC,UAAU,EAAE,MAAM,MAAM,GAAG,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,iBAAc,CAAC,SAAS,EAAE,MAAM,SAAS;QACxG;QAEA,IAAI,UAAU;YACV,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,CAAC,QAAQ,GAAG,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,iKAAA,CAAA,iBAAc,EAAE,MAAM,CAAC;YACzD,YAAY,MAAM,MAAM;YACxB,WAAW,MAAM,SAAS;QAC9B,GAAG,SAAS,CAAC;YACT,YAAY,iKAAA,CAAA,iBAAc,CAAC,UAAU;QACzC;QAEA,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,OAAO;YACH,QAAQ,QAAQ,UAAU;QAC9B;IACJ;IACA,SAAS,uJAAA,CAAA,qBAAkB,CACtB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAW;SAAS;QAClC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;QACnB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACjC,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;YACjB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACjC;QACA,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACb,SAAS,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM;YACzB,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;QACtD;IACJ,IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,IAAI,MAAM,IAAI,KAAK,WAAW;YAC1B,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;gBACxD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACL,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,iBAAc,CAAC,SAAS,EAAE,MAAM,SAAS,GAC5C,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iKAAA,CAAA,iBAAc,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,OAAO;YAE/D;YACA,IAAI,CAAC,SAAS;gBACV,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAChB,MAAM;oBACN,SAAS;gBACb;YACJ;QACJ,OAAO,IAAI,MAAM,IAAI,KAAK,UAAU;YAChC,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACzD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACL,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,mKAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE,MAAM,SAAS,GAC9C,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,mKAAA,CAAA,mBAAgB,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,OAAO;YAEjE;YACA,IAAI,CAAC,QAAQ;gBACT,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAChB,MAAM;oBACN,SAAS;gBACb;YACJ;QACJ;QAEA,MAAM,UAAU,0LAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC,+IAAA,CAAA,kBAAe,CAAC,SAAS;QAEvE,MAAM,kBAAsF,CAAC;QAC7F,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,EAAG;YACpD,eAAe,CAAC,KAAK,GAAG;gBACpB,SAAS,KAAK,OAAO;gBACrB,UAAW,KAAK,QAAQ,KAAK,WAAW,WAAW;YACvD;QACJ;QAEA,MAAM,SAAS,MAAM,QAAQ,MAAM,CAAC;YAChC,OAAO;YACP,QAAQ,MAAM,MAAM;QACxB;QAEA,OAAO;IACX;AACR", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/domain/verify.ts"], "sourcesContent": ["import { customDomains, customDomainVerification, publishedDomains } from '@onlook/db';\r\nimport { VerificationRequestStatus } from '@onlook/models';\r\nimport { TRPCError } from '@trpc/server';\r\nimport { and, eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\nimport { initializeFreestyleSdk } from './freestyle';\r\n\r\nexport const verificationRouter = createTRPCRouter({\r\n    get: protectedProcedure.input(z.object({\r\n        projectId: z.string(),\r\n    })).query(async ({ ctx, input }) => {\r\n        const verification = await ctx.db.query.customDomainVerification.findMany({\r\n            where: eq(customDomainVerification.projectId, input.projectId),\r\n        });\r\n        return verification;\r\n    }),\r\n    create: protectedProcedure.input(z.object({\r\n        domain: z.string(),\r\n        projectId: z.string(),\r\n    })).mutation(async ({ ctx, input }) => {\r\n        // Use upsert to handle the unique constraint properly\r\n        const [customDomain] = await ctx.db\r\n            .insert(customDomains)\r\n            .values({\r\n                apexDomain: input.domain,\r\n            })\r\n            .onConflictDoUpdate({\r\n                target: customDomains.apexDomain,\r\n                set: {\r\n                    updatedAt: new Date(),\r\n                },\r\n            })\r\n            .returning();\r\n\r\n        if (!customDomain) {\r\n            throw new TRPCError({\r\n                code: 'INTERNAL_SERVER_ERROR',\r\n                message: 'Failed to create or update domain',\r\n            });\r\n        }\r\n\r\n        // Check if verification request already exists\r\n        const verification = await ctx.db.query.customDomainVerification.findFirst({\r\n            where: and(\r\n                eq(customDomainVerification.domainId, customDomain.id),\r\n                eq(customDomainVerification.projectId, input.projectId)\r\n            ),\r\n        });\r\n\r\n        if (verification) {\r\n            return verification;\r\n        }\r\n\r\n        const sdk = initializeFreestyleSdk();\r\n        const res = await sdk.createDomainVerificationRequest(input.domain);\r\n        await ctx.db.insert(customDomainVerification).values({\r\n            domainId: customDomain.id,\r\n            projectId: input.projectId,\r\n            verificationId: res.id,\r\n            verificationCode: res.verificationCode,\r\n        });\r\n        return res;\r\n    }),\r\n    verify: protectedProcedure.input(z.object({\r\n        verificationId: z.string(),\r\n        projectId: z.string(),\r\n    })).mutation(async ({ ctx, input }) => {\r\n        const verification = await ctx.db.query.customDomainVerification.findFirst({\r\n            where: and(eq(customDomainVerification.verificationId, input.verificationId), eq(customDomainVerification.projectId, input.projectId)),\r\n        });\r\n        if (!verification) {\r\n            throw new TRPCError({\r\n                code: 'NOT_FOUND',\r\n                message: 'Verification request not found',\r\n            });\r\n        }\r\n\r\n        if (verification.status === VerificationRequestStatus.USED) {\r\n            throw new TRPCError({\r\n                code: 'BAD_REQUEST',\r\n                message: 'Domain already verified',\r\n            });\r\n        }\r\n\r\n        if (verification.status === VerificationRequestStatus.EXPIRED) {\r\n            throw new TRPCError({\r\n                code: 'BAD_REQUEST',\r\n                message: 'Verification request expired',\r\n            });\r\n        }\r\n\r\n        const sdk = initializeFreestyleSdk();\r\n        const res: {\r\n            domain?: string;\r\n            message?: string;\r\n        } = await sdk.verifyDomainVerificationRequest(input.verificationId);\r\n        if (res.message) {\r\n            throw new TRPCError({\r\n                code: 'INTERNAL_SERVER_ERROR',\r\n                message: res.message,\r\n            });\r\n        }\r\n\r\n        const domain = res.domain;\r\n\r\n        if (!domain) {\r\n            throw new TRPCError({\r\n                code: 'INTERNAL_SERVER_ERROR',\r\n                message: 'Domain not found',\r\n            });\r\n        }\r\n\r\n        await ctx.db\r\n            .transaction(\r\n                async (tx) => {\r\n                    await tx.update(customDomains).set({\r\n                        verified: true,\r\n                    }).where(eq(customDomains.id, verification.domainId));\r\n\r\n                    await tx.insert(publishedDomains).values({\r\n                        projectId: verification.projectId,\r\n                        fullDomain: domain,\r\n                        domainId: verification.domainId,\r\n                    });\r\n                },\r\n            );\r\n        return res;\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC/C,KAAK,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ,CAAC;YACtE,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,sKAAA,CAAA,2BAAwB,CAAC,SAAS,EAAE,MAAM,SAAS;QACjE;QACA,OAAO;IACX;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;QAChB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9B,sDAAsD;QACtD,MAAM,CAAC,aAAa,GAAG,MAAM,IAAI,EAAE,CAC9B,MAAM,CAAC,gKAAA,CAAA,gBAAa,EACpB,MAAM,CAAC;YACJ,YAAY,MAAM,MAAM;QAC5B,GACC,kBAAkB,CAAC;YAChB,QAAQ,gKAAA,CAAA,gBAAa,CAAC,UAAU;YAChC,KAAK;gBACD,WAAW,IAAI;YACnB;QACJ,GACC,SAAS;QAEd,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,+CAA+C;QAC/C,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,wBAAwB,CAAC,SAAS,CAAC;YACvE,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACL,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,sKAAA,CAAA,2BAAwB,CAAC,QAAQ,EAAE,aAAa,EAAE,GACrD,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,sKAAA,CAAA,2BAAwB,CAAC,SAAS,EAAE,MAAM,SAAS;QAE9D;QAEA,IAAI,cAAc;YACd,OAAO;QACX;QAEA,MAAM,MAAM,CAAA,GAAA,iLAAA,CAAA,yBAAsB,AAAD;QACjC,MAAM,MAAM,MAAM,IAAI,+BAA+B,CAAC,MAAM,MAAM;QAClE,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,sKAAA,CAAA,2BAAwB,EAAE,MAAM,CAAC;YACjD,UAAU,aAAa,EAAE;YACzB,WAAW,MAAM,SAAS;YAC1B,gBAAgB,IAAI,EAAE;YACtB,kBAAkB,IAAI,gBAAgB;QAC1C;QACA,OAAO;IACX;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtC,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM;QACxB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9B,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,wBAAwB,CAAC,SAAS,CAAC;YACvE,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,sKAAA,CAAA,2BAAwB,CAAC,cAAc,EAAE,MAAM,cAAc,GAAG,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,sKAAA,CAAA,2BAAwB,CAAC,SAAS,EAAE,MAAM,SAAS;QACxI;QACA,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,IAAI,aAAa,MAAM,KAAK,8IAAA,CAAA,4BAAyB,CAAC,IAAI,EAAE;YACxD,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,IAAI,aAAa,MAAM,KAAK,8IAAA,CAAA,4BAAyB,CAAC,OAAO,EAAE;YAC3D,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,MAAM,CAAA,GAAA,iLAAA,CAAA,yBAAsB,AAAD;QACjC,MAAM,MAGF,MAAM,IAAI,+BAA+B,CAAC,MAAM,cAAc;QAClE,IAAI,IAAI,OAAO,EAAE;YACb,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS,IAAI,OAAO;YACxB;QACJ;QAEA,MAAM,SAAS,IAAI,MAAM;QAEzB,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,IAAI,EAAE,CACP,WAAW,CACR,OAAO;YACH,MAAM,GAAG,MAAM,CAAC,gKAAA,CAAA,gBAAa,EAAE,GAAG,CAAC;gBAC/B,UAAU;YACd,GAAG,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,gKAAA,CAAA,gBAAa,CAAC,EAAE,EAAE,aAAa,QAAQ;YAEnD,MAAM,GAAG,MAAM,CAAC,mKAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC;gBACrC,WAAW,aAAa,SAAS;gBACjC,YAAY;gBACZ,UAAU,aAAa,QAAQ;YACnC;QACJ;QAER,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/domain/index.ts"], "sourcesContent": ["import { previewDomains, publishedDomains, toDomainInfoFromPreview, toDomainInfoFromPublished } from '@onlook/db';\r\nimport { eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\nimport { previewRouter } from './preview';\r\nimport { verificationRouter } from './verify';\r\n\r\nexport const domainRouter = createTRPCRouter({\r\n    preview: previewRouter,\r\n    verification: verificationRouter,\r\n    getAll: protectedProcedure.input(z.object({\r\n        projectId: z.string(),\r\n    })).query(async ({ ctx, input }) => {\r\n        const preview = await ctx.db.query.previewDomains.findFirst({\r\n            where: eq(previewDomains.projectId, input.projectId),\r\n        });\r\n        const published = await ctx.db.query.publishedDomains.findFirst({\r\n            where: eq(publishedDomains.projectId, input.projectId),\r\n        });\r\n        return {\r\n            preview: preview ? toDomainInfoFromPreview(preview) : null,\r\n            published: published ? toDomainInfoFromPublished(published) : null,\r\n        }\r\n    }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,+KAAA,CAAA,gBAAa;IACtB,cAAc,8KAAA,CAAA,qBAAkB;IAChC,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;YACxD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,iBAAc,CAAC,SAAS,EAAE,MAAM,SAAS;QACvD;QACA,MAAM,YAAY,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC;YAC5D,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,mKAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE,MAAM,SAAS;QACzD;QACA,OAAO;YACH,SAAS,UAAU,CAAA,GAAA,wIAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW;YACtD,WAAW,YAAY,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE,aAAa;QAClE;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/forward/editor.ts"], "sourcesContent": ["import { editorServerConfig, type EditorRouter } from '@onlook/rpc';\r\nimport { createTR<PERSON><PERSON>lient, createWSClient, httpBatchLink, splitLink, wsLink } from '@trpc/client';\r\nimport superJ<PERSON><PERSON> from 'superjson';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, publicProcedure } from '../../trpc';\r\n\r\nconst { port, prefix } = editorServerConfig;\r\nconst urlEnd = `localhost:${port}${prefix}`;\r\nconst wsClient = createWSClient({ url: `ws://${urlEnd}` });\r\n\r\nconst editorClient = createTRPCClient<EditorRouter>({\r\n    links: [\r\n        splitLink({\r\n            condition(op) {\r\n                return op.type === 'subscription';\r\n            },\r\n            true: wsLink({ client: wsClient, transformer: superJSON }),\r\n            false: httpBatchLink({\r\n                url: `http://${urlEnd}`,\r\n                transformer: superJSON,\r\n            }),\r\n        }),\r\n    ],\r\n});\r\n\r\n// Export the router with all the forwarded procedures\r\nexport const editorForwardRouter = createTRPCRouter({\r\n    sandbox: createTRPCRouter({\r\n        create: publicProcedure.input(z.string()).mutation(({ input }) => {\r\n            return editorClient.sandbox.create.mutate(input);\r\n        }),\r\n        start: publicProcedure.input(z.string()).mutation(({ input }) => {\r\n            return editorClient.sandbox.start.mutate(input);\r\n        }),\r\n\r\n        stop: publicProcedure.input(z.string()).mutation(({ input }) => {\r\n            return editorClient.sandbox.stop.mutate(input);\r\n        }),\r\n        status: publicProcedure.input(z.string()).query(({ input }) => {\r\n            return editorClient.sandbox.status.query(input);\r\n        }),\r\n    }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,0IAAA,CAAA,qBAAkB;AAC3C,MAAM,SAAS,CAAC,UAAU,EAAE,OAAO,QAAQ;AAC3C,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD,EAAE;IAAE,KAAK,CAAC,KAAK,EAAE,QAAQ;AAAC;AAExD,MAAM,eAAe,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD,EAAgB;IAChD,OAAO;QACH,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;YACN,WAAU,EAAE;gBACR,OAAO,GAAG,IAAI,KAAK;YACvB;YACA,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;gBAAE,QAAQ;gBAAU,aAAa,4IAAA,CAAA,UAAS;YAAC;YACxD,OAAO,CAAA,GAAA,wKAAA,CAAA,gBAAa,AAAD,EAAE;gBACjB,KAAK,CAAC,OAAO,EAAE,QAAQ;gBACvB,aAAa,4IAAA,CAAA,UAAS;YAC1B;QACJ;KACH;AACL;AAGO,MAAM,sBAAsB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAChD,SAAS,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,QAAQ,uJAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE;YACzD,OAAO,aAAa,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9C;QACA,OAAO,uJAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE;YACxD,OAAO,aAAa,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;QAC7C;QAEA,MAAM,uJAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE;YACvD,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;QAC5C;QACA,QAAQ,uJAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE;YACtD,OAAO,aAAa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;QAC7C;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/forward/index.ts"], "sourcesContent": ["export * from './editor';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/github.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { z } from 'zod';\r\nimport { Octokit } from 'octokit';\r\nimport { createTRPCRouter, protectedProcedure } from '../trpc';\r\nimport { TRPCError } from '@trpc/server';\r\n\r\n// Helper function to get user's GitHub access token from Supabase session\r\nconst getUserGitHubToken = async (supabase: any) => {\r\n    const { data: { session }, error } = await supabase.auth.getSession();\r\n    \r\n    if (error || !session) {\r\n        throw new TRPCError({\r\n            code: 'UNAUTHORIZED',\r\n            message: 'No active session found'\r\n        });\r\n    }\r\n\r\n    // GitHub OAuth token should be in provider_token, not access_token\r\n    // access_token is Supabase's JWT, provider_token is GitHub's OAuth token\r\n    const githubToken = session.provider_token;\r\n    if (!githubToken) {\r\n        throw new TRPCError({\r\n            code: 'UNAUTHORIZED',\r\n            message: `GitHub token not found. Please reconnect your GitHub account.`\r\n        });\r\n    }\r\n\r\n    return githubToken;\r\n};\r\n\r\n// Create Octokit instance with user's token\r\nconst createUserOctokit = async (supabase: any) => {\r\n    const token = await getUserGitHubToken(supabase);\r\n    return new Octokit({ auth: token });\r\n};\r\n\r\nexport const githubRouter = createTRPCRouter({\r\n    validate: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                owner: z.string(),\r\n                repo: z.string()\r\n            }),\r\n        )\r\n        .mutation(async ({ input, ctx }) => {\r\n            const octokit = await createUserOctokit(ctx.supabase);\r\n            const { data } = await octokit.rest.repos.get({ owner: input.owner, repo: input.repo });\r\n            return {\r\n                branch: data.default_branch,\r\n                isPrivateRepo: data.private\r\n            };\r\n        }),\r\n    \r\n    getRepo: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                owner: z.string(),\r\n                repo: z.string()\r\n            }),\r\n        )\r\n        .query(async ({ input, ctx }) => {\r\n            const octokit = await createUserOctokit(ctx.supabase);\r\n            const { data } = await octokit.rest.repos.get({ \r\n                owner: input.owner, \r\n                repo: input.repo \r\n            });\r\n            return data;\r\n        }),\r\n\r\n    getOrganizations: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                username: z.string().optional()\r\n            }).optional()\r\n        )\r\n        .query(async ({ input, ctx }) => {\r\n            const octokit = await createUserOctokit(ctx.supabase);\r\n            \r\n            if (input?.username) {\r\n                const { data } = await octokit.rest.orgs.listForUser({\r\n                    username: input.username\r\n                });\r\n                return data;\r\n            } else {\r\n                const { data } = await octokit.rest.orgs.listForAuthenticatedUser();\r\n                return data;\r\n            }\r\n        }),\r\n\r\n    getRepositories: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                username: z.string().optional(),\r\n            }).optional()\r\n        )\r\n        .query(async ({ input, ctx }) => {\r\n            const octokit = await createUserOctokit(ctx.supabase);\r\n            \r\n            if (input?.username) {\r\n                // listForUser only supports 'all', 'owner', 'member' types\r\n                const { data } = await octokit.rest.repos.listForUser({\r\n                    username: input.username,\r\n                });\r\n                return data;\r\n            } else {\r\n                const { data } = await octokit.rest.repos.listForAuthenticatedUser({\r\n                    per_page: 100,\r\n                    page: 1,\r\n                });\r\n                return data;\r\n            }\r\n        }),\r\n\r\n    getRepoFiles: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                owner: z.string(),\r\n                repo: z.string(),\r\n                path: z.string().default(''),\r\n                ref: z.string().optional() // branch, tag, or commit SHA\r\n            })\r\n        )\r\n        .query(async ({ input, ctx }) => {\r\n            const octokit = await createUserOctokit(ctx.supabase);\r\n            const { data } = await octokit.rest.repos.getContent({\r\n                owner: input.owner,\r\n                repo: input.repo,\r\n                path: input.path,\r\n                ...(input.ref && { ref: input.ref })\r\n            });\r\n            return data;\r\n        }),\r\n\r\n    checkGitHubConnection: protectedProcedure\r\n        .query(async ({ ctx }) => {\r\n            try {\r\n                const token = await getUserGitHubToken(ctx.supabase);\r\n                return { connected: !!token };\r\n            } catch (error) {\r\n                return { connected: false };\r\n            }\r\n        }),\r\n\r\n    reconnectGitHub: protectedProcedure\r\n        .mutation(async ({ ctx }) => {\r\n            const origin = process.env.NEXT_PUBLIC_APP_URL;\r\n            \r\n            const { data, error } = await ctx.supabase.auth.signInWithOAuth({\r\n                provider: 'github',\r\n                options: {\r\n                    redirectTo: `${origin}/auth/callback`,\r\n                    skipBrowserRedirect: true,\r\n                },\r\n            });\r\n\r\n            if (error) {\r\n                throw new TRPCError({\r\n                    code: 'INTERNAL_SERVER_ERROR',\r\n                    message: 'Failed to initiate GitHub reconnection',\r\n                    cause: error,\r\n                });\r\n            }\r\n\r\n            return { url: data.url };\r\n        }),\r\n});"], "names": [], "mappings": ";;;AACA;AAAA;AACA;AACA;AACA;;;;;AAEA,0EAA0E;AAC1E,MAAM,qBAAqB,OAAO;IAC9B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAEnE,IAAI,SAAS,CAAC,SAAS;QACnB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAChB,MAAM;YACN,SAAS;QACb;IACJ;IAEA,mEAAmE;IACnE,yEAAyE;IACzE,MAAM,cAAc,QAAQ,cAAc;IAC1C,IAAI,CAAC,aAAa;QACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAChB,MAAM;YACN,SAAS,CAAC,6DAA6D,CAAC;QAC5E;IACJ;IAEA,OAAO;AACX;AAEA,4CAA4C;AAC5C,MAAM,oBAAoB,OAAO;IAC7B,MAAM,QAAQ,MAAM,mBAAmB;IACvC,OAAO,IAAI,oKAAA,CAAA,UAAO,CAAC;QAAE,MAAM;IAAM;AACrC;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,UAAU,uJAAA,CAAA,qBAAkB,CACvB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM;QACf,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC3B,MAAM,UAAU,MAAM,kBAAkB,IAAI,QAAQ;QACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAAE,OAAO,MAAM,KAAK;YAAE,MAAM,MAAM,IAAI;QAAC;QACrF,OAAO;YACH,QAAQ,KAAK,cAAc;YAC3B,eAAe,KAAK,OAAO;QAC/B;IACJ;IAEJ,SAAS,uJAAA,CAAA,qBAAkB,CACtB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM;QACf,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,IAEH,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QACxB,MAAM,UAAU,MAAM,kBAAkB,IAAI,QAAQ;QACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC1C,OAAO,MAAM,KAAK;YAClB,MAAM,MAAM,IAAI;QACpB;QACA,OAAO;IACX;IAEJ,kBAAkB,uJAAA,CAAA,qBAAkB,CAC/B,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,GAAG,QAAQ,IAEd,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QACxB,MAAM,UAAU,MAAM,kBAAkB,IAAI,QAAQ;QAEpD,IAAI,OAAO,UAAU;YACjB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;gBACjD,UAAU,MAAM,QAAQ;YAC5B;YACA,OAAO;QACX,OAAO;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,wBAAwB;YACjE,OAAO;QACX;IACJ;IAEJ,iBAAiB,uJAAA,CAAA,qBAAkB,CAC9B,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,GAAG,QAAQ,IAEd,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QACxB,MAAM,UAAU,MAAM,kBAAkB,IAAI,QAAQ;QAEpD,IAAI,OAAO,UAAU;YACjB,2DAA2D;YAC3D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;gBAClD,UAAU,MAAM,QAAQ;YAC5B;YACA,OAAO;QACX,OAAO;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;gBAC/D,UAAU;gBACV,MAAM;YACV;YACA,OAAO;QACX;IACJ;IAEJ,cAAc,uJAAA,CAAA,qBAAkB,CAC3B,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM;QACf,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM;QACd,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;QACzB,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,6BAA6B;IAC5D,IAEH,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QACxB,MAAM,UAAU,MAAM,kBAAkB,IAAI,QAAQ;QACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACjD,OAAO,MAAM,KAAK;YAClB,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI;YAChB,GAAI,MAAM,GAAG,IAAI;gBAAE,KAAK,MAAM,GAAG;YAAC,CAAC;QACvC;QACA,OAAO;IACX;IAEJ,uBAAuB,uJAAA,CAAA,qBAAkB,CACpC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACjB,IAAI;YACA,MAAM,QAAQ,MAAM,mBAAmB,IAAI,QAAQ;YACnD,OAAO;gBAAE,WAAW,CAAC,CAAC;YAAM;QAChC,EAAE,OAAO,OAAO;YACZ,OAAO;gBAAE,WAAW;YAAM;QAC9B;IACJ;IAEJ,iBAAiB,uJAAA,CAAA,qBAAkB,CAC9B,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACpB,MAAM,SAAS,QAAQ,GAAG,CAAC,mBAAmB;QAE9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;YAC5D,UAAU;YACV,SAAS;gBACL,YAAY,GAAG,OAAO,cAAc,CAAC;gBACrC,qBAAqB;YACzB;QACJ;QAEA,IAAI,OAAO;YACP,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;gBACT,OAAO;YACX;QACJ;QAEA,OAAO;YAAE,KAAK,KAAK,GAAG;QAAC;IAC3B;AACR", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/image.ts"], "sourcesContent": ["import { compressImageServer, type CompressionOptions, type CompressionResult } from '@onlook/image-server';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../trpc';\r\n\r\ntype TRPCCompressionResult = Omit<CompressionResult, 'buffer'> & {\r\n    bufferData?: string; // base64 encoded buffer data\r\n};\r\n\r\nexport const imageRouter = createTRPCRouter({\r\n    compress: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                imageData: z.string(), // base64 encoded image data\r\n                options: z.object({\r\n                    quality: z.number().optional(),\r\n                    width: z.number().optional(),\r\n                    height: z.number().optional(),\r\n                    format: z.enum(['jpeg', 'png', 'webp', 'avif', 'auto']).optional(),\r\n                    progressive: z.boolean().optional(),\r\n                    mozjpeg: z.boolean().optional(),\r\n                    effort: z.number().optional(),\r\n                    compressionLevel: z.number().optional(),\r\n                    keepAspectRatio: z.boolean().optional(),\r\n                    withoutEnlargement: z.boolean().optional(),\r\n                }).optional(),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }): Promise<TRPCCompressionResult> => {\r\n            try {\r\n                const buffer = Buffer.from(input.imageData, 'base64');\r\n\r\n                const result = await compressImageServer(\r\n                    buffer,\r\n                    undefined, // No output path - return buffer\r\n                    input.options as CompressionOptions || {}\r\n                );\r\n\r\n                // Convert buffer to base64 for client transmission\r\n                if (result.success && result.buffer) {\r\n                    const { buffer: resultBuffer, ...restResult } = result;\r\n                    return {\r\n                        ...restResult,\r\n                        bufferData: resultBuffer.toString('base64'),\r\n                    };\r\n                }\r\n\r\n                const { buffer: resultBuffer, ...restResult } = result;\r\n                return restResult;\r\n            } catch (error) {\r\n                console.error('Error compressing image:', error);\r\n                return {\r\n                    success: false,\r\n                    error: error instanceof Error ? error.message : 'Unknown compression error',\r\n                };\r\n            }\r\n        }),\r\n}); "], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;;;;AAMO,MAAM,cAAc,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,UAAU,uJAAA,CAAA,qBAAkB,CACvB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;QACnB,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACd,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC5B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC1B,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC3B,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAQ;gBAAO;gBAAQ;gBAAQ;aAAO,EAAE,QAAQ;YAChE,aAAa,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YACjC,SAAS,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YAC7B,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC3B,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACrC,iBAAiB,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YACrC,oBAAoB,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QAC5C,GAAG,QAAQ;IACf,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,IAAI;YACA,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,EAAE;YAE5C,MAAM,SAAS,MAAM,CAAA,GAAA,gJAAA,CAAA,sBAAmB,AAAD,EACnC,QACA,WACA,MAAM,OAAO,IAA0B,CAAC;YAG5C,mDAAmD;YACnD,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,EAAE;gBACjC,MAAM,EAAE,QAAQ,YAAY,EAAE,GAAG,YAAY,GAAG;gBAChD,OAAO;oBACH,GAAG,UAAU;oBACb,YAAY,aAAa,QAAQ,CAAC;gBACtC;YACJ;YAEA,MAAM,EAAE,QAAQ,YAAY,EAAE,GAAG,YAAY,GAAG;YAChD,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACH,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACJ;IACJ;AACR", "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/canvas.ts"], "sourcesContent": ["import { canvases, canvasUpdateSchema } from '@onlook/db';\r\nimport { eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const canvasRouter = createTRPCRouter({\r\n    get: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const dbCanvas = await ctx.db.query.canvases.findFirst({\r\n                where: eq(canvases.projectId, input.projectId),\r\n            });\r\n            if (!dbCanvas) {\r\n                return null;\r\n            }\r\n            return dbCanvas;\r\n        }),\r\n    update: protectedProcedure.input(canvasUpdateSchema).mutation(async ({ ctx, input }) => {\r\n        try {\r\n            if (!input.id) {\r\n                throw new Error('Canvas ID is required');\r\n            }\r\n            await ctx.db.update(canvases).set(input).where(eq(canvases.id, input.id));\r\n            return true;\r\n        } catch (error) {\r\n            console.error('Error updating canvas', error);\r\n            return false;\r\n        }\r\n    }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;YACnD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,gKAAA,CAAA,WAAQ,CAAC,SAAS,EAAE,MAAM,SAAS;QACjD;QACA,IAAI,CAAC,UAAU;YACX,OAAO;QACX;QACA,OAAO;IACX;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,gKAAA,CAAA,qBAAkB,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC/E,IAAI;YACA,IAAI,CAAC,MAAM,EAAE,EAAE;gBACX,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,gKAAA,CAAA,WAAQ,EAAE,GAAG,CAAC,OAAO,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,gKAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,MAAM,EAAE;YACvE,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACX;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/chat.ts"], "sourcesContent": ["import {\r\n    conversationInsertSchema,\r\n    conversations,\r\n    messageInsertSchema,\r\n    messages,\r\n    toConversation,\r\n    toMessage,\r\n    type Message,\r\n} from '@onlook/db';\r\nimport type { ChatMessageRole } from '@onlook/models';\r\nimport { eq, inArray } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nconst conversationRouter = createTRPCRouter({\r\n    get: protectedProcedure\r\n        .input(z.object({ projectId: z.string() }))\r\n        .query(async ({ ctx, input }) => {\r\n            const dbConversations = await ctx.db.query.conversations.findMany({\r\n                where: eq(conversations.projectId, input.projectId),\r\n                orderBy: (conversations, { desc }) => [desc(conversations.updatedAt)],\r\n            });\r\n            return dbConversations.map((conversation) => toConversation(conversation));\r\n        }),\r\n    upsert: protectedProcedure\r\n        .input(z.object({ conversation: conversationInsertSchema }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            return await ctx.db\r\n                .insert(conversations)\r\n                .values(input.conversation)\r\n                .onConflictDoUpdate({\r\n                    target: [conversations.id],\r\n                    set: {\r\n                        ...input.conversation,\r\n                    },\r\n                });\r\n        }),\r\n    delete: protectedProcedure\r\n        .input(z.object({ conversationId: z.string() }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            return await ctx.db\r\n                .delete(conversations)\r\n                .where(eq(conversations.id, input.conversationId));\r\n        }),\r\n});\r\n\r\nconst messageRouter = createTRPCRouter({\r\n    get: protectedProcedure\r\n        .input(z.object({ conversationId: z.string() }))\r\n        .query(async ({ ctx, input }) => {\r\n            const dbMessages = await ctx.db.query.messages.findMany({\r\n                where: eq(messages.conversationId, input.conversationId),\r\n                orderBy: (messages, { asc }) => [asc(messages.createdAt)],\r\n            });\r\n            return dbMessages.map((message) => toMessage(message));\r\n        }),\r\n    upsert: protectedProcedure\r\n        .input(z.object({ message: messageInsertSchema }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            const normalizedMessage = {\r\n                ...input.message,\r\n                role: input.message.role as ChatMessageRole,\r\n                parts: input.message.parts as Message['parts'],\r\n            };\r\n            return await ctx.db\r\n                .insert(messages)\r\n                .values(normalizedMessage)\r\n                .onConflictDoUpdate({\r\n                    target: [messages.id],\r\n                    set: {\r\n                        ...normalizedMessage,\r\n                    },\r\n                });\r\n\r\n        }),\r\n    delete: protectedProcedure\r\n        .input(z.object({ messageIds: z.array(z.string()) }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            return await ctx.db.delete(messages).where(inArray(messages.id, input.messageIds));\r\n        }),\r\n})\r\n\r\nexport const chatRouter = createTRPCRouter({\r\n    conversation: conversationRouter,\r\n    message: messageRouter,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAAA;AACA;;;;;AAEA,MAAM,qBAAqB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,kBAAkB,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC9D,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,oKAAA,CAAA,gBAAa,CAAC,SAAS,EAAE,MAAM,SAAS;YAClD,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,GAAK;oBAAC,KAAK,cAAc,SAAS;iBAAE;QACzE;QACA,OAAO,gBAAgB,GAAG,CAAC,CAAC,eAAiB,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE;IAChE;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,cAAc,oKAAA,CAAA,2BAAwB;IAAC,IACxD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,OAAO,MAAM,IAAI,EAAE,CACd,MAAM,CAAC,oKAAA,CAAA,gBAAa,EACpB,MAAM,CAAC,MAAM,YAAY,EACzB,kBAAkB,CAAC;YAChB,QAAQ;gBAAC,oKAAA,CAAA,gBAAa,CAAC,EAAE;aAAC;YAC1B,KAAK;gBACD,GAAG,MAAM,YAAY;YACzB;QACJ;IACR;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAC5C,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,OAAO,MAAM,IAAI,EAAE,CACd,MAAM,CAAC,oKAAA,CAAA,gBAAa,EACpB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,oKAAA,CAAA,gBAAa,CAAC,EAAE,EAAE,MAAM,cAAc;IACxD;AACR;AAEA,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACnC,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAC5C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,+JAAA,CAAA,WAAQ,CAAC,cAAc,EAAE,MAAM,cAAc;YACvD,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,GAAK;oBAAC,IAAI,SAAS,SAAS;iBAAE;QAC7D;QACA,OAAO,WAAW,GAAG,CAAC,CAAC,UAAY,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE;IACjD;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,SAAS,+JAAA,CAAA,sBAAmB;IAAC,IAC9C,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,oBAAoB;YACtB,GAAG,MAAM,OAAO;YAChB,MAAM,MAAM,OAAO,CAAC,IAAI;YACxB,OAAO,MAAM,OAAO,CAAC,KAAK;QAC9B;QACA,OAAO,MAAM,IAAI,EAAE,CACd,MAAM,CAAC,+JAAA,CAAA,WAAQ,EACf,MAAM,CAAC,mBACP,kBAAkB,CAAC;YAChB,QAAQ;gBAAC,+JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;YACrB,KAAK;gBACD,GAAG,iBAAiB;YACxB;QACJ;IAER;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,YAAY,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAI,IACjD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,OAAO,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,+JAAA,CAAA,WAAQ,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,+JAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,MAAM,UAAU;IACpF;AACR;AAEO,MAAM,aAAa,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,cAAc;IACd,SAAS;AACb", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/frame.ts"], "sourcesContent": ["import { frameInsertSchema, frames, frameUpdateSchema, toFrame } from '@onlook/db';\r\nimport { FrameType } from '@onlook/models';\r\nimport { eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const frameRouter = createTRPCRouter({\r\n    get: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                frameId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const dbFrame = await ctx.db.query.frames.findFirst({\r\n                where: eq(frames.id, input.frameId),\r\n            });\r\n            if (!dbFrame) {\r\n                return null;\r\n            }\r\n            return toFrame(dbFrame);\r\n        }),\r\n    getByCanvas: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                canvasId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const dbFrames = await ctx.db.query.frames.findMany({\r\n                where: eq(frames.canvasId, input.canvasId),\r\n                orderBy: (frames, { asc }) => [asc(frames.x), asc(frames.y)],\r\n            });\r\n            return dbFrames.map((frame) => toFrame(frame));\r\n        }),\r\n    create: protectedProcedure.input(frameInsertSchema).mutation(async ({ ctx, input }) => {\r\n        try {\r\n            const normalizedInput = {\r\n                ...input,\r\n                type: input.type as FrameType,\r\n            };\r\n            await ctx.db.insert(frames).values(normalizedInput);\r\n            return true;\r\n        } catch (error) {\r\n            console.error('Error creating frame', error);\r\n            return false;\r\n        }\r\n    }),\r\n    update: protectedProcedure.input(frameUpdateSchema).mutation(async ({ ctx, input }) => {\r\n        try {\r\n            if (!input.id) {\r\n                throw new Error('Frame ID is required');\r\n            }\r\n            const normalizedInput = {\r\n                ...input,\r\n                type: input.type as FrameType,\r\n            };\r\n            await ctx.db.update(frames).set(normalizedInput).where(eq(frames.id, input.id));\r\n            return true;\r\n        } catch (error) {\r\n            console.error('Error updating frame', error);\r\n            return false;\r\n        }\r\n    }),\r\n    delete: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                frameId: z.string(),\r\n            }),\r\n        )\r\n        .mutation(async ({ ctx, input }) => {\r\n            try {\r\n                await ctx.db.delete(frames).where(eq(frames.id, input.frameId));\r\n                return true;\r\n            } catch (error) {\r\n                console.error('Error deleting frame', error);\r\n                return false;\r\n            }\r\n        }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;;;;;AAEO,MAAM,cAAc,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;YAChD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,+JAAA,CAAA,SAAM,CAAC,EAAE,EAAE,MAAM,OAAO;QACtC;QACA,IAAI,CAAC,SAAS;YACV,OAAO;QACX;QACA,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE;IACnB;IACJ,aAAa,uJAAA,CAAA,qBAAkB,CAC1B,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IACtB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;YAChD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,+JAAA,CAAA,SAAM,CAAC,QAAQ,EAAE,MAAM,QAAQ;YACzC,SAAS,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAK;oBAAC,IAAI,OAAO,CAAC;oBAAG,IAAI,OAAO,CAAC;iBAAE;QAChE;QACA,OAAO,SAAS,GAAG,CAAC,CAAC,QAAU,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE;IAC3C;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,+JAAA,CAAA,oBAAiB,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9E,IAAI;YACA,MAAM,kBAAkB;gBACpB,GAAG,KAAK;gBACR,MAAM,MAAM,IAAI;YACpB;YACA,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,+JAAA,CAAA,SAAM,EAAE,MAAM,CAAC;YACnC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACX;IACJ;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,+JAAA,CAAA,oBAAiB,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9E,IAAI;YACA,IAAI,CAAC,MAAM,EAAE,EAAE;gBACX,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,kBAAkB;gBACpB,GAAG,KAAK;gBACR,MAAM,MAAM,IAAI;YACpB;YACA,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,+JAAA,CAAA,SAAM,EAAE,GAAG,CAAC,iBAAiB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,+JAAA,CAAA,SAAM,CAAC,EAAE,EAAE,MAAM,EAAE;YAC7E,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACX;IACJ;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,IAAI;YACA,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,+JAAA,CAAA,SAAM,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,+JAAA,CAAA,SAAM,CAAC,EAAE,EAAE,MAAM,OAAO;YAC7D,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACX;IACJ;AACR", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/invitation.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport {\r\n    authUsers,\r\n    createDefaultUserCan<PERSON>,\r\n    fromAuthUser,\r\n    projectInvitationInsertSchema,\r\n    projectInvitations,\r\n    userCanvases,\r\n    userProjects,\r\n} from '@onlook/db';\r\nimport { getResendClient, sendInvitationEmail } from '@onlook/email';\r\nimport { ProjectRole } from '@onlook/models';\r\nimport { isFreeEmail } from '@onlook/utility';\r\nimport { TRPCError } from '@trpc/server';\r\nimport dayjs from 'dayjs';\r\nimport { and, eq, ilike, isNull } from 'drizzle-orm';\r\nimport urlJoin from 'url-join';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const invitationRouter = createTRPCRouter({\r\n    get: protectedProcedure.input(z.object({ id: z.string() })).query(async ({ ctx, input }) => {\r\n        const invitation = await ctx.db.query.projectInvitations.findFirst({\r\n            where: eq(projectInvitations.id, input.id),\r\n        });\r\n\r\n        if (!invitation) {\r\n            throw new TRPCError({\r\n                code: 'NOT_FOUND',\r\n                message: 'Invitation not found',\r\n            });\r\n        }\r\n\r\n        const inviter = await ctx.db.query.authUsers.findFirst({\r\n            where: eq(authUsers.id, invitation.inviterId),\r\n        });\r\n\r\n        if (!inviter) {\r\n            throw new TRPCError({\r\n                code: 'NOT_FOUND',\r\n                message: 'Inviter not found',\r\n            });\r\n        }\r\n\r\n        return {\r\n            ...invitation,\r\n            inviter: fromAuthUser(inviter),\r\n        };\r\n    }),\r\n    list: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const invitations = await ctx.db.query.projectInvitations.findMany({\r\n                where: eq(projectInvitations.projectId, input.projectId),\r\n            });\r\n\r\n            return invitations;\r\n        }),\r\n    create: protectedProcedure\r\n        .input(\r\n            projectInvitationInsertSchema.pick({\r\n                projectId: true,\r\n                inviteeEmail: true,\r\n                role: true,\r\n            }),\r\n        )\r\n        .mutation(async ({ ctx, input }) => {\r\n            if (!ctx.user.id) {\r\n                throw new TRPCError({\r\n                    code: 'UNAUTHORIZED',\r\n                    message: 'You must be logged in to invite a user',\r\n                });\r\n            }\r\n\r\n            const invitation = await ctx.db\r\n                .transaction(async (tx) => {\r\n                    const existingUser = await tx\r\n                        .select()\r\n                        .from(userProjects)\r\n                        .innerJoin(authUsers, eq(authUsers.id, userProjects.userId))\r\n                        .where(\r\n                            and(\r\n                                eq(userProjects.projectId, input.projectId),\r\n                                eq(authUsers.email, input.inviteeEmail),\r\n                            ),\r\n                        )\r\n                        .limit(1);\r\n\r\n                    if (existingUser.length > 0) {\r\n                        throw new TRPCError({\r\n                            code: 'CONFLICT',\r\n                            message: 'User is already a member of the project',\r\n                        });\r\n                    }\r\n\r\n                    return await tx\r\n                        .insert(projectInvitations)\r\n                        .values([\r\n                            {\r\n                                ...input,\r\n                                role: input.role as ProjectRole,\r\n                                token: uuidv4(),\r\n                                inviterId: ctx.user.id,\r\n                                expiresAt: dayjs().add(7, 'day').toDate(),\r\n                            },\r\n                        ])\r\n                        .returning();\r\n                })\r\n                .then(([invitation]) => invitation);\r\n\r\n            if (invitation) {\r\n                if (!env.RESEND_API_KEY) {\r\n                    throw new TRPCError({\r\n                        code: 'INTERNAL_SERVER_ERROR',\r\n                        message: 'RESEND_API_KEY is not set, cannot send email',\r\n                    });\r\n                }\r\n                const emailClient = getResendClient({\r\n                    apiKey: env.RESEND_API_KEY,\r\n                });\r\n\r\n                await sendInvitationEmail(\r\n                    emailClient,\r\n                    {\r\n                        invitedByEmail: ctx.user.email,\r\n                        inviteLink: urlJoin(\r\n                            env.NEXT_PUBLIC_SITE_URL,\r\n                            'invitation',\r\n                            invitation.id,\r\n                            new URLSearchParams([['token', invitation.token]]).toString(),\r\n                        ),\r\n                    },\r\n                    {\r\n                        dryRun: process.env.NODE_ENV !== 'production',\r\n                    },\r\n                );\r\n            }\r\n\r\n            return invitation;\r\n        }),\r\n    delete: protectedProcedure\r\n        .input(z.object({ id: z.string() }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            await ctx.db.delete(projectInvitations).where(eq(projectInvitations.id, input.id));\r\n\r\n            return true;\r\n        }),\r\n    accept: protectedProcedure\r\n        .input(z.object({ token: z.string(), id: z.string() }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            if (!ctx.user.id) {\r\n                throw new TRPCError({\r\n                    code: 'UNAUTHORIZED',\r\n                    message: 'You must be logged in to accept an invitation',\r\n                });\r\n            }\r\n\r\n            const invitation = await ctx.db.query.projectInvitations.findFirst({\r\n                where: and(\r\n                    eq(projectInvitations.id, input.id),\r\n                    eq(projectInvitations.token, input.token),\r\n                    eq(projectInvitations.inviteeEmail, ctx.user.email),\r\n                ),\r\n                with: {\r\n                    project: {\r\n                        with: {\r\n                            canvas: true,\r\n                        },\r\n                    },\r\n                },\r\n            });\r\n\r\n            if (!invitation || dayjs().isAfter(dayjs(invitation.expiresAt))) {\r\n                if (invitation) {\r\n                    await ctx.db\r\n                        .delete(projectInvitations)\r\n                        .where(eq(projectInvitations.id, invitation.id));\r\n                }\r\n\r\n                throw new TRPCError({\r\n                    code: 'BAD_REQUEST',\r\n                    message: 'Invitation does not exist or has expired',\r\n                });\r\n            }\r\n\r\n            await ctx.db.transaction(async (tx) => {\r\n                await tx.delete(projectInvitations).where(eq(projectInvitations.id, invitation.id));\r\n\r\n                await tx\r\n                    .insert(userProjects)\r\n                    .values({\r\n                        projectId: invitation.projectId,\r\n                        userId: ctx.user.id,\r\n                        role: invitation.role,\r\n                    })\r\n                    .onConflictDoNothing();\r\n\r\n                await tx\r\n                    .insert(userCanvases)\r\n                    .values(createDefaultUserCanvas(ctx.user.id, invitation.project.canvas.id))\r\n                    .onConflictDoNothing();\r\n            });\r\n        }),\r\n    suggested: protectedProcedure\r\n        .input(z.object({ projectId: z.string() }))\r\n        .query(async ({ ctx, input }) => {\r\n            if (isFreeEmail(ctx.user.email)) {\r\n                return [];\r\n            }\r\n            const domain = ctx.user.email.split('@').at(-1);\r\n\r\n            const suggestedUsers = await ctx.db\r\n                .select()\r\n                .from(authUsers)\r\n                .leftJoin(\r\n                    userProjects,\r\n                    and(\r\n                        eq(userProjects.userId, authUsers.id),\r\n                        eq(userProjects.projectId, input.projectId),\r\n                    ),\r\n                )\r\n                .leftJoin(\r\n                    projectInvitations,\r\n                    and(\r\n                        eq(projectInvitations.inviteeEmail, authUsers.email),\r\n                        eq(projectInvitations.projectId, input.projectId),\r\n                    ),\r\n                )\r\n                .where(\r\n                    and(\r\n                        ilike(authUsers.email, `%@${domain}`),\r\n                        isNull(userProjects.userId), // Not in the project\r\n                        isNull(projectInvitations.id), // Not invited\r\n                    ),\r\n                )\r\n                .limit(5);\r\n\r\n            return suggestedUsers.map((user) => user.users.email);\r\n        }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7C,KAAK,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAAI,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACnF,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC;YAC/D,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE;QAC7C;QAEA,IAAI,CAAC,YAAY;YACb,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC;YACnD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,qJAAA,CAAA,YAAS,CAAC,EAAE,EAAE,WAAW,SAAS;QAChD;QAEA,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,OAAO;YACH,GAAG,UAAU;YACb,SAAS,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE;QAC1B;IACJ;IACA,MAAM,uJAAA,CAAA,qBAAkB,CACnB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,cAAc,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC/D,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,SAAS,EAAE,MAAM,SAAS;QAC3D;QAEA,OAAO;IACX;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CACF,0JAAA,CAAA,gCAA6B,CAAC,IAAI,CAAC;QAC/B,WAAW;QACX,cAAc;QACd,MAAM;IACV,IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,aAAa,MAAM,IAAI,EAAE,CAC1B,WAAW,CAAC,OAAO;YAChB,MAAM,eAAe,MAAM,GACtB,MAAM,GACN,IAAI,CAAC,4JAAA,CAAA,eAAY,EACjB,SAAS,CAAC,qJAAA,CAAA,YAAS,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,qJAAA,CAAA,YAAS,CAAC,EAAE,EAAE,4JAAA,CAAA,eAAY,CAAC,MAAM,GACzD,KAAK,CACF,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,SAAS,EAAE,MAAM,SAAS,GAC1C,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,qJAAA,CAAA,YAAS,CAAC,KAAK,EAAE,MAAM,YAAY,IAG7C,KAAK,CAAC;YAEX,IAAI,aAAa,MAAM,GAAG,GAAG;gBACzB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAChB,MAAM;oBACN,SAAS;gBACb;YACJ;YAEA,OAAO,MAAM,GACR,MAAM,CAAC,0JAAA,CAAA,qBAAkB,EACzB,MAAM,CAAC;gBACJ;oBACI,GAAG,KAAK;oBACR,MAAM,MAAM,IAAI;oBAChB,OAAO,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;oBACZ,WAAW,IAAI,IAAI,CAAC,EAAE;oBACtB,WAAW,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,IAAI,GAAG,CAAC,GAAG,OAAO,MAAM;gBAC3C;aACH,EACA,SAAS;QAClB,GACC,IAAI,CAAC,CAAC,CAAC,WAAW,GAAK;QAE5B,IAAI,YAAY;YACZ,IAAI,CAAC,qIAAA,CAAA,MAAG,CAAC,cAAc,EAAE;gBACrB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAChB,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,MAAM,cAAc,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE;gBAChC,QAAQ,qIAAA,CAAA,MAAG,CAAC,cAAc;YAC9B;YAEA,MAAM,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EACpB,aACA;gBACI,gBAAgB,IAAI,IAAI,CAAC,KAAK;gBAC9B,YAAY,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EACd,qIAAA,CAAA,MAAG,CAAC,oBAAoB,EACxB,cACA,WAAW,EAAE,EACb,IAAI,gBAAgB;oBAAC;wBAAC;wBAAS,WAAW,KAAK;qBAAC;iBAAC,EAAE,QAAQ;YAEnE,GACA;gBACI,QAAQ,oDAAyB;YACrC;QAER;QAEA,OAAO;IACX;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,0JAAA,CAAA,qBAAkB,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE;QAEhF,OAAO;IACX;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACnD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC;YAC/D,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACL,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,GAClC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,KAAK,EAAE,MAAM,KAAK,GACxC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,KAAK;YAEtD,MAAM;gBACF,SAAS;oBACL,MAAM;wBACF,QAAQ;oBACZ;gBACJ;YACJ;QACJ;QAEA,IAAI,CAAC,cAAc,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,IAAI,OAAO,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,WAAW,SAAS,IAAI;YAC7D,IAAI,YAAY;gBACZ,MAAM,IAAI,EAAE,CACP,MAAM,CAAC,0JAAA,CAAA,qBAAkB,EACzB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,EAAE,EAAE,WAAW,EAAE;YACtD;YAEA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QAEA,MAAM,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO;YAC5B,MAAM,GAAG,MAAM,CAAC,0JAAA,CAAA,qBAAkB,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,EAAE,EAAE,WAAW,EAAE;YAEjF,MAAM,GACD,MAAM,CAAC,4JAAA,CAAA,eAAY,EACnB,MAAM,CAAC;gBACJ,WAAW,WAAW,SAAS;gBAC/B,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACnB,MAAM,WAAW,IAAI;YACzB,GACC,mBAAmB;YAExB,MAAM,GACD,MAAM,CAAC,2JAAA,CAAA,eAAY,EACnB,MAAM,CAAC,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,MAAM,CAAC,EAAE,GACxE,mBAAmB;QAC5B;IACJ;IACJ,WAAW,uJAAA,CAAA,qBAAkB,CACxB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,IAAI,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG;YAC7B,OAAO,EAAE;QACb;QACA,MAAM,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QAE7C,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAC9B,MAAM,GACN,IAAI,CAAC,qJAAA,CAAA,YAAS,EACd,QAAQ,CACL,4JAAA,CAAA,eAAY,EACZ,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,qJAAA,CAAA,YAAS,CAAC,EAAE,GACpC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,SAAS,EAAE,MAAM,SAAS,IAGjD,QAAQ,CACL,0JAAA,CAAA,qBAAkB,EAClB,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,YAAY,EAAE,qJAAA,CAAA,YAAS,CAAC,KAAK,GACnD,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,SAAS,EAAE,MAAM,SAAS,IAGvD,KAAK,CACF,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,QAAK,AAAD,EAAE,qJAAA,CAAA,YAAS,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,QAAQ,GACpC,CAAA,GAAA,oKAAA,CAAA,SAAM,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,MAAM,GAC1B,CAAA,GAAA,oKAAA,CAAA,SAAM,AAAD,EAAE,0JAAA,CAAA,qBAAkB,CAAC,EAAE,IAGnC,KAAK,CAAC;QAEX,OAAO,eAAe,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,CAAC,KAAK;IACxD;AACR", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/member.ts"], "sourcesContent": ["import { authUsers, fromAuthUser, userProjects } from '@onlook/db';\r\nimport { and, eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const memberRouter = createTRPCRouter({\r\n    list: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const members = await ctx.db\r\n                .select()\r\n                .from(userProjects)\r\n                .innerJoin(authUsers, eq(userProjects.userId, authUsers.id))\r\n                .where(eq(userProjects.projectId, input.projectId));\r\n\r\n            return members.map((member) => {\r\n                return {\r\n                    ...member.user_projects,\r\n                    user: fromAuthUser(member.users),\r\n                };\r\n            });\r\n        }),\r\n    remove: protectedProcedure\r\n        .input(z.object({ userId: z.string(), projectId: z.string() }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            await ctx.db\r\n                .delete(userProjects)\r\n                .where(\r\n                    and(\r\n                        eq(userProjects.userId, input.userId),\r\n                        eq(userProjects.projectId, input.projectId),\r\n                    ),\r\n                );\r\n\r\n            return true;\r\n        }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,MAAM,uJAAA,CAAA,qBAAkB,CACnB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,UAAU,MAAM,IAAI,EAAE,CACvB,MAAM,GACN,IAAI,CAAC,4JAAA,CAAA,eAAY,EACjB,SAAS,CAAC,qJAAA,CAAA,YAAS,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,qJAAA,CAAA,YAAS,CAAC,EAAE,GACzD,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,SAAS,EAAE,MAAM,SAAS;QAErD,OAAO,QAAQ,GAAG,CAAC,CAAC;YAChB,OAAO;gBACH,GAAG,OAAO,aAAa;gBACvB,MAAM,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE,OAAO,KAAK;YACnC;QACJ;IACJ;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAC3D,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,IAAI,EAAE,CACP,MAAM,CAAC,4JAAA,CAAA,eAAY,EACnB,KAAK,CACF,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,MAAM,MAAM,GACpC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,SAAS,EAAE,MAAM,SAAS;QAItD,OAAO;IACX;AACR", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/project.ts"], "sourcesContent": ["import {\r\n    canvases,\r\n    createDefaultCanvas, createDefaultFrame, createDefaultUserCanvas,\r\n    frames,\r\n    projectInsertSchema,\r\n    projects,\r\n    toCanvas,\r\n    toFrame,\r\n    toProject,\r\n    userCanvases,\r\n    userProjects,\r\n    type Canvas,\r\n    type UserCanvas\r\n} from '@onlook/db';\r\nimport { ProjectRole } from '@onlook/models';\r\nimport { eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const projectRouter = createTRPCRouter({\r\n    getFullProject: protectedProcedure\r\n        .input(z.object({ projectId: z.string() }))\r\n        .query(async ({ ctx, input }) => {\r\n            const project = await ctx.db.query.projects.findFirst({\r\n                where: eq(projects.id, input.projectId),\r\n                with: {\r\n                    canvas: {\r\n                        with: {\r\n                            frames: true,\r\n                            userCanvases: {\r\n                                where: eq(userCanvases.userId, ctx.user.id),\r\n                            },\r\n                        },\r\n                    },\r\n                    conversations: {\r\n                        orderBy: (conversations, { desc }) => [desc(conversations.updatedAt)],\r\n                        limit: 1,\r\n                    },\r\n                },\r\n            });\r\n            if (!project) {\r\n                console.error('project not found');\r\n                return null;\r\n            }\r\n            const canvas: Canvas = project.canvas ?? createDefaultCanvas(project.id);\r\n            const userCanvas: UserCanvas = project.canvas?.userCanvases[0] ?? createDefaultUserCanvas(ctx.user.id, canvas.id);\r\n\r\n            return {\r\n                project: toProject(project),\r\n                userCanvas: toCanvas(userCanvas),\r\n                frames: project.canvas?.frames.map(toFrame) ?? [],\r\n            };\r\n        }),\r\n    create: protectedProcedure\r\n        .input(z.object({ project: projectInsertSchema, userId: z.string() }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            return await ctx.db.transaction(async (tx) => {\r\n                // 1. Insert the new project\r\n                const [newProject] = await tx.insert(projects).values(input.project).returning();\r\n                if (!newProject) {\r\n                    throw new Error('Failed to create project in database');\r\n                }\r\n\r\n                // 2. Create the association in the junction table\r\n                await tx.insert(userProjects).values({\r\n                    userId: input.userId,\r\n                    projectId: newProject.id,\r\n                    role: ProjectRole.OWNER,\r\n                });\r\n\r\n                // 3. Create the default canvas\r\n                const newCanvas = createDefaultCanvas(newProject.id);\r\n                await tx.insert(canvases).values(newCanvas);\r\n\r\n                const newUserCanvas = createDefaultUserCanvas(input.userId, newCanvas.id);\r\n                await tx.insert(userCanvases).values(newUserCanvas);\r\n\r\n                // 4. Create the default frame\r\n                const newFrame = createDefaultFrame(newCanvas.id, input.project.sandboxUrl);\r\n                await tx.insert(frames).values(newFrame);\r\n\r\n                return newProject;\r\n            });\r\n        }),\r\n    delete: protectedProcedure\r\n        .input(z.object({ id: z.string() }))\r\n        .mutation(async ({ ctx, input }) => {\r\n            await ctx.db.transaction(async (tx) => {\r\n                await tx.delete(projects).where(eq(projects.id, input.id));\r\n                await tx.delete(userProjects).where(eq(userProjects.projectId, input.id));\r\n            });\r\n        }),\r\n    getPreviewProjects: protectedProcedure\r\n        .input(z.object({ userId: z.string() }))\r\n        .query(async ({ ctx, input }) => {\r\n            const projects = await ctx.db.query.userProjects.findMany({\r\n                where: eq(userProjects.userId, input.userId),\r\n                with: {\r\n                    project: true,\r\n                },\r\n            });\r\n            return projects.map((project) => toProject(project.project));\r\n        }),\r\n    update: protectedProcedure.input(projectInsertSchema).mutation(async ({ ctx, input }) => {\r\n        if (!input.id) {\r\n            throw new Error('Project ID is required');\r\n        }\r\n        await ctx.db.update(projects).set(input).where(eq(projects.id, input.id));\r\n    }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC1C,gBAAgB,uJAAA,CAAA,qBAAkB,CAC7B,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;YAClD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,uJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,MAAM,SAAS;YACtC,MAAM;gBACF,QAAQ;oBACJ,MAAM;wBACF,QAAQ;wBACR,cAAc;4BACV,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,2JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,EAAE;wBAC9C;oBACJ;gBACJ;gBACA,eAAe;oBACX,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,GAAK;4BAAC,KAAK,cAAc,SAAS;yBAAE;oBACrE,OAAO;gBACX;YACJ;QACJ;QACA,IAAI,CAAC,SAAS;YACV,QAAQ,KAAK,CAAC;YACd,OAAO;QACX;QACA,MAAM,SAAiB,QAAQ,MAAM,IAAI,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,EAAE;QACvE,MAAM,aAAyB,QAAQ,MAAM,EAAE,YAAY,CAAC,EAAE,IAAI,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;QAEhH,OAAO;YACH,SAAS,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE;YACnB,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;YACrB,QAAQ,QAAQ,MAAM,EAAE,OAAO,IAAI,uIAAA,CAAA,UAAO,KAAK,EAAE;QACrD;IACJ;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,SAAS,uJAAA,CAAA,sBAAmB;QAAE,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAClE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,OAAO,MAAM,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO;YACnC,4BAA4B;YAC5B,MAAM,CAAC,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC,uJAAA,CAAA,WAAQ,EAAE,MAAM,CAAC,MAAM,OAAO,EAAE,SAAS;YAC9E,IAAI,CAAC,YAAY;gBACb,MAAM,IAAI,MAAM;YACpB;YAEA,kDAAkD;YAClD,MAAM,GAAG,MAAM,CAAC,4JAAA,CAAA,eAAY,EAAE,MAAM,CAAC;gBACjC,QAAQ,MAAM,MAAM;gBACpB,WAAW,WAAW,EAAE;gBACxB,MAAM,8IAAA,CAAA,cAAW,CAAC,KAAK;YAC3B;YAEA,+BAA+B;YAC/B,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,EAAE;YACnD,MAAM,GAAG,MAAM,CAAC,gKAAA,CAAA,WAAQ,EAAE,MAAM,CAAC;YAEjC,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,MAAM,EAAE,UAAU,EAAE;YACxE,MAAM,GAAG,MAAM,CAAC,2JAAA,CAAA,eAAY,EAAE,MAAM,CAAC;YAErC,8BAA8B;YAC9B,MAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU;YAC1E,MAAM,GAAG,MAAM,CAAC,+JAAA,CAAA,SAAM,EAAE,MAAM,CAAC;YAE/B,OAAO;QACX;IACJ;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO;YAC5B,MAAM,GAAG,MAAM,CAAC,uJAAA,CAAA,WAAQ,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,uJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,MAAM,EAAE;YACxD,MAAM,GAAG,MAAM,CAAC,4JAAA,CAAA,eAAY,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,SAAS,EAAE,MAAM,EAAE;QAC3E;IACJ;IACJ,oBAAoB,uJAAA,CAAA,qBAAkB,CACjC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACpC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC;YACtD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,MAAM,MAAM;YAC3C,MAAM;gBACF,SAAS;YACb;QACJ;QACA,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO;IAC9D;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,uJAAA,CAAA,sBAAmB,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAChF,IAAI,CAAC,MAAM,EAAE,EAAE;YACX,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,uJAAA,CAAA,WAAQ,EAAE,GAAG,CAAC,OAAO,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,uJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,MAAM,EAAE;IAC3E;AACJ", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/editor/sandbox/helpers.ts"], "sourcesContent": ["import { JS_FILE_EXTENSIONS, JSX_FILE_EXTENSIONS } from '@onlook/constants';\r\nimport path from 'path';\r\nimport parserEstree from 'prettier/plugins/estree';\r\nimport parserTypescript from 'prettier/plugins/typescript';\r\nimport prettier from 'prettier/standalone';\r\n\r\nconst SANDBOX_ROOT = '/project/sandbox';\r\n\r\nexport function normalizePath(p: string): string {\r\n    let abs = path.isAbsolute(p) ? p : path.join(SANDBOX_ROOT, p);\r\n    let relative = path.relative(SANDBOX_ROOT, abs);\r\n    return relative.replace(/\\\\/g, '/'); // Always POSIX style\r\n}\r\n\r\nexport async function formatContent(filePath: string, content: string): Promise<string> {\r\n    try {\r\n        // Only format if the file is a .ts or .tsx file\r\n        const extension = path.extname(filePath);\r\n        if (!JSX_FILE_EXTENSIONS.includes(extension) && !JS_FILE_EXTENSIONS.includes(extension)) {\r\n            console.log('Skipping formatting for non-TS/TSX file:', filePath);\r\n            return content;\r\n        }\r\n\r\n        // Use browser standalone version with necessary plugins\r\n        const formattedContent = await prettier.format(content, {\r\n            filepath: filePath,\r\n            plugins: [parserEstree, parserTypescript],\r\n            parser: 'typescript',\r\n        });\r\n        return formattedContent;\r\n    } catch (error: any) {\r\n        console.error('Error formatting file:', error);\r\n        return content;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,eAAe;AAEd,SAAS,cAAc,CAAS;IACnC,IAAI,MAAM,iGAAA,CAAA,UAAI,CAAC,UAAU,CAAC,KAAK,IAAI,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,cAAc;IAC3D,IAAI,WAAW,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,cAAc;IAC3C,OAAO,SAAS,OAAO,CAAC,OAAO,MAAM,qBAAqB;AAC9D;AAEO,eAAe,cAAc,QAAgB,EAAE,OAAe;IACjE,IAAI;QACA,gDAAgD;QAChD,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,uIAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC,uIAAA,CAAA,qBAAkB,CAAC,QAAQ,CAAC,YAAY;YACrF,QAAQ,GAAG,CAAC,4CAA4C;YACxD,OAAO;QACX;QAEA,wDAAwD;QACxD,MAAM,mBAAmB,MAAM,4IAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,SAAS;YACpD,UAAU;YACV,SAAS;gBAAC,0JAAA,CAAA,UAAY;gBAAE,kKAAA,CAAA,UAAgB;aAAC;YACzC,QAAQ;QACZ;QACA,OAAO;IACX,EAAE,OAAO,OAAY;QACjB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/store/editor/pages/helper.ts"], "sourcesContent": ["import type { ReaddirEntry, WebSocketSession } from '@codesandbox/sdk';\r\nimport type { PageMetadata, PageNode } from '@onlook/models';\r\nimport { generate, parse, types as t, traverse, type NodePath, type t as T } from '@onlook/parser';\r\nimport { nanoid } from 'nanoid';\r\nimport { formatContent } from '../sandbox/helpers';\r\n\r\nconst DEFAULT_LAYOUT_CONTENT = `export default function Layout({\r\n    children,\r\n}: {\r\n    children: React.ReactNode;\r\n}) {\r\n    return <>{children}</>;\r\n}`;\r\n\r\nexport const normalizeRoute = (route: string): string => {\r\n    return route\r\n        .replace(/\\\\/g, '/') // Replace backslashes with forward slashes\r\n        .replace(/\\/+/g, '/') // Replace multiple slashes with single slash\r\n        .replace(/^\\/|\\/$/g, '') // Remove leading and trailing slashes\r\n        .toLowerCase(); // Ensure lowercase\r\n};\r\n\r\nexport const validateNextJsRoute = (route: string): { valid: boolean; error?: string } => {\r\n    if (!route) {\r\n        return { valid: false, error: 'Page name is required' };\r\n    }\r\n\r\n    // Checks if it's a dynamic route\r\n    const hasMatchingBrackets = /\\[[^\\]]*\\]/.test(route);\r\n    if (hasMatchingBrackets) {\r\n        const dynamicRegex = /^\\[([a-z0-9-]+)\\]$/;\r\n        if (!dynamicRegex.test(route)) {\r\n            return {\r\n                valid: false,\r\n                error: 'Invalid dynamic route format. Example: [id] or [blog]',\r\n            };\r\n        }\r\n        return { valid: true };\r\n    }\r\n\r\n    // For regular routes, allow lowercase letters, numbers, and hyphens\r\n    const validCharRegex = /^[a-z0-9-]+$/;\r\n    if (!validCharRegex.test(route)) {\r\n        return {\r\n            valid: false,\r\n            error: 'Page name can only contain lowercase letters, numbers, and hyphens',\r\n        };\r\n    }\r\n\r\n    return { valid: true };\r\n};\r\n\r\nexport const doesRouteExist = (nodes: PageNode[], route: string): boolean => {\r\n    const normalizedRoute = normalizeRoute(route);\r\n\r\n    const checkNode = (nodes: PageNode[]): boolean => {\r\n        for (const node of nodes) {\r\n            if (normalizeRoute(node.path) === normalizedRoute) {\r\n                return true;\r\n            }\r\n            if (\r\n                Array.isArray(node.children) &&\r\n                node.children.length > 0 &&\r\n                checkNode(node.children)\r\n            ) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    };\r\n\r\n    return checkNode(nodes);\r\n};\r\n\r\nconst IGNORED_DIRECTORIES = ['api', 'components', 'lib', 'utils', 'node_modules'];\r\nconst APP_ROUTER_PATHS = ['src/app', 'app'];\r\nconst PAGES_ROUTER_PATHS = ['src/pages', 'pages'];\r\nconst ALLOWED_EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];\r\nconst ROOT_PAGE_NAME = 'Home';\r\nconst ROOT_PATH_IDENTIFIERS = ['', '/', '.'];\r\nconst ROOT_PAGE_COPY_NAME = 'landing-page-copy';\r\n\r\nconst DEFAULT_PAGE_CONTENT = `export default function Page() {\r\n    return (\r\n        <div className=\"w-full min-h-screen flex items-center justify-center bg-white dark:bg-black transition-colors duration-200 flex-col p-4 gap-[32px]\">\r\n            <div className=\"text-center text-gray-900 dark:text-gray-100 p-4\">\r\n                <h1 className=\"text-4xl md:text-5xl font-semibold mb-4 tracking-tight\">\r\n                    This is a blank page\r\n                </h1>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n`;\r\n\r\nconst getFileExtension = (fileName: string): string => {\r\n    const lastDot = fileName.lastIndexOf('.');\r\n    return lastDot !== -1 ? fileName.substring(lastDot) : '';\r\n};\r\n\r\nconst getBaseName = (filePath: string): string => {\r\n    const parts = filePath.replace(/\\\\/g, '/').split('/');\r\n    return parts[parts.length - 1] || '';\r\n};\r\n\r\nconst getDirName = (filePath: string): string => {\r\n    const parts = filePath.replace(/\\\\/g, '/').split('/');\r\n    return parts.slice(0, -1).join('/');\r\n};\r\n\r\nconst joinPath = (...parts: string[]): string => {\r\n    return parts.filter(Boolean).join('/').replace(/\\/+/g, '/');\r\n};\r\n\r\n// Helper function to extract metadata from file content\r\nconst extractMetadata = async (content: string): Promise<PageMetadata | undefined> => {\r\n    try {\r\n        const ast = parse(content, {\r\n            sourceType: 'module',\r\n            plugins: ['typescript', 'jsx'],\r\n        });\r\n\r\n        let metadata: PageMetadata | undefined;\r\n\r\n        // Helper functions for AST traversal\r\n        const extractObjectValue = (obj: any): any => {\r\n            const result: any = {};\r\n            for (const prop of obj.properties) {\r\n                if (t.isObjectProperty(prop) && t.isIdentifier(prop.key)) {\r\n                    const key = prop.key.name;\r\n                    if (t.isStringLiteral(prop.value)) {\r\n                        result[key] = prop.value.value;\r\n                    } else if (t.isObjectExpression(prop.value)) {\r\n                        result[key] = extractObjectValue(prop.value);\r\n                    } else if (t.isArrayExpression(prop.value)) {\r\n                        result[key] = extractArrayValue(prop.value);\r\n                    }\r\n                }\r\n            }\r\n            return result;\r\n        };\r\n\r\n        const extractArrayValue = (arr: any): any[] => {\r\n            return arr.elements\r\n                .map((element: any) => {\r\n                    if (t.isStringLiteral(element)) {\r\n                        return element.value;\r\n                    } else if (t.isObjectExpression(element)) {\r\n                        return extractObjectValue(element);\r\n                    } else if (t.isArrayExpression(element)) {\r\n                        return extractArrayValue(element);\r\n                    }\r\n                    return null;\r\n                })\r\n                .filter(Boolean);\r\n        };\r\n\r\n        // Traverse the AST to find metadata export\r\n        traverse(ast, {\r\n            ExportNamedDeclaration(path) {\r\n                const declaration = path.node.declaration;\r\n                if (t.isVariableDeclaration(declaration)) {\r\n                    const declarator = declaration.declarations[0];\r\n                    if (\r\n                        declarator &&\r\n                        t.isIdentifier(declarator.id) &&\r\n                        declarator.id.name === 'metadata' &&\r\n                        t.isObjectExpression(declarator.init)\r\n                    ) {\r\n                        metadata = {};\r\n                        // Extract properties from the object expression\r\n                        for (const prop of declarator.init.properties) {\r\n                            if (t.isObjectProperty(prop) && t.isIdentifier(prop.key)) {\r\n                                const key = prop.key.name;\r\n                                if (t.isStringLiteral(prop.value)) {\r\n                                    (metadata as any)[key] = prop.value.value;\r\n                                } else if (t.isObjectExpression(prop.value)) {\r\n                                    (metadata as any)[key] = extractObjectValue(prop.value);\r\n                                } else if (t.isArrayExpression(prop.value)) {\r\n                                    (metadata as any)[key] = extractArrayValue(prop.value);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n        });\r\n\r\n        return metadata;\r\n    } catch (error) {\r\n        console.error(`Error reading metadata:`, error);\r\n        return undefined;\r\n    }\r\n};\r\n\r\nconst scanAppDirectory = async (\r\n    session: WebSocketSession,\r\n    dir: string,\r\n    parentPath: string = '',\r\n): Promise<PageNode[]> => {\r\n    const nodes: PageNode[] = [];\r\n    let entries;\r\n\r\n    try {\r\n        entries = await session.fs.readdir(dir);\r\n    } catch (error) {\r\n        console.error(`Error reading directory ${dir}:`, error);\r\n        return nodes;\r\n    }\r\n\r\n    // Handle page files\r\n    const pageFile = entries.find(\r\n        (entry: any) =>\r\n            entry.type === 'file' &&\r\n            entry.name.startsWith('page.') &&\r\n            ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)),\r\n    );\r\n\r\n    if (pageFile) {\r\n        const currentDir = getBaseName(dir);\r\n        const isDynamicRoute = currentDir.startsWith('[') && currentDir.endsWith(']');\r\n\r\n        let cleanPath;\r\n        if (isDynamicRoute) {\r\n            const paramName = currentDir;\r\n            cleanPath = parentPath ? joinPath(getDirName(parentPath), paramName) : '/' + paramName;\r\n        } else {\r\n            cleanPath = parentPath ? `/${parentPath}` : '/';\r\n        }\r\n\r\n        // Normalize path and ensure leading slash & no trailing slash\r\n        cleanPath = '/' + cleanPath.replace(/^\\/|\\/$/g, '');\r\n\r\n        const isRoot = ROOT_PATH_IDENTIFIERS.includes(cleanPath);\r\n\r\n        // Extract metadata from both page and layout files\r\n        let pageMetadata: PageMetadata | undefined;\r\n        try {\r\n            const pageContent = await session.fs.readTextFile(`${dir}/${pageFile.name}`);\r\n            pageMetadata = await extractMetadata(pageContent);\r\n        } catch (error) {\r\n            console.error(`Error reading page file ${dir}/${pageFile.name}:`, error);\r\n        }\r\n\r\n        // Look for layout file in the same directory\r\n        const layoutFile = entries.find(\r\n            (entry: any) =>\r\n                entry.type === 'file' &&\r\n                entry.name.startsWith('layout.') &&\r\n                ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)),\r\n        );\r\n\r\n        let layoutMetadata: PageMetadata | undefined;\r\n        if (layoutFile) {\r\n            try {\r\n                const layoutContent = await session.fs.readTextFile(`${dir}/${layoutFile.name}`);\r\n                layoutMetadata = await extractMetadata(layoutContent);\r\n            } catch (error) {\r\n                console.error(`Error reading layout file ${dir}/${layoutFile.name}:`, error);\r\n            }\r\n        }\r\n\r\n        // Merge metadata, with page metadata taking precedence over layout metadata\r\n        const metadata = {\r\n            ...layoutMetadata,\r\n            ...pageMetadata,\r\n        };\r\n\r\n        nodes.push({\r\n            id: nanoid(),\r\n            name: isDynamicRoute\r\n                ? currentDir\r\n                : parentPath\r\n                  ? getBaseName(parentPath)\r\n                  : ROOT_PAGE_NAME,\r\n            path: cleanPath,\r\n            children: [],\r\n            isActive: false,\r\n            isRoot,\r\n            metadata: metadata || {},\r\n        });\r\n    }\r\n\r\n    // Handle directories\r\n    for (const entry of entries) {\r\n        if (IGNORED_DIRECTORIES.includes(entry.name)) {\r\n            continue;\r\n        }\r\n\r\n        const fullPath = `${dir}/${entry.name}`;\r\n        const relativePath = joinPath(parentPath, entry.name);\r\n\r\n        if (entry.type === 'directory') {\r\n            const children = await scanAppDirectory(session, fullPath, relativePath);\r\n            if (children.length > 0) {\r\n                const dirPath = relativePath.replace(/\\\\/g, '/');\r\n                const cleanPath = '/' + dirPath.replace(/^\\/|\\/$/g, '');\r\n                nodes.push({\r\n                    id: nanoid(),\r\n                    name: entry.name,\r\n                    path: cleanPath,\r\n                    children,\r\n                    isActive: false,\r\n                    isRoot: false,\r\n                    metadata: {},\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    return nodes;\r\n};\r\n\r\nconst scanPagesDirectory = async (\r\n    session: WebSocketSession,\r\n    dir: string,\r\n    parentPath: string = '',\r\n): Promise<PageNode[]> => {\r\n    const nodes: PageNode[] = [];\r\n    let entries: ReaddirEntry[];\r\n\r\n    try {\r\n        entries = await session.fs.readdir(dir);\r\n    } catch (error) {\r\n        console.error(`Error reading directory ${dir}:`, error);\r\n        return nodes;\r\n    }\r\n\r\n    // Process files first\r\n    for (const entry of entries) {\r\n        const fileName = entry.name?.split('.')[0];\r\n\r\n        if (!fileName) {\r\n            console.error(`Error reading file ${entry.name}`);\r\n            continue;\r\n        }\r\n\r\n        if (\r\n            entry.type === 'file' &&\r\n            ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)) &&\r\n            !IGNORED_DIRECTORIES.includes(fileName)\r\n        ) {\r\n            const isDynamicRoute = fileName.startsWith('[') && fileName.endsWith(']');\r\n\r\n            let cleanPath;\r\n            if (fileName === 'index') {\r\n                cleanPath = parentPath ? `/${parentPath}` : '/';\r\n            } else {\r\n                if (isDynamicRoute) {\r\n                    const paramName = fileName.slice(1, -1);\r\n                    cleanPath = joinPath(parentPath, paramName);\r\n                } else {\r\n                    cleanPath = joinPath(parentPath, fileName);\r\n                }\r\n                // Normalize path\r\n                cleanPath = '/' + cleanPath.replace(/\\\\/g, '/').replace(/^\\/|\\/$/g, '');\r\n            }\r\n\r\n            const isRoot = ROOT_PATH_IDENTIFIERS.includes(cleanPath);\r\n\r\n            // Extract metadata from the page file\r\n            let metadata: PageMetadata | undefined;\r\n            try {\r\n                const fileContent = await session.fs.readTextFile(`${dir}/${entry.name}`);\r\n                metadata = await extractMetadata(fileContent);\r\n            } catch (error) {\r\n                console.error(`Error reading file ${dir}/${entry.name}:`, error);\r\n            }\r\n\r\n            nodes.push({\r\n                id: nanoid(),\r\n                name:\r\n                    fileName === 'index'\r\n                        ? parentPath\r\n                            ? `/${getBaseName(parentPath)}`\r\n                            : ROOT_PAGE_NAME\r\n                        : '/' + fileName,\r\n                path: cleanPath,\r\n                children: [],\r\n                isActive: false,\r\n                isRoot,\r\n                metadata: metadata || {},\r\n            });\r\n        }\r\n    }\r\n\r\n    // Process directories\r\n    for (const entry of entries) {\r\n        if (IGNORED_DIRECTORIES.includes(entry.name)) {\r\n            continue;\r\n        }\r\n\r\n        const fullPath = `${dir}/${entry.name}`;\r\n        const isDynamicDir = entry.name.startsWith('[') && entry.name.endsWith(']');\r\n\r\n        const dirNameForPath = isDynamicDir ? entry.name.slice(1, -1) : entry.name;\r\n        const relativePath = joinPath(parentPath, dirNameForPath);\r\n\r\n        if (entry.type === 'directory') {\r\n            const children = await scanPagesDirectory(session, fullPath, relativePath);\r\n            if (children.length > 0) {\r\n                const dirPath = relativePath.replace(/\\\\/g, '/');\r\n                const cleanPath = '/' + dirPath.replace(/^\\/|\\/$/g, '');\r\n                nodes.push({\r\n                    id: nanoid(),\r\n                    name: entry.name,\r\n                    path: cleanPath,\r\n                    children,\r\n                    isActive: false,\r\n                    isRoot: false,\r\n                    metadata: {},\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    return nodes;\r\n};\r\n\r\nexport const scanPagesFromSandbox = async (session: WebSocketSession): Promise<PageNode[]> => {\r\n    if (!session) {\r\n        throw new Error('No sandbox session available');\r\n    }\r\n\r\n    // Detect router configuration\r\n    let routerConfig: { type: 'app' | 'pages'; basePath: string } | null = null;\r\n\r\n    // Check for App Router first (Next.js 13+)\r\n    for (const appPath of APP_ROUTER_PATHS) {\r\n        try {\r\n            const entries = await session.fs.readdir(appPath);\r\n            if (entries && entries.length > 0) {\r\n                console.log(`Found App Router at: ${appPath}`);\r\n                routerConfig = { type: 'app', basePath: appPath };\r\n                break;\r\n            }\r\n        } catch (error) {\r\n            // Directory doesn't exist, continue checking\r\n        }\r\n    }\r\n\r\n    // Check for Pages Router if App Router not found\r\n    if (!routerConfig) {\r\n        for (const pagesPath of PAGES_ROUTER_PATHS) {\r\n            try {\r\n                const entries = await session.fs.readdir(pagesPath);\r\n                if (entries && entries.length > 0) {\r\n                    console.log(`Found Pages Router at: ${pagesPath}`);\r\n                    routerConfig = { type: 'pages', basePath: pagesPath };\r\n                    break;\r\n                }\r\n            } catch (error) {\r\n                // Directory doesn't exist, continue checking\r\n            }\r\n        }\r\n    }\r\n\r\n    if (!routerConfig) {\r\n        console.log('No Next.js router detected, returning empty pages');\r\n        return [];\r\n    }\r\n\r\n    if (routerConfig.type === 'app') {\r\n        return await scanAppDirectory(session, routerConfig.basePath);\r\n    } else {\r\n        return await scanPagesDirectory(session, routerConfig.basePath);\r\n    }\r\n};\r\n\r\nconst detectRouterTypeInSandbox = async (\r\n    session: WebSocketSession,\r\n): Promise<{ type: 'app' | 'pages'; basePath: string } | null> => {\r\n    // Check for App Router\r\n    for (const appPath of APP_ROUTER_PATHS) {\r\n        try {\r\n            const entries = await session.fs.readdir(appPath);\r\n            if (entries && entries.length > 0) {\r\n                // Check for layout file (required for App Router)\r\n                const hasLayout = entries.some(\r\n                    (entry: any) =>\r\n                        entry.type === 'file' &&\r\n                        entry.name.startsWith('layout.') &&\r\n                        ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)),\r\n                );\r\n\r\n                if (hasLayout) {\r\n                    console.log(`Found App Router at: ${appPath}`);\r\n                    return { type: 'app', basePath: appPath };\r\n                }\r\n            }\r\n        } catch (error) {\r\n            // Directory doesn't exist, continue checking\r\n        }\r\n    }\r\n\r\n    // Check for Pages Router if App Router not found\r\n    for (const pagesPath of PAGES_ROUTER_PATHS) {\r\n        try {\r\n            const entries = await session.fs.readdir(pagesPath);\r\n            if (entries && entries.length > 0) {\r\n                // Check for index file (common in Pages Router)\r\n                const hasIndex = entries.some(\r\n                    (entry: any) =>\r\n                        entry.type === 'file' &&\r\n                        entry.name.startsWith('index.') &&\r\n                        ALLOWED_EXTENSIONS.includes(getFileExtension(entry.name)),\r\n                );\r\n\r\n                if (hasIndex) {\r\n                    console.log(`Found Pages Router at: ${pagesPath}`);\r\n                    return { type: 'pages', basePath: pagesPath };\r\n                }\r\n            }\r\n        } catch (error) {\r\n            // Directory doesn't exist, continue checking\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\n// checks if file/directory exists\r\nconst pathExists = async (session: WebSocketSession, filePath: string): Promise<boolean> => {\r\n    try {\r\n        await session.fs.readdir(getDirName(filePath));\r\n        const dirEntries = await session.fs.readdir(getDirName(filePath));\r\n        const fileName = getBaseName(filePath);\r\n        return dirEntries.some((entry: any) => entry.name === fileName);\r\n    } catch (error) {\r\n        return false;\r\n    }\r\n};\r\n\r\nconst cleanupEmptyFolders = async (\r\n    session: WebSocketSession,\r\n    folderPath: string,\r\n): Promise<void> => {\r\n    while (folderPath && folderPath !== getDirName(folderPath)) {\r\n        try {\r\n            const entries = await session.fs.readdir(folderPath);\r\n            if (entries.length === 0) {\r\n                // Delete empty directory using remove method\r\n                await session.fs.remove(folderPath);\r\n                folderPath = getDirName(folderPath);\r\n            } else {\r\n                break;\r\n            }\r\n        } catch (error) {\r\n            // Directory doesn't exist or can't be accessed\r\n            break;\r\n        }\r\n    }\r\n};\r\n\r\nconst getUniqueDir = async (\r\n    session: WebSocketSession,\r\n    basePath: string,\r\n    dirName: string,\r\n    maxAttempts = 100,\r\n): Promise<string> => {\r\n    let uniquePath = dirName;\r\n    let counter = 1;\r\n\r\n    const baseName = dirName.replace(/-copy(-\\d+)?$/, '');\r\n\r\n    while (counter <= maxAttempts) {\r\n        const fullPath = joinPath(basePath, uniquePath);\r\n        if (!(await pathExists(session, fullPath))) {\r\n            return uniquePath;\r\n        }\r\n        uniquePath = `${baseName}-copy-${counter}`;\r\n        counter++;\r\n    }\r\n\r\n    throw new Error(`Unable to find available directory name for ${dirName}`);\r\n};\r\n\r\nconst createDirectory = async (session: WebSocketSession, dirPath: string): Promise<void> => {\r\n    // Creates a temporary file to ensure directory structure exists, then remove it\r\n    const tempFile = joinPath(dirPath, '.temp');\r\n    await session.fs.writeTextFile(tempFile, '');\r\n    await session.fs.remove(tempFile);\r\n};\r\n\r\nexport const createPageInSandbox = async (\r\n    session: WebSocketSession,\r\n    pagePath: string,\r\n): Promise<void> => {\r\n    try {\r\n        const routerConfig = await detectRouterTypeInSandbox(session);\r\n\r\n        if (!routerConfig) {\r\n            throw new Error('Could not detect Next.js router type');\r\n        }\r\n\r\n        if (routerConfig.type !== 'app') {\r\n            throw new Error('Page creation is only supported for App Router projects.');\r\n        }\r\n\r\n        // Validate and normalize the path\r\n        const normalizedPagePath = pagePath.replace(/\\/+/g, '/').replace(/^\\/|\\/$/g, '');\r\n        if (!/^[a-zA-Z0-9\\-_[\\]()/]+$/.test(normalizedPagePath)) {\r\n            throw new Error('Page path contains invalid characters');\r\n        }\r\n\r\n        const fullPath = joinPath(routerConfig.basePath, normalizedPagePath);\r\n        const pageFilePath = joinPath(fullPath, 'page.tsx');\r\n\r\n        if (await pathExists(session, pageFilePath)) {\r\n            throw new Error('Page already exists at this path');\r\n        }\r\n\r\n        await session.fs.writeTextFile(pageFilePath, DEFAULT_PAGE_CONTENT);\r\n\r\n        console.log(`Created page at: ${pageFilePath}`);\r\n    } catch (error) {\r\n        console.error('Error creating page:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const deletePageInSandbox = async (\r\n    session: WebSocketSession,\r\n    pagePath: string,\r\n    isDir: boolean,\r\n): Promise<void> => {\r\n    try {\r\n        const routerConfig = await detectRouterTypeInSandbox(session);\r\n\r\n        if (!routerConfig) {\r\n            throw new Error('Could not detect Next.js router type');\r\n        }\r\n\r\n        if (routerConfig.type !== 'app') {\r\n            throw new Error('Page deletion is only supported for App Router projects.');\r\n        }\r\n\r\n        const normalizedPath = pagePath.replace(/\\/+/g, '/').replace(/^\\/|\\/$/g, '');\r\n        if (normalizedPath === '' || normalizedPath === '/') {\r\n            throw new Error('Cannot delete root page');\r\n        }\r\n\r\n        const fullPath = joinPath(routerConfig.basePath, normalizedPath);\r\n\r\n        if (!(await pathExists(session, fullPath))) {\r\n            throw new Error('Selected page not found');\r\n        }\r\n\r\n        if (isDir) {\r\n            // Delete entire directory\r\n            await session.fs.remove(fullPath, true);\r\n        } else {\r\n            // Delete just the page.tsx file\r\n            const pageFilePath = joinPath(fullPath, 'page.tsx');\r\n            await session.fs.remove(pageFilePath);\r\n\r\n            // Clean up empty parent directories\r\n            await cleanupEmptyFolders(session, fullPath);\r\n        }\r\n\r\n        console.log(`Deleted: ${fullPath}`);\r\n    } catch (error) {\r\n        console.error('Error deleting page:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const renamePageInSandbox = async (\r\n    session: WebSocketSession,\r\n    oldPath: string,\r\n    newName: string,\r\n): Promise<void> => {\r\n    try {\r\n        const routerConfig = await detectRouterTypeInSandbox(session);\r\n\r\n        if (!routerConfig || routerConfig.type !== 'app') {\r\n            throw new Error('Page renaming is only supported for App Router projects.');\r\n        }\r\n\r\n        if (ROOT_PATH_IDENTIFIERS.includes(oldPath)) {\r\n            throw new Error('Cannot rename root page');\r\n        }\r\n\r\n        // Validate new name\r\n        if (!/^[a-zA-Z0-9\\-_[\\]()]+$/.test(newName)) {\r\n            throw new Error('Page name contains invalid characters');\r\n        }\r\n\r\n        const normalizedOldPath = oldPath.replace(/\\/+/g, '/').replace(/^\\/|\\/$/g, '');\r\n        const oldFullPath = joinPath(routerConfig.basePath, normalizedOldPath);\r\n        const parentDir = getDirName(oldFullPath);\r\n        const newFullPath = joinPath(parentDir, newName);\r\n\r\n        if (!(await pathExists(session, oldFullPath))) {\r\n            throw new Error(`Source page not found: ${oldFullPath}`);\r\n        }\r\n\r\n        if (await pathExists(session, newFullPath)) {\r\n            throw new Error(`Target path already exists: ${newFullPath}`);\r\n        }\r\n\r\n        await session.fs.rename(oldFullPath, newFullPath);\r\n\r\n        console.log(`Renamed page from ${oldFullPath} to ${newFullPath}`);\r\n    } catch (error) {\r\n        console.error('Error renaming page:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const duplicatePageInSandbox = async (\r\n    session: WebSocketSession,\r\n    sourcePath: string,\r\n    targetPath: string,\r\n): Promise<void> => {\r\n    try {\r\n        const routerConfig = await detectRouterTypeInSandbox(session);\r\n\r\n        if (!routerConfig || routerConfig.type !== 'app') {\r\n            throw new Error('Page duplication is only supported for App Router projects.');\r\n        }\r\n\r\n        // Handle root path case\r\n        const isRootPath = ROOT_PATH_IDENTIFIERS.includes(sourcePath);\r\n\r\n        if (isRootPath) {\r\n            const sourcePageFile = joinPath(routerConfig.basePath, 'page.tsx');\r\n            const targetDir = await getUniqueDir(\r\n                session,\r\n                routerConfig.basePath,\r\n                ROOT_PAGE_COPY_NAME,\r\n            );\r\n            const targetDirPath = joinPath(routerConfig.basePath, targetDir);\r\n            const targetPageFile = joinPath(targetDirPath, 'page.tsx');\r\n\r\n            if (await pathExists(session, targetDirPath)) {\r\n                throw new Error('Target path already exists');\r\n            }\r\n\r\n            await session.fs.copy(sourcePageFile, targetPageFile);\r\n\r\n            console.log(`Duplicated root page to: ${targetPageFile}`);\r\n            return;\r\n        }\r\n\r\n        // Handle non-root pages\r\n        const normalizedSourcePath = sourcePath.replace(/\\/+/g, '/').replace(/^\\/|\\/$/g, '');\r\n        const normalizedTargetPath = await getUniqueDir(session, routerConfig.basePath, targetPath);\r\n\r\n        const sourceFull = joinPath(routerConfig.basePath, normalizedSourcePath);\r\n        const targetFull = joinPath(routerConfig.basePath, normalizedTargetPath);\r\n\r\n        if (await pathExists(session, targetFull)) {\r\n            throw new Error('Target path already exists');\r\n        }\r\n\r\n        // Check if source is a directory or file\r\n        const sourceEntries = await session.fs.readdir(getDirName(sourceFull));\r\n        const sourceEntry = sourceEntries.find(\r\n            (entry: any) => entry.name === getBaseName(sourceFull),\r\n        );\r\n\r\n        if (!sourceEntry) {\r\n            throw new Error('Source page not found');\r\n        }\r\n\r\n        await session.fs.copy(sourceFull, targetFull, true);\r\n\r\n        console.log(`Duplicated page from ${sourceFull} to ${targetFull}`);\r\n    } catch (error) {\r\n        console.error('Error duplicating page:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const updatePageMetadataInSandbox = async (\r\n    session: WebSocketSession,\r\n    pagePath: string,\r\n    metadata: PageMetadata,\r\n): Promise<void> => {\r\n    const routerConfig = await detectRouterTypeInSandbox(session);\r\n\r\n    if (!routerConfig) {\r\n        throw new Error('Could not detect Next.js router type');\r\n    }\r\n\r\n    if (routerConfig.type !== 'app') {\r\n        throw new Error('Metadata update is only supported for App Router projects for now.');\r\n    }\r\n\r\n    const fullPath = joinPath(routerConfig.basePath, pagePath);\r\n    const pageFilePath = joinPath(fullPath, 'page.tsx');\r\n    // check if page.tsx exists\r\n    const pageExists = await pathExists(session, pageFilePath);\r\n\r\n    if (!pageExists) {\r\n        throw new Error('Page not found');\r\n    }\r\n\r\n    const pageContent = await session.fs.readTextFile(pageFilePath);\r\n    const hasUseClient =\r\n        pageContent.includes(\"'use client'\") || pageContent.includes('\"use client\"');\r\n\r\n    if (hasUseClient) {\r\n        // check if layout.tsx exists\r\n        const layoutFilePath = joinPath(fullPath, 'layout.tsx');\r\n        const layoutExists = await pathExists(session, layoutFilePath);\r\n\r\n        if (layoutExists) {\r\n            await updateMetadataInFile(session, layoutFilePath, metadata);\r\n        } else {\r\n            // create layout.tsx\r\n            // Create new layout file with metadata\r\n            const layoutContent = `import type { Metadata } from 'next';\\n\\nexport const metadata: Metadata = ${JSON.stringify(metadata, null, 2)};\\n\\n${DEFAULT_LAYOUT_CONTENT}`;\r\n            await session.fs.writeTextFile(layoutFilePath, layoutContent);\r\n        }\r\n    } else {\r\n        await updateMetadataInFile(session, pageFilePath, metadata);\r\n    }\r\n};\r\n\r\nasync function updateMetadataInFile(\r\n    session: WebSocketSession,\r\n    filePath: string,\r\n    metadata: PageMetadata,\r\n) {\r\n    // Read the current file content\r\n    const content = await session.fs.readTextFile(filePath);\r\n\r\n    // Parse the file content using Babel\r\n    const ast = parse(content, {\r\n        sourceType: 'module',\r\n        plugins: ['typescript', 'jsx'],\r\n    });\r\n\r\n    let hasMetadataImport = false;\r\n    let metadataNode: T.ExportNamedDeclaration | null = null;\r\n\r\n    // Traverse the AST to find metadata import and export\r\n    traverse(ast, {\r\n        ImportDeclaration(path) {\r\n            if (\r\n                path.node.source.value === 'next' &&\r\n                path.node.specifiers.some(\r\n                    (spec) =>\r\n                        t.isImportSpecifier(spec) &&\r\n                        t.isIdentifier(spec.imported) &&\r\n                        spec.imported.name === 'Metadata',\r\n                )\r\n            ) {\r\n                hasMetadataImport = true;\r\n            }\r\n        },\r\n        ExportNamedDeclaration(path) {\r\n            const declaration = path.node.declaration;\r\n            if (t.isVariableDeclaration(declaration)) {\r\n                const declarator = declaration.declarations[0];\r\n                if (\r\n                    declarator &&\r\n                    t.isIdentifier(declarator.id) &&\r\n                    declarator.id.name === 'metadata'\r\n                ) {\r\n                    metadataNode = path.node;\r\n                }\r\n            }\r\n        },\r\n    });\r\n\r\n    // Add Metadata import if not present\r\n    if (!hasMetadataImport) {\r\n        const metadataImport = t.importDeclaration(\r\n            [t.importSpecifier(t.identifier('Metadata'), t.identifier('Metadata'))],\r\n            t.stringLiteral('next'),\r\n        );\r\n        ast.program.body.unshift(metadataImport);\r\n    }\r\n    // Create metadata object expression\r\n    const metadataObject = t.objectExpression(\r\n        Object.entries(metadata).map(([key, value]) => {\r\n            if (typeof value === 'string') {\r\n                if (key === 'metadataBase') {\r\n                    return t.objectProperty(\r\n                        t.identifier(key),\r\n                        t.newExpression(t.identifier('URL'), [t.stringLiteral(value)]),\r\n                    );\r\n                }\r\n                return t.objectProperty(t.identifier(key), t.stringLiteral(value));\r\n            } else if (value === null) {\r\n                return t.objectProperty(t.identifier(key), t.nullLiteral());\r\n            } else if (Array.isArray(value)) {\r\n                return t.objectProperty(\r\n                    t.identifier(key),\r\n                    t.arrayExpression(\r\n                        value.map((v) => {\r\n                            if (typeof v === 'string') {\r\n                                return t.stringLiteral(v);\r\n                            } else if (typeof v === 'object' && v !== null) {\r\n                                return t.objectExpression(\r\n                                    Object.entries(v).map(([k, val]) => {\r\n                                        if (typeof val === 'string') {\r\n                                            return t.objectProperty(\r\n                                                t.identifier(k),\r\n                                                t.stringLiteral(val),\r\n                                            );\r\n                                        } else if (typeof val === 'number') {\r\n                                            return t.objectProperty(\r\n                                                t.identifier(k),\r\n                                                t.numericLiteral(val),\r\n                                            );\r\n                                        }\r\n                                        return t.objectProperty(\r\n                                            t.identifier(k),\r\n                                            t.stringLiteral(String(val)),\r\n                                        );\r\n                                    }),\r\n                                );\r\n                            }\r\n                            return t.stringLiteral(String(v));\r\n                        }),\r\n                    ),\r\n                );\r\n            } else if (typeof value === 'object' && value !== null) {\r\n                return t.objectProperty(\r\n                    t.identifier(key),\r\n                    t.objectExpression(\r\n                        Object.entries(value).map(([k, v]) => {\r\n                            if (typeof v === 'string') {\r\n                                return t.objectProperty(t.identifier(k), t.stringLiteral(v));\r\n                            } else if (typeof v === 'number') {\r\n                                return t.objectProperty(t.identifier(k), t.numericLiteral(v));\r\n                            } else if (Array.isArray(v)) {\r\n                                return t.objectProperty(\r\n                                    t.identifier(k),\r\n                                    t.arrayExpression(\r\n                                        v.map((item) => {\r\n                                            if (typeof item === 'string') {\r\n                                                return t.stringLiteral(item);\r\n                                            } else if (typeof item === 'object' && item !== null) {\r\n                                                return t.objectExpression(\r\n                                                    Object.entries(item).map(([ik, iv]) => {\r\n                                                        if (typeof iv === 'string') {\r\n                                                            return t.objectProperty(\r\n                                                                t.identifier(ik),\r\n                                                                t.stringLiteral(iv),\r\n                                                            );\r\n                                                        } else if (typeof iv === 'number') {\r\n                                                            return t.objectProperty(\r\n                                                                t.identifier(ik),\r\n                                                                t.numericLiteral(iv),\r\n                                                            );\r\n                                                        }\r\n                                                        return t.objectProperty(\r\n                                                            t.identifier(ik),\r\n                                                            t.stringLiteral(String(iv)),\r\n                                                        );\r\n                                                    }),\r\n                                                );\r\n                                            }\r\n                                            return t.stringLiteral(String(item));\r\n                                        }),\r\n                                    ),\r\n                                );\r\n                            }\r\n                            return t.objectProperty(t.identifier(k), t.stringLiteral(String(v)));\r\n                        }),\r\n                    ),\r\n                );\r\n            }\r\n            return t.objectProperty(t.identifier(key), t.stringLiteral(String(value)));\r\n        }),\r\n    );\r\n\r\n    // Create metadata variable declaration\r\n    const metadataVarDecl = t.variableDeclaration('const', [\r\n        t.variableDeclarator(t.identifier('metadata'), metadataObject),\r\n    ]);\r\n\r\n    // Add type annotation\r\n    const metadataTypeAnnotation = t.tsTypeAnnotation(t.tsTypeReference(t.identifier('Metadata')));\r\n    (metadataVarDecl.declarations[0]?.id as T.Identifier).typeAnnotation = metadataTypeAnnotation;\r\n\r\n    // Create metadata export\r\n    const metadataExport = t.exportNamedDeclaration(metadataVarDecl);\r\n\r\n    if (metadataNode) {\r\n        // Replace existing metadata export\r\n        const metadataExportIndex = ast.program.body.findIndex((node) => {\r\n            if (!t.isExportNamedDeclaration(node) || !t.isVariableDeclaration(node.declaration)) {\r\n                return false;\r\n            }\r\n            const declarator = node.declaration.declarations[0];\r\n            return t.isIdentifier(declarator?.id) && declarator.id.name === 'metadata';\r\n        });\r\n\r\n        if (metadataExportIndex !== -1) {\r\n            ast.program.body[metadataExportIndex] = metadataExport;\r\n        }\r\n    } else {\r\n        // Find the default export and add metadata before it\r\n        const defaultExportIndex = ast.program.body.findIndex((node) =>\r\n            t.isExportDefaultDeclaration(node),\r\n        );\r\n\r\n        if (defaultExportIndex === -1) {\r\n            throw new Error('Could not find default export in the file');\r\n        }\r\n\r\n        ast.program.body.splice(defaultExportIndex, 0, metadataExport);\r\n    }\r\n\r\n    // Generate the updated code\r\n    const { code } = generate(ast);\r\n\r\n    const formattedContent = await formatContent(filePath, code);\r\n\r\n    // Write the updated content back to the file\r\n    await session.fs.writeTextFile(filePath, formattedContent);\r\n}\r\n\r\nexport const injectPreloadScript = async (session: WebSocketSession) => {\r\n    await addSetupTask(session);\r\n    await updatePackageJson(session);\r\n\r\n    // Step 3: Inject script tag\r\n    const routerType = await detectRouterTypeInSandbox(session);\r\n    const preLoadScript =\r\n        'https://cdn.jsdelivr.net/gh/onlook-dev/web@latest/apps/web/preload/dist/index.js';\r\n\r\n    if (!routerType || routerType.type !== 'app') {\r\n        throw new Error('We currently support only Next.js App projects.');\r\n    }\r\n\r\n    const layoutPath = './src/app/layout.tsx';\r\n    const layoutRaw = await session.fs.readFile(layoutPath);\r\n    const layoutSrc = new TextDecoder().decode(layoutRaw);\r\n\r\n    const ast = parse(layoutSrc, {\r\n        sourceType: 'module',\r\n        plugins: ['typescript', 'jsx'],\r\n    });\r\n\r\n    let importedScript = false;\r\n    let foundHead = false;\r\n    let alreadyInjected = false;\r\n\r\n    traverse(ast, {\r\n        ImportDeclaration(path) {\r\n            if (path.node.source.value === 'next/script') {\r\n                importedScript = true;\r\n            }\r\n        },\r\n        JSXElement(path) {\r\n            const opening = path.node.openingElement;\r\n\r\n            if (\r\n                t.isJSXIdentifier(opening.name, { name: 'Script' }) &&\r\n                opening.attributes.some(\r\n                    (attr) =>\r\n                        t.isJSXAttribute(attr) &&\r\n                        attr.name.name === 'src' &&\r\n                        t.isStringLiteral(attr.value) &&\r\n                        attr.value.value === preLoadScript,\r\n                )\r\n            ) {\r\n                alreadyInjected = true;\r\n            }\r\n\r\n            if (t.isJSXIdentifier(opening.name, { name: 'head' })) {\r\n                foundHead = true;\r\n\r\n                if (!alreadyInjected) {\r\n                    const scriptElement = t.jsxElement(\r\n                        t.jsxOpeningElement(\r\n                            t.jsxIdentifier('Script'),\r\n                            [\r\n                                t.jsxAttribute(t.jsxIdentifier('type'), t.stringLiteral('module')),\r\n                                t.jsxAttribute(\r\n                                    t.jsxIdentifier('src'),\r\n                                    t.stringLiteral(preLoadScript),\r\n                                ),\r\n                            ],\r\n                            true,\r\n                        ),\r\n                        null,\r\n                        [],\r\n                        true,\r\n                    );\r\n\r\n                    // Prepend the script to the <head> children\r\n                    path.node.children.unshift(scriptElement);\r\n                    alreadyInjected = true;\r\n                }\r\n            }\r\n\r\n            if (!foundHead && t.isJSXIdentifier(opening.name, { name: 'html' })) {\r\n                if (!alreadyInjected) {\r\n                    const scriptInHead = t.jsxElement(\r\n                        t.jsxOpeningElement(\r\n                            t.jsxIdentifier('Script'),\r\n                            [\r\n                                t.jsxAttribute(t.jsxIdentifier('type'), t.stringLiteral('module')),\r\n                                t.jsxAttribute(\r\n                                    t.jsxIdentifier('src'),\r\n                                    t.stringLiteral(preLoadScript),\r\n                                ),\r\n                            ],\r\n                            true,\r\n                        ),\r\n                        null,\r\n                        [],\r\n                        true,\r\n                    );\r\n\r\n                    const headElement = t.jsxElement(\r\n                        t.jsxOpeningElement(t.jsxIdentifier('head'), [], false),\r\n                        t.jsxClosingElement(t.jsxIdentifier('head')),\r\n                        [scriptInHead],\r\n                        false,\r\n                    );\r\n\r\n                    path.node.children.unshift(headElement);\r\n                    foundHead = true;\r\n                    alreadyInjected = true;\r\n                }\r\n            }\r\n        },\r\n    });\r\n\r\n    if (!importedScript) {\r\n        ast.program.body.unshift(\r\n            t.importDeclaration(\r\n                [t.importDefaultSpecifier(t.identifier('Script'))],\r\n                t.stringLiteral('next/script'),\r\n            ),\r\n        );\r\n    }\r\n\r\n    const { code } = generate(ast, {}, layoutSrc);\r\n\r\n    await session.fs.writeFile(layoutPath, new TextEncoder().encode(code));\r\n};\r\n\r\nconst addSetupTask = async (session: WebSocketSession) => {\r\n    const tasks = {\r\n        setupTasks: ['npm install'],\r\n        tasks: {\r\n            dev: {\r\n                name: 'Dev Server',\r\n                command: 'npm run dev',\r\n                preview: {\r\n                    port: 3000,\r\n                },\r\n                runAtStart: true,\r\n            },\r\n        },\r\n    };\r\n    await session.fs.writeFile(\r\n        './.codesandbox/tasks.json',\r\n        new TextEncoder().encode(JSON.stringify(tasks, null, 2)),\r\n    );\r\n};\r\n\r\nconst updatePackageJson = async (session: WebSocketSession) => {\r\n    const pkgRaw = await session.fs.readFile('./package.json');\r\n    const pkgJson = JSON.parse(new TextDecoder().decode(pkgRaw));\r\n\r\n    pkgJson.scripts = pkgJson.scripts || {};\r\n    pkgJson.scripts.dev = 'next dev';\r\n\r\n    await session.fs.writeFile(\r\n        './package.json',\r\n        new TextEncoder().encode(JSON.stringify(pkgJson, null, 2)),\r\n    );\r\n};\r\n\r\nexport const parseRepoUrl = (repoUrl: string): { owner: string; repo: string } => {\r\n    const match = repoUrl.match(/github\\.com\\/([^/]+)\\/([^/]+)(?:\\.git)?/);\r\n    if (!match || !match[1] || !match[2]) {\r\n        throw new Error('Invalid GitHub URL');\r\n    }\r\n\r\n    return {\r\n        owner: match[1],\r\n        repo: match[2],\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAAA;AACA;AACA;;;;;;;;AAEA,MAAM,yBAAyB,CAAC;;;;;;CAM/B,CAAC;AAEK,MAAM,iBAAiB,CAAC;IAC3B,OAAO,MACF,OAAO,CAAC,OAAO,KAAK,2CAA2C;KAC/D,OAAO,CAAC,QAAQ,KAAK,6CAA6C;KAClE,OAAO,CAAC,YAAY,IAAI,sCAAsC;KAC9D,WAAW,IAAI,mBAAmB;AAC3C;AAEO,MAAM,sBAAsB,CAAC;IAChC,IAAI,CAAC,OAAO;QACR,OAAO;YAAE,OAAO;YAAO,OAAO;QAAwB;IAC1D;IAEA,iCAAiC;IACjC,MAAM,sBAAsB,aAAa,IAAI,CAAC;IAC9C,IAAI,qBAAqB;QACrB,MAAM,eAAe;QACrB,IAAI,CAAC,aAAa,IAAI,CAAC,QAAQ;YAC3B,OAAO;gBACH,OAAO;gBACP,OAAO;YACX;QACJ;QACA,OAAO;YAAE,OAAO;QAAK;IACzB;IAEA,oEAAoE;IACpE,MAAM,iBAAiB;IACvB,IAAI,CAAC,eAAe,IAAI,CAAC,QAAQ;QAC7B,OAAO;YACH,OAAO;YACP,OAAO;QACX;IACJ;IAEA,OAAO;QAAE,OAAO;IAAK;AACzB;AAEO,MAAM,iBAAiB,CAAC,OAAmB;IAC9C,MAAM,kBAAkB,eAAe;IAEvC,MAAM,YAAY,CAAC;QACf,KAAK,MAAM,QAAQ,MAAO;YACtB,IAAI,eAAe,KAAK,IAAI,MAAM,iBAAiB;gBAC/C,OAAO;YACX;YACA,IACI,MAAM,OAAO,CAAC,KAAK,QAAQ,KAC3B,KAAK,QAAQ,CAAC,MAAM,GAAG,KACvB,UAAU,KAAK,QAAQ,GACzB;gBACE,OAAO;YACX;QACJ;QACA,OAAO;IACX;IAEA,OAAO,UAAU;AACrB;AAEA,MAAM,sBAAsB;IAAC;IAAO;IAAc;IAAO;IAAS;CAAe;AACjF,MAAM,mBAAmB;IAAC;IAAW;CAAM;AAC3C,MAAM,qBAAqB;IAAC;IAAa;CAAQ;AACjD,MAAM,qBAAqB;IAAC;IAAQ;IAAO;IAAQ;CAAM;AACzD,MAAM,iBAAiB;AACvB,MAAM,wBAAwB;IAAC;IAAI;IAAK;CAAI;AAC5C,MAAM,sBAAsB;AAE5B,MAAM,uBAAuB,CAAC;;;;;;;;;;;AAW9B,CAAC;AAED,MAAM,mBAAmB,CAAC;IACtB,MAAM,UAAU,SAAS,WAAW,CAAC;IACrC,OAAO,YAAY,CAAC,IAAI,SAAS,SAAS,CAAC,WAAW;AAC1D;AAEA,MAAM,cAAc,CAAC;IACjB,MAAM,QAAQ,SAAS,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC;IACjD,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,IAAI;AACtC;AAEA,MAAM,aAAa,CAAC;IAChB,MAAM,QAAQ,SAAS,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC;IACjD,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACnC;AAEA,MAAM,WAAW,CAAC,GAAG;IACjB,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ;AAC3D;AAEA,wDAAwD;AACxD,MAAM,kBAAkB,OAAO;IAC3B,IAAI;QACA,MAAM,MAAM,CAAA,GAAA,uIAAA,CAAA,QAAK,AAAD,EAAE,SAAS;YACvB,YAAY;YACZ,SAAS;gBAAC;gBAAc;aAAM;QAClC;QAEA,IAAI;QAEJ,qCAAqC;QACrC,MAAM,qBAAqB,CAAC;YACxB,MAAM,SAAc,CAAC;YACrB,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAE;gBAC/B,IAAI,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CAAC,SAAS,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG;oBACtD,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI;oBACzB,IAAI,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,KAAK,KAAK,GAAG;wBAC/B,MAAM,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK;oBAClC,OAAO,IAAI,uIAAA,CAAA,QAAC,CAAC,kBAAkB,CAAC,KAAK,KAAK,GAAG;wBACzC,MAAM,CAAC,IAAI,GAAG,mBAAmB,KAAK,KAAK;oBAC/C,OAAO,IAAI,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CAAC,KAAK,KAAK,GAAG;wBACxC,MAAM,CAAC,IAAI,GAAG,kBAAkB,KAAK,KAAK;oBAC9C;gBACJ;YACJ;YACA,OAAO;QACX;QAEA,MAAM,oBAAoB,CAAC;YACvB,OAAO,IAAI,QAAQ,CACd,GAAG,CAAC,CAAC;gBACF,IAAI,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,UAAU;oBAC5B,OAAO,QAAQ,KAAK;gBACxB,OAAO,IAAI,uIAAA,CAAA,QAAC,CAAC,kBAAkB,CAAC,UAAU;oBACtC,OAAO,mBAAmB;gBAC9B,OAAO,IAAI,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CAAC,UAAU;oBACrC,OAAO,kBAAkB;gBAC7B;gBACA,OAAO;YACX,GACC,MAAM,CAAC;QAChB;QAEA,2CAA2C;QAC3C,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;YACV,wBAAuB,IAAI;gBACvB,MAAM,cAAc,KAAK,IAAI,CAAC,WAAW;gBACzC,IAAI,uIAAA,CAAA,QAAC,CAAC,qBAAqB,CAAC,cAAc;oBACtC,MAAM,aAAa,YAAY,YAAY,CAAC,EAAE;oBAC9C,IACI,cACA,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,WAAW,EAAE,KAC5B,WAAW,EAAE,CAAC,IAAI,KAAK,cACvB,uIAAA,CAAA,QAAC,CAAC,kBAAkB,CAAC,WAAW,IAAI,GACtC;wBACE,WAAW,CAAC;wBACZ,gDAAgD;wBAChD,KAAK,MAAM,QAAQ,WAAW,IAAI,CAAC,UAAU,CAAE;4BAC3C,IAAI,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CAAC,SAAS,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG;gCACtD,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI;gCACzB,IAAI,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,KAAK,KAAK,GAAG;oCAC9B,QAAgB,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK;gCAC7C,OAAO,IAAI,uIAAA,CAAA,QAAC,CAAC,kBAAkB,CAAC,KAAK,KAAK,GAAG;oCACxC,QAAgB,CAAC,IAAI,GAAG,mBAAmB,KAAK,KAAK;gCAC1D,OAAO,IAAI,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CAAC,KAAK,KAAK,GAAG;oCACvC,QAAgB,CAAC,IAAI,GAAG,kBAAkB,KAAK,KAAK;gCACzD;4BACJ;wBACJ;oBACJ;gBACJ;YACJ;QACJ;QAEA,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,CAAC,uBAAuB,CAAC,EAAE;QACzC,OAAO;IACX;AACJ;AAEA,MAAM,mBAAmB,OACrB,SACA,KACA,aAAqB,EAAE;IAEvB,MAAM,QAAoB,EAAE;IAC5B,IAAI;IAEJ,IAAI;QACA,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;IACvC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,EAAE;QACjD,OAAO;IACX;IAEA,oBAAoB;IACpB,MAAM,WAAW,QAAQ,IAAI,CACzB,CAAC,QACG,MAAM,IAAI,KAAK,UACf,MAAM,IAAI,CAAC,UAAU,CAAC,YACtB,mBAAmB,QAAQ,CAAC,iBAAiB,MAAM,IAAI;IAG/D,IAAI,UAAU;QACV,MAAM,aAAa,YAAY;QAC/B,MAAM,iBAAiB,WAAW,UAAU,CAAC,QAAQ,WAAW,QAAQ,CAAC;QAEzE,IAAI;QACJ,IAAI,gBAAgB;YAChB,MAAM,YAAY;YAClB,YAAY,aAAa,SAAS,WAAW,aAAa,aAAa,MAAM;QACjF,OAAO;YACH,YAAY,aAAa,CAAC,CAAC,EAAE,YAAY,GAAG;QAChD;QAEA,8DAA8D;QAC9D,YAAY,MAAM,UAAU,OAAO,CAAC,YAAY;QAEhD,MAAM,SAAS,sBAAsB,QAAQ,CAAC;QAE9C,mDAAmD;QACnD,IAAI;QACJ,IAAI;YACA,MAAM,cAAc,MAAM,QAAQ,EAAE,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,EAAE,SAAS,IAAI,EAAE;YAC3E,eAAe,MAAM,gBAAgB;QACzC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE;QACtE;QAEA,6CAA6C;QAC7C,MAAM,aAAa,QAAQ,IAAI,CAC3B,CAAC,QACG,MAAM,IAAI,KAAK,UACf,MAAM,IAAI,CAAC,UAAU,CAAC,cACtB,mBAAmB,QAAQ,CAAC,iBAAiB,MAAM,IAAI;QAG/D,IAAI;QACJ,IAAI,YAAY;YACZ,IAAI;gBACA,MAAM,gBAAgB,MAAM,QAAQ,EAAE,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE;gBAC/E,iBAAiB,MAAM,gBAAgB;YAC3C,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC,CAAC,EAAE;YAC1E;QACJ;QAEA,4EAA4E;QAC5E,MAAM,WAAW;YACb,GAAG,cAAc;YACjB,GAAG,YAAY;QACnB;QAEA,MAAM,IAAI,CAAC;YACP,IAAI,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD;YACT,MAAM,iBACA,aACA,aACE,YAAY,cACZ;YACR,MAAM;YACN,UAAU,EAAE;YACZ,UAAU;YACV;YACA,UAAU,YAAY,CAAC;QAC3B;IACJ;IAEA,qBAAqB;IACrB,KAAK,MAAM,SAAS,QAAS;QACzB,IAAI,oBAAoB,QAAQ,CAAC,MAAM,IAAI,GAAG;YAC1C;QACJ;QAEA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE;QACvC,MAAM,eAAe,SAAS,YAAY,MAAM,IAAI;QAEpD,IAAI,MAAM,IAAI,KAAK,aAAa;YAC5B,MAAM,WAAW,MAAM,iBAAiB,SAAS,UAAU;YAC3D,IAAI,SAAS,MAAM,GAAG,GAAG;gBACrB,MAAM,UAAU,aAAa,OAAO,CAAC,OAAO;gBAC5C,MAAM,YAAY,MAAM,QAAQ,OAAO,CAAC,YAAY;gBACpD,MAAM,IAAI,CAAC;oBACP,IAAI,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD;oBACT,MAAM,MAAM,IAAI;oBAChB,MAAM;oBACN;oBACA,UAAU;oBACV,QAAQ;oBACR,UAAU,CAAC;gBACf;YACJ;QACJ;IACJ;IAEA,OAAO;AACX;AAEA,MAAM,qBAAqB,OACvB,SACA,KACA,aAAqB,EAAE;IAEvB,MAAM,QAAoB,EAAE;IAC5B,IAAI;IAEJ,IAAI;QACA,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;IACvC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,EAAE;QACjD,OAAO;IACX;IAEA,sBAAsB;IACtB,KAAK,MAAM,SAAS,QAAS;QACzB,MAAM,WAAW,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE;QAE1C,IAAI,CAAC,UAAU;YACX,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,IAAI,EAAE;YAChD;QACJ;QAEA,IACI,MAAM,IAAI,KAAK,UACf,mBAAmB,QAAQ,CAAC,iBAAiB,MAAM,IAAI,MACvD,CAAC,oBAAoB,QAAQ,CAAC,WAChC;YACE,MAAM,iBAAiB,SAAS,UAAU,CAAC,QAAQ,SAAS,QAAQ,CAAC;YAErE,IAAI;YACJ,IAAI,aAAa,SAAS;gBACtB,YAAY,aAAa,CAAC,CAAC,EAAE,YAAY,GAAG;YAChD,OAAO;gBACH,IAAI,gBAAgB;oBAChB,MAAM,YAAY,SAAS,KAAK,CAAC,GAAG,CAAC;oBACrC,YAAY,SAAS,YAAY;gBACrC,OAAO;oBACH,YAAY,SAAS,YAAY;gBACrC;gBACA,iBAAiB;gBACjB,YAAY,MAAM,UAAU,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,YAAY;YACxE;YAEA,MAAM,SAAS,sBAAsB,QAAQ,CAAC;YAE9C,sCAAsC;YACtC,IAAI;YACJ,IAAI;gBACA,MAAM,cAAc,MAAM,QAAQ,EAAE,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE;gBACxE,WAAW,MAAM,gBAAgB;YACrC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;YAC9D;YAEA,MAAM,IAAI,CAAC;gBACP,IAAI,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD;gBACT,MACI,aAAa,UACP,aACI,CAAC,CAAC,EAAE,YAAY,aAAa,GAC7B,iBACJ,MAAM;gBAChB,MAAM;gBACN,UAAU,EAAE;gBACZ,UAAU;gBACV;gBACA,UAAU,YAAY,CAAC;YAC3B;QACJ;IACJ;IAEA,sBAAsB;IACtB,KAAK,MAAM,SAAS,QAAS;QACzB,IAAI,oBAAoB,QAAQ,CAAC,MAAM,IAAI,GAAG;YAC1C;QACJ;QAEA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE;QACvC,MAAM,eAAe,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,CAAC;QAEvE,MAAM,iBAAiB,eAAe,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM,IAAI;QAC1E,MAAM,eAAe,SAAS,YAAY;QAE1C,IAAI,MAAM,IAAI,KAAK,aAAa;YAC5B,MAAM,WAAW,MAAM,mBAAmB,SAAS,UAAU;YAC7D,IAAI,SAAS,MAAM,GAAG,GAAG;gBACrB,MAAM,UAAU,aAAa,OAAO,CAAC,OAAO;gBAC5C,MAAM,YAAY,MAAM,QAAQ,OAAO,CAAC,YAAY;gBACpD,MAAM,IAAI,CAAC;oBACP,IAAI,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD;oBACT,MAAM,MAAM,IAAI;oBAChB,MAAM;oBACN;oBACA,UAAU;oBACV,QAAQ;oBACR,UAAU,CAAC;gBACf;YACJ;QACJ;IACJ;IAEA,OAAO;AACX;AAEO,MAAM,uBAAuB,OAAO;IACvC,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IAEA,8BAA8B;IAC9B,IAAI,eAAmE;IAEvE,2CAA2C;IAC3C,KAAK,MAAM,WAAW,iBAAkB;QACpC,IAAI;YACA,MAAM,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;YACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;gBAC/B,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS;gBAC7C,eAAe;oBAAE,MAAM;oBAAO,UAAU;gBAAQ;gBAChD;YACJ;QACJ,EAAE,OAAO,OAAO;QACZ,6CAA6C;QACjD;IACJ;IAEA,iDAAiD;IACjD,IAAI,CAAC,cAAc;QACf,KAAK,MAAM,aAAa,mBAAoB;YACxC,IAAI;gBACA,MAAM,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;gBACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;oBAC/B,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW;oBACjD,eAAe;wBAAE,MAAM;wBAAS,UAAU;oBAAU;oBACpD;gBACJ;YACJ,EAAE,OAAO,OAAO;YACZ,6CAA6C;YACjD;QACJ;IACJ;IAEA,IAAI,CAAC,cAAc;QACf,QAAQ,GAAG,CAAC;QACZ,OAAO,EAAE;IACb;IAEA,IAAI,aAAa,IAAI,KAAK,OAAO;QAC7B,OAAO,MAAM,iBAAiB,SAAS,aAAa,QAAQ;IAChE,OAAO;QACH,OAAO,MAAM,mBAAmB,SAAS,aAAa,QAAQ;IAClE;AACJ;AAEA,MAAM,4BAA4B,OAC9B;IAEA,uBAAuB;IACvB,KAAK,MAAM,WAAW,iBAAkB;QACpC,IAAI;YACA,MAAM,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;YACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;gBAC/B,kDAAkD;gBAClD,MAAM,YAAY,QAAQ,IAAI,CAC1B,CAAC,QACG,MAAM,IAAI,KAAK,UACf,MAAM,IAAI,CAAC,UAAU,CAAC,cACtB,mBAAmB,QAAQ,CAAC,iBAAiB,MAAM,IAAI;gBAG/D,IAAI,WAAW;oBACX,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS;oBAC7C,OAAO;wBAAE,MAAM;wBAAO,UAAU;oBAAQ;gBAC5C;YACJ;QACJ,EAAE,OAAO,OAAO;QACZ,6CAA6C;QACjD;IACJ;IAEA,iDAAiD;IACjD,KAAK,MAAM,aAAa,mBAAoB;QACxC,IAAI;YACA,MAAM,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;YACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;gBAC/B,gDAAgD;gBAChD,MAAM,WAAW,QAAQ,IAAI,CACzB,CAAC,QACG,MAAM,IAAI,KAAK,UACf,MAAM,IAAI,CAAC,UAAU,CAAC,aACtB,mBAAmB,QAAQ,CAAC,iBAAiB,MAAM,IAAI;gBAG/D,IAAI,UAAU;oBACV,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW;oBACjD,OAAO;wBAAE,MAAM;wBAAS,UAAU;oBAAU;gBAChD;YACJ;QACJ,EAAE,OAAO,OAAO;QACZ,6CAA6C;QACjD;IACJ;IAEA,OAAO;AACX;AAEA,kCAAkC;AAClC,MAAM,aAAa,OAAO,SAA2B;IACjD,IAAI;QACA,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW;QACpC,MAAM,aAAa,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW;QACvD,MAAM,WAAW,YAAY;QAC7B,OAAO,WAAW,IAAI,CAAC,CAAC,QAAe,MAAM,IAAI,KAAK;IAC1D,EAAE,OAAO,OAAO;QACZ,OAAO;IACX;AACJ;AAEA,MAAM,sBAAsB,OACxB,SACA;IAEA,MAAO,cAAc,eAAe,WAAW,YAAa;QACxD,IAAI;YACA,MAAM,UAAU,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC;YACzC,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACtB,6CAA6C;gBAC7C,MAAM,QAAQ,EAAE,CAAC,MAAM,CAAC;gBACxB,aAAa,WAAW;YAC5B,OAAO;gBACH;YACJ;QACJ,EAAE,OAAO,OAAO;YAEZ;QACJ;IACJ;AACJ;AAEA,MAAM,eAAe,OACjB,SACA,UACA,SACA,cAAc,GAAG;IAEjB,IAAI,aAAa;IACjB,IAAI,UAAU;IAEd,MAAM,WAAW,QAAQ,OAAO,CAAC,iBAAiB;IAElD,MAAO,WAAW,YAAa;QAC3B,MAAM,WAAW,SAAS,UAAU;QACpC,IAAI,CAAE,MAAM,WAAW,SAAS,WAAY;YACxC,OAAO;QACX;QACA,aAAa,GAAG,SAAS,MAAM,EAAE,SAAS;QAC1C;IACJ;IAEA,MAAM,IAAI,MAAM,CAAC,4CAA4C,EAAE,SAAS;AAC5E;AAEA,MAAM,kBAAkB,OAAO,SAA2B;IACtD,gFAAgF;IAChF,MAAM,WAAW,SAAS,SAAS;IACnC,MAAM,QAAQ,EAAE,CAAC,aAAa,CAAC,UAAU;IACzC,MAAM,QAAQ,EAAE,CAAC,MAAM,CAAC;AAC5B;AAEO,MAAM,sBAAsB,OAC/B,SACA;IAEA,IAAI;QACA,MAAM,eAAe,MAAM,0BAA0B;QAErD,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QAEA,IAAI,aAAa,IAAI,KAAK,OAAO;YAC7B,MAAM,IAAI,MAAM;QACpB;QAEA,kCAAkC;QAClC,MAAM,qBAAqB,SAAS,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,YAAY;QAC7E,IAAI,CAAC,0BAA0B,IAAI,CAAC,qBAAqB;YACrD,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,WAAW,SAAS,aAAa,QAAQ,EAAE;QACjD,MAAM,eAAe,SAAS,UAAU;QAExC,IAAI,MAAM,WAAW,SAAS,eAAe;YACzC,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,QAAQ,EAAE,CAAC,aAAa,CAAC,cAAc;QAE7C,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,cAAc;IAClD,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACV;AACJ;AAEO,MAAM,sBAAsB,OAC/B,SACA,UACA;IAEA,IAAI;QACA,MAAM,eAAe,MAAM,0BAA0B;QAErD,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QAEA,IAAI,aAAa,IAAI,KAAK,OAAO;YAC7B,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,iBAAiB,SAAS,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,YAAY;QACzE,IAAI,mBAAmB,MAAM,mBAAmB,KAAK;YACjD,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,WAAW,SAAS,aAAa,QAAQ,EAAE;QAEjD,IAAI,CAAE,MAAM,WAAW,SAAS,WAAY;YACxC,MAAM,IAAI,MAAM;QACpB;QAEA,IAAI,OAAO;YACP,0BAA0B;YAC1B,MAAM,QAAQ,EAAE,CAAC,MAAM,CAAC,UAAU;QACtC,OAAO;YACH,gCAAgC;YAChC,MAAM,eAAe,SAAS,UAAU;YACxC,MAAM,QAAQ,EAAE,CAAC,MAAM,CAAC;YAExB,oCAAoC;YACpC,MAAM,oBAAoB,SAAS;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;IACtC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACV;AACJ;AAEO,MAAM,sBAAsB,OAC/B,SACA,SACA;IAEA,IAAI;QACA,MAAM,eAAe,MAAM,0BAA0B;QAErD,IAAI,CAAC,gBAAgB,aAAa,IAAI,KAAK,OAAO;YAC9C,MAAM,IAAI,MAAM;QACpB;QAEA,IAAI,sBAAsB,QAAQ,CAAC,UAAU;YACzC,MAAM,IAAI,MAAM;QACpB;QAEA,oBAAoB;QACpB,IAAI,CAAC,yBAAyB,IAAI,CAAC,UAAU;YACzC,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,oBAAoB,QAAQ,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,YAAY;QAC3E,MAAM,cAAc,SAAS,aAAa,QAAQ,EAAE;QACpD,MAAM,YAAY,WAAW;QAC7B,MAAM,cAAc,SAAS,WAAW;QAExC,IAAI,CAAE,MAAM,WAAW,SAAS,cAAe;YAC3C,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,aAAa;QAC3D;QAEA,IAAI,MAAM,WAAW,SAAS,cAAc;YACxC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,aAAa;QAChE;QAEA,MAAM,QAAQ,EAAE,CAAC,MAAM,CAAC,aAAa;QAErC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,YAAY,IAAI,EAAE,aAAa;IACpE,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACV;AACJ;AAEO,MAAM,yBAAyB,OAClC,SACA,YACA;IAEA,IAAI;QACA,MAAM,eAAe,MAAM,0BAA0B;QAErD,IAAI,CAAC,gBAAgB,aAAa,IAAI,KAAK,OAAO;YAC9C,MAAM,IAAI,MAAM;QACpB;QAEA,wBAAwB;QACxB,MAAM,aAAa,sBAAsB,QAAQ,CAAC;QAElD,IAAI,YAAY;YACZ,MAAM,iBAAiB,SAAS,aAAa,QAAQ,EAAE;YACvD,MAAM,YAAY,MAAM,aACpB,SACA,aAAa,QAAQ,EACrB;YAEJ,MAAM,gBAAgB,SAAS,aAAa,QAAQ,EAAE;YACtD,MAAM,iBAAiB,SAAS,eAAe;YAE/C,IAAI,MAAM,WAAW,SAAS,gBAAgB;gBAC1C,MAAM,IAAI,MAAM;YACpB;YAEA,MAAM,QAAQ,EAAE,CAAC,IAAI,CAAC,gBAAgB;YAEtC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,gBAAgB;YACxD;QACJ;QAEA,wBAAwB;QACxB,MAAM,uBAAuB,WAAW,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,YAAY;QACjF,MAAM,uBAAuB,MAAM,aAAa,SAAS,aAAa,QAAQ,EAAE;QAEhF,MAAM,aAAa,SAAS,aAAa,QAAQ,EAAE;QACnD,MAAM,aAAa,SAAS,aAAa,QAAQ,EAAE;QAEnD,IAAI,MAAM,WAAW,SAAS,aAAa;YACvC,MAAM,IAAI,MAAM;QACpB;QAEA,yCAAyC;QACzC,MAAM,gBAAgB,MAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW;QAC1D,MAAM,cAAc,cAAc,IAAI,CAClC,CAAC,QAAe,MAAM,IAAI,KAAK,YAAY;QAG/C,IAAI,CAAC,aAAa;YACd,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,YAAY;QAE9C,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW,IAAI,EAAE,YAAY;IACrE,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACV;AACJ;AAEO,MAAM,8BAA8B,OACvC,SACA,UACA;IAEA,MAAM,eAAe,MAAM,0BAA0B;IAErD,IAAI,CAAC,cAAc;QACf,MAAM,IAAI,MAAM;IACpB;IAEA,IAAI,aAAa,IAAI,KAAK,OAAO;QAC7B,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,WAAW,SAAS,aAAa,QAAQ,EAAE;IACjD,MAAM,eAAe,SAAS,UAAU;IACxC,2BAA2B;IAC3B,MAAM,aAAa,MAAM,WAAW,SAAS;IAE7C,IAAI,CAAC,YAAY;QACb,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,cAAc,MAAM,QAAQ,EAAE,CAAC,YAAY,CAAC;IAClD,MAAM,eACF,YAAY,QAAQ,CAAC,mBAAmB,YAAY,QAAQ,CAAC;IAEjE,IAAI,cAAc;QACd,6BAA6B;QAC7B,MAAM,iBAAiB,SAAS,UAAU;QAC1C,MAAM,eAAe,MAAM,WAAW,SAAS;QAE/C,IAAI,cAAc;YACd,MAAM,qBAAqB,SAAS,gBAAgB;QACxD,OAAO;YACH,oBAAoB;YACpB,uCAAuC;YACvC,MAAM,gBAAgB,CAAC,2EAA2E,EAAE,KAAK,SAAS,CAAC,UAAU,MAAM,GAAG,KAAK,EAAE,wBAAwB;YACrK,MAAM,QAAQ,EAAE,CAAC,aAAa,CAAC,gBAAgB;QACnD;IACJ,OAAO;QACH,MAAM,qBAAqB,SAAS,cAAc;IACtD;AACJ;AAEA,eAAe,qBACX,OAAyB,EACzB,QAAgB,EAChB,QAAsB;IAEtB,gCAAgC;IAChC,MAAM,UAAU,MAAM,QAAQ,EAAE,CAAC,YAAY,CAAC;IAE9C,qCAAqC;IACrC,MAAM,MAAM,CAAA,GAAA,uIAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACvB,YAAY;QACZ,SAAS;YAAC;YAAc;SAAM;IAClC;IAEA,IAAI,oBAAoB;IACxB,IAAI,eAAgD;IAEpD,sDAAsD;IACtD,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACV,mBAAkB,IAAI;YAClB,IACI,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,UAC3B,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CACrB,CAAC,OACG,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CAAC,SACpB,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,KAAK,QAAQ,KAC5B,KAAK,QAAQ,CAAC,IAAI,KAAK,aAEjC;gBACE,oBAAoB;YACxB;QACJ;QACA,wBAAuB,IAAI;YACvB,MAAM,cAAc,KAAK,IAAI,CAAC,WAAW;YACzC,IAAI,uIAAA,CAAA,QAAC,CAAC,qBAAqB,CAAC,cAAc;gBACtC,MAAM,aAAa,YAAY,YAAY,CAAC,EAAE;gBAC9C,IACI,cACA,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,WAAW,EAAE,KAC5B,WAAW,EAAE,CAAC,IAAI,KAAK,YACzB;oBACE,eAAe,KAAK,IAAI;gBAC5B;YACJ;QACJ;IACJ;IAEA,qCAAqC;IACrC,IAAI,CAAC,mBAAmB;QACpB,MAAM,iBAAiB,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CACtC;YAAC,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,aAAa,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC;SAAa,EACvE,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;QAEpB,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;IAC7B;IACA,oCAAoC;IACpC,MAAM,iBAAiB,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CACrC,OAAO,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QACtC,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,QAAQ,gBAAgB;gBACxB,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,MACb,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,QAAQ;oBAAC,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;iBAAO;YAErE;YACA,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,MAAM,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;QAC/D,OAAO,IAAI,UAAU,MAAM;YACvB,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,MAAM,uIAAA,CAAA,QAAC,CAAC,WAAW;QAC5D,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC7B,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,MACb,uIAAA,CAAA,QAAC,CAAC,eAAe,CACb,MAAM,GAAG,CAAC,CAAC;gBACP,IAAI,OAAO,MAAM,UAAU;oBACvB,OAAO,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;gBAC3B,OAAO,IAAI,OAAO,MAAM,YAAY,MAAM,MAAM;oBAC5C,OAAO,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CACrB,OAAO,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;wBAC3B,IAAI,OAAO,QAAQ,UAAU;4BACzB,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IACb,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;wBAExB,OAAO,IAAI,OAAO,QAAQ,UAAU;4BAChC,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IACb,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC;wBAEzB;wBACA,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IACb,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,OAAO;oBAE/B;gBAER;gBACA,OAAO,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,OAAO;YAClC;QAGZ,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;YACpD,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,MACb,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CACd,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;gBAC7B,IAAI,OAAO,MAAM,UAAU;oBACvB,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IAAI,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;gBAC7D,OAAO,IAAI,OAAO,MAAM,UAAU;oBAC9B,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IAAI,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC;gBAC9D,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI;oBACzB,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IACb,uIAAA,CAAA,QAAC,CAAC,eAAe,CACb,EAAE,GAAG,CAAC,CAAC;wBACH,IAAI,OAAO,SAAS,UAAU;4BAC1B,OAAO,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;wBAC3B,OAAO,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM;4BAClD,OAAO,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CACrB,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG;gCAC9B,IAAI,OAAO,OAAO,UAAU;oCACxB,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,KACb,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;gCAExB,OAAO,IAAI,OAAO,OAAO,UAAU;oCAC/B,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,KACb,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC;gCAEzB;gCACA,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CACnB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,KACb,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,OAAO;4BAE/B;wBAER;wBACA,OAAO,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,OAAO;oBAClC;gBAGZ;gBACA,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,IAAI,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,OAAO;YACpE;QAGZ;QACA,OAAO,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,MAAM,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,OAAO;IACtE;IAGJ,uCAAuC;IACvC,MAAM,kBAAkB,uIAAA,CAAA,QAAC,CAAC,mBAAmB,CAAC,SAAS;QACnD,uIAAA,CAAA,QAAC,CAAC,kBAAkB,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC,aAAa;KAClD;IAED,sBAAsB;IACtB,MAAM,yBAAyB,uIAAA,CAAA,QAAC,CAAC,gBAAgB,CAAC,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC;IACjF,CAAC,gBAAgB,YAAY,CAAC,EAAE,EAAE,EAAkB,EAAE,cAAc,GAAG;IAEvE,yBAAyB;IACzB,MAAM,iBAAiB,uIAAA,CAAA,QAAC,CAAC,sBAAsB,CAAC;IAEhD,IAAI,cAAc;QACd,mCAAmC;QACnC,MAAM,sBAAsB,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,uIAAA,CAAA,QAAC,CAAC,wBAAwB,CAAC,SAAS,CAAC,uIAAA,CAAA,QAAC,CAAC,qBAAqB,CAAC,KAAK,WAAW,GAAG;gBACjF,OAAO;YACX;YACA,MAAM,aAAa,KAAK,WAAW,CAAC,YAAY,CAAC,EAAE;YACnD,OAAO,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,YAAY,OAAO,WAAW,EAAE,CAAC,IAAI,KAAK;QACpE;QAEA,IAAI,wBAAwB,CAAC,GAAG;YAC5B,IAAI,OAAO,CAAC,IAAI,CAAC,oBAAoB,GAAG;QAC5C;IACJ,OAAO;QACH,qDAAqD;QACrD,MAAM,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OACnD,uIAAA,CAAA,QAAC,CAAC,0BAA0B,CAAC;QAGjC,IAAI,uBAAuB,CAAC,GAAG;YAC3B,MAAM,IAAI,MAAM;QACpB;QAEA,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG;IACnD;IAEA,4BAA4B;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE;IAE1B,MAAM,mBAAmB,MAAM,CAAA,GAAA,qLAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;IAEvD,6CAA6C;IAC7C,MAAM,QAAQ,EAAE,CAAC,aAAa,CAAC,UAAU;AAC7C;AAEO,MAAM,sBAAsB,OAAO;IACtC,MAAM,aAAa;IACnB,MAAM,kBAAkB;IAExB,4BAA4B;IAC5B,MAAM,aAAa,MAAM,0BAA0B;IACnD,MAAM,gBACF;IAEJ,IAAI,CAAC,cAAc,WAAW,IAAI,KAAK,OAAO;QAC1C,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,aAAa;IACnB,MAAM,YAAY,MAAM,QAAQ,EAAE,CAAC,QAAQ,CAAC;IAC5C,MAAM,YAAY,IAAI,cAAc,MAAM,CAAC;IAE3C,MAAM,MAAM,CAAA,GAAA,uIAAA,CAAA,QAAK,AAAD,EAAE,WAAW;QACzB,YAAY;QACZ,SAAS;YAAC;YAAc;SAAM;IAClC;IAEA,IAAI,iBAAiB;IACrB,IAAI,YAAY;IAChB,IAAI,kBAAkB;IAEtB,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACV,mBAAkB,IAAI;YAClB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,eAAe;gBAC1C,iBAAiB;YACrB;QACJ;QACA,YAAW,IAAI;YACX,MAAM,UAAU,KAAK,IAAI,CAAC,cAAc;YAExC,IACI,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,QAAQ,IAAI,EAAE;gBAAE,MAAM;YAAS,MACjD,QAAQ,UAAU,CAAC,IAAI,CACnB,CAAC,OACG,uIAAA,CAAA,QAAC,CAAC,cAAc,CAAC,SACjB,KAAK,IAAI,CAAC,IAAI,KAAK,SACnB,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,KAAK,KAAK,KAC5B,KAAK,KAAK,CAAC,KAAK,KAAK,gBAE/B;gBACE,kBAAkB;YACtB;YAEA,IAAI,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,QAAQ,IAAI,EAAE;gBAAE,MAAM;YAAO,IAAI;gBACnD,YAAY;gBAEZ,IAAI,CAAC,iBAAiB;oBAClB,MAAM,gBAAgB,uIAAA,CAAA,QAAC,CAAC,UAAU,CAC9B,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CACf,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,WAChB;wBACI,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,SAAS,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;wBACxD,uIAAA,CAAA,QAAC,CAAC,YAAY,CACV,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,QAChB,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;qBAEvB,EACD,OAEJ,MACA,EAAE,EACF;oBAGJ,4CAA4C;oBAC5C,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAC3B,kBAAkB;gBACtB;YACJ;YAEA,IAAI,CAAC,aAAa,uIAAA,CAAA,QAAC,CAAC,eAAe,CAAC,QAAQ,IAAI,EAAE;gBAAE,MAAM;YAAO,IAAI;gBACjE,IAAI,CAAC,iBAAiB;oBAClB,MAAM,eAAe,uIAAA,CAAA,QAAC,CAAC,UAAU,CAC7B,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CACf,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,WAChB;wBACI,uIAAA,CAAA,QAAC,CAAC,YAAY,CAAC,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,SAAS,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;wBACxD,uIAAA,CAAA,QAAC,CAAC,YAAY,CACV,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,QAChB,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;qBAEvB,EACD,OAEJ,MACA,EAAE,EACF;oBAGJ,MAAM,cAAc,uIAAA,CAAA,QAAC,CAAC,UAAU,CAC5B,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CAAC,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE,QACjD,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CAAC,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC,UACpC;wBAAC;qBAAa,EACd;oBAGJ,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAC3B,YAAY;oBACZ,kBAAkB;gBACtB;YACJ;QACJ;IACJ;IAEA,IAAI,CAAC,gBAAgB;QACjB,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CACpB,uIAAA,CAAA,QAAC,CAAC,iBAAiB,CACf;YAAC,uIAAA,CAAA,QAAC,CAAC,sBAAsB,CAAC,uIAAA,CAAA,QAAC,CAAC,UAAU,CAAC;SAAW,EAClD,uIAAA,CAAA,QAAC,CAAC,aAAa,CAAC;IAG5B;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,GAAG;IAEnC,MAAM,QAAQ,EAAE,CAAC,SAAS,CAAC,YAAY,IAAI,cAAc,MAAM,CAAC;AACpE;AAEA,MAAM,eAAe,OAAO;IACxB,MAAM,QAAQ;QACV,YAAY;YAAC;SAAc;QAC3B,OAAO;YACH,KAAK;gBACD,MAAM;gBACN,SAAS;gBACT,SAAS;oBACL,MAAM;gBACV;gBACA,YAAY;YAChB;QACJ;IACJ;IACA,MAAM,QAAQ,EAAE,CAAC,SAAS,CACtB,6BACA,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,OAAO,MAAM;AAE7D;AAEA,MAAM,oBAAoB,OAAO;IAC7B,MAAM,SAAS,MAAM,QAAQ,EAAE,CAAC,QAAQ,CAAC;IACzC,MAAM,UAAU,KAAK,KAAK,CAAC,IAAI,cAAc,MAAM,CAAC;IAEpD,QAAQ,OAAO,GAAG,QAAQ,OAAO,IAAI,CAAC;IACtC,QAAQ,OAAO,CAAC,GAAG,GAAG;IAEtB,MAAM,QAAQ,EAAE,CAAC,SAAS,CACtB,kBACA,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,SAAS,MAAM;AAE/D;AAEO,MAAM,eAAe,CAAC;IACzB,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;QAClC,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO;QACH,OAAO,KAAK,CAAC,EAAE;QACf,MAAM,KAAK,CAAC,EAAE;IAClB;AACJ", "debugId": null}}, {"offset": {"line": 2490, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/sandbox.ts"], "sourcesContent": ["import { injectPreloadScript } from '@/components/store/editor/pages/helper';\r\nimport { env } from '@/env';\r\nimport { CodeSandbox, Sandbox, WebSocketSession } from '@codesandbox/sdk';\r\nimport { CSB_PREVIEW_TASK_NAME, getSandboxPreviewUrl, SandboxTemplates, Templates } from '@onlook/constants';\r\nimport { generate, parse } from '@onlook/parser';\r\nimport { addScriptConfig } from '@onlook/parser/src/code-edit/config';\r\nimport { shortenUuid } from '@onlook/utility/src/id';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nconst sdk = new CodeSandbox(env.CSB_API_KEY);\r\n\r\nexport const sandboxRouter = createTRPCRouter({\r\n    start: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                sandboxId: z.string(),\r\n                userId: z.string().optional(),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }) => {\r\n            const startData = await sdk.sandboxes.resume(input.sandboxId);\r\n            const session = await startData.createBrowserSession({\r\n                id: shortenUuid(input.userId ?? uuidv4(), 20),\r\n            });\r\n            return session;\r\n        }),\r\n    get: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                sandboxId: z.string(),\r\n                userId: z.string().optional(),\r\n            }),\r\n        )\r\n        .query(async ({ input }) => {\r\n            const startData = await sdk.sandboxes.resume(input.sandboxId);\r\n            const session = await startData.createBrowserSession({\r\n                id: shortenUuid(input.userId ?? uuidv4(), 20),\r\n            });\r\n            return session;\r\n        }),\r\n    hibernate: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                sandboxId: z.string(),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }) => {\r\n            await sdk.sandboxes.hibernate(input.sandboxId);\r\n        }),\r\n    list: protectedProcedure.query(async () => {\r\n        const listResponse = await sdk.sandboxes.list();\r\n        return listResponse;\r\n    }),\r\n    fork: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                sandbox: z.object({\r\n                    id: z.string(),\r\n                    port: z.number(),\r\n                }),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }) => {\r\n            const sandbox = await sdk.sandboxes.create({\r\n                source: 'template',\r\n                id: input.sandbox.id,\r\n            });\r\n\r\n            const previewUrl = getSandboxPreviewUrl(sandbox.id, input.sandbox.port);\r\n\r\n            return {\r\n                sandboxId: sandbox.id,\r\n                previewUrl,\r\n            };\r\n        }),\r\n    delete: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                sandboxId: z.string(),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }) => {\r\n            await sdk.sandboxes.shutdown(input.sandboxId);\r\n        }),\r\n    uploadProject: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                files: z.record(\r\n                    z.object({\r\n                        content: z.string(),\r\n                        isBinary: z.boolean().optional(),\r\n                    }),\r\n                ),\r\n                projectName: z.string().optional(),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }) => {\r\n            let session: WebSocketSession | null = null;\r\n            let templateSandbox: Sandbox | null = null;\r\n\r\n            try {\r\n                const template = SandboxTemplates[Templates.BLANK];\r\n\r\n                templateSandbox = await sdk.sandboxes.create({\r\n                    source: 'template',\r\n                    id: template.id,\r\n                });\r\n\r\n                session = await templateSandbox.connect();\r\n\r\n                // Upload all project files\r\n                for (const [path, file] of Object.entries(input.files)) {\r\n                    try {\r\n                        // Handle binary vs text files using the correct SDK methods\r\n                        if (file.isBinary) {\r\n                            // For binary files, convert base64 string back to Uint8Array\r\n                            const binaryData = Uint8Array.from(atob(file.content), (c) =>\r\n                                c.charCodeAt(0),\r\n                            );\r\n                            await session.fs.writeFile(path, binaryData, {\r\n                                overwrite: true,\r\n                            });\r\n                        } else {\r\n                            // For text files, use writeTextFile\r\n                            let content = file.content;\r\n\r\n                            // Add script config to the file 'app/layout.tsx' or 'src/app/layout.tsx'\r\n                            if (path === 'app/layout.tsx' || path === 'src/app/layout.tsx') {\r\n                                try {\r\n                                    const ast = parse(content, {\r\n                                        sourceType: 'module',\r\n                                        plugins: ['jsx', 'typescript'],\r\n                                    });\r\n                                    const modifiedAst = addScriptConfig(ast);\r\n                                    content = generate(modifiedAst, {}, content).code;\r\n                                } catch (parseError) {\r\n                                    console.warn(\r\n                                        'Failed to add script config to layout.tsx:',\r\n                                        parseError,\r\n                                    );\r\n                                }\r\n                            }\r\n\r\n                            await session.fs.writeTextFile(path, content, {\r\n                                overwrite: true,\r\n                            });\r\n                        }\r\n                    } catch (fileError) {\r\n                        console.error(`Error uploading file ${path}:`, fileError);\r\n                        throw new Error(\r\n                            `Failed to upload file: ${path} - ${fileError instanceof Error ? fileError.message : 'Unknown error'}`,\r\n                        );\r\n                    }\r\n                }\r\n\r\n                // Run setup task\r\n                await session.setup.run();\r\n\r\n                // Start the dev task\r\n                const task = await session.tasks.get(CSB_PREVIEW_TASK_NAME);\r\n                if (task) {\r\n                    await task.run();\r\n                }\r\n\r\n                // Disconnect the session\r\n                try {\r\n                    await session.disconnect();\r\n                    console.log('Disconnected session');\r\n                } catch (disconnectError) {\r\n                    console.error('Error disconnecting session:', disconnectError);\r\n                }\r\n\r\n                return {\r\n                    sandboxId: templateSandbox.id,\r\n                    previewUrl: getSandboxPreviewUrl(templateSandbox.id, template.port),\r\n                };\r\n            } catch (error) {\r\n                console.error('Error creating project sandbox:', error);\r\n                if (session) {\r\n                    try {\r\n                        await session.disconnect();\r\n                        console.log('Disconnected session during cleanup');\r\n                    } catch (cleanupError) {\r\n                        console.error('Error disconnecting session during cleanup:', cleanupError);\r\n                    }\r\n                }\r\n\r\n                if (templateSandbox?.id) {\r\n                    try {\r\n                        await sdk.sandboxes.shutdown(templateSandbox.id);\r\n                        console.log('Cleaned up failed sandbox:', templateSandbox.id);\r\n                    } catch (cleanupError) {\r\n                        console.error('Error cleaning up sandbox:', cleanupError);\r\n                    }\r\n                }\r\n\r\n                throw new Error(\r\n                    `Failed to create project sandbox: ${error instanceof Error ? error.message : 'Unknown error'}`,\r\n                );\r\n            }\r\n        }),\r\n    createFromGitHub: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                repoUrl: z.string(),\r\n                branch: z.string(),\r\n            }),\r\n        )\r\n        .mutation(async ({ input }) => {\r\n            const sandbox = await sdk.sandboxes.create({\r\n                source: 'git',\r\n                url: input.repoUrl,\r\n                branch: input.branch,\r\n                async setup(session) {\r\n                    await injectPreloadScript(session);\r\n                    await session.setup.run();\r\n                },\r\n            });\r\n            return {\r\n                sandboxId: sandbox.id,\r\n                previewUrl: `https://${sandbox.id}-8084.csb.app`,\r\n            };\r\n        }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;;AAEA,MAAM,MAAM,IAAI,8JAAA,CAAA,cAAW,CAAC,qIAAA,CAAA,MAAG,CAAC,WAAW;AAEpC,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC1C,OAAO,uJAAA,CAAA,qBAAkB,CACpB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;QACnB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,MAAM,YAAY,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,SAAS;QAC5D,MAAM,UAAU,MAAM,UAAU,oBAAoB,CAAC;YACjD,IAAI,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,KAAK;QAC9C;QACA,OAAO;IACX;IACJ,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;QACnB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,IAEH,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE;QACnB,MAAM,YAAY,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,SAAS;QAC5D,MAAM,UAAU,MAAM,UAAU,oBAAoB,CAAC;YACjD,IAAI,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,KAAK;QAC9C;QACA,OAAO;IACX;IACJ,WAAW,uJAAA,CAAA,qBAAkB,CACxB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,MAAM,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,SAAS;IACjD;IACJ,MAAM,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC;QAC3B,MAAM,eAAe,MAAM,IAAI,SAAS,CAAC,IAAI;QAC7C,OAAO;IACX;IACA,MAAM,uJAAA,CAAA,qBAAkB,CACnB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACd,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM;YACZ,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM;QAClB;IACJ,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,MAAM,UAAU,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC;YACvC,QAAQ;YACR,IAAI,MAAM,OAAO,CAAC,EAAE;QACxB;QAEA,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,EAAE,EAAE,MAAM,OAAO,CAAC,IAAI;QAEtE,OAAO;YACH,WAAW,QAAQ,EAAE;YACrB;QACJ;IACJ;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,SAAS;IAChD;IACJ,eAAe,uJAAA,CAAA,qBAAkB,CAC5B,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,CACX,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACL,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;YACjB,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QAClC;QAEJ,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,IAAI,UAAmC;QACvC,IAAI,kBAAkC;QAEtC,IAAI;YACA,MAAM,WAAW,qIAAA,CAAA,mBAAgB,CAAC,qIAAA,CAAA,YAAS,CAAC,KAAK,CAAC;YAElD,kBAAkB,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC;gBACzC,QAAQ;gBACR,IAAI,SAAS,EAAE;YACnB;YAEA,UAAU,MAAM,gBAAgB,OAAO;YAEvC,2BAA2B;YAC3B,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,EAAG;gBACpD,IAAI;oBACA,4DAA4D;oBAC5D,IAAI,KAAK,QAAQ,EAAE;wBACf,6DAA6D;wBAC7D,MAAM,aAAa,WAAW,IAAI,CAAC,KAAK,KAAK,OAAO,GAAG,CAAC,IACpD,EAAE,UAAU,CAAC;wBAEjB,MAAM,QAAQ,EAAE,CAAC,SAAS,CAAC,MAAM,YAAY;4BACzC,WAAW;wBACf;oBACJ,OAAO;wBACH,oCAAoC;wBACpC,IAAI,UAAU,KAAK,OAAO;wBAE1B,yEAAyE;wBACzE,IAAI,SAAS,oBAAoB,SAAS,sBAAsB;4BAC5D,IAAI;gCACA,MAAM,MAAM,CAAA,GAAA,uIAAA,CAAA,QAAK,AAAD,EAAE,SAAS;oCACvB,YAAY;oCACZ,SAAS;wCAAC;wCAAO;qCAAa;gCAClC;gCACA,MAAM,cAAc,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;gCACpC,UAAU,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,CAAC,GAAG,SAAS,IAAI;4BACrD,EAAE,OAAO,YAAY;gCACjB,QAAQ,IAAI,CACR,8CACA;4BAER;wBACJ;wBAEA,MAAM,QAAQ,EAAE,CAAC,aAAa,CAAC,MAAM,SAAS;4BAC1C,WAAW;wBACf;oBACJ;gBACJ,EAAE,OAAO,WAAW;oBAChB,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC,EAAE;oBAC/C,MAAM,IAAI,MACN,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE,qBAAqB,QAAQ,UAAU,OAAO,GAAG,iBAAiB;gBAE9G;YACJ;YAEA,iBAAiB;YACjB,MAAM,QAAQ,KAAK,CAAC,GAAG;YAEvB,qBAAqB;YACrB,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,qIAAA,CAAA,wBAAqB;YAC1D,IAAI,MAAM;gBACN,MAAM,KAAK,GAAG;YAClB;YAEA,yBAAyB;YACzB,IAAI;gBACA,MAAM,QAAQ,UAAU;gBACxB,QAAQ,GAAG,CAAC;YAChB,EAAE,OAAO,iBAAiB;gBACtB,QAAQ,KAAK,CAAC,gCAAgC;YAClD;YAEA,OAAO;gBACH,WAAW,gBAAgB,EAAE;gBAC7B,YAAY,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,EAAE,EAAE,SAAS,IAAI;YACtE;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,SAAS;gBACT,IAAI;oBACA,MAAM,QAAQ,UAAU;oBACxB,QAAQ,GAAG,CAAC;gBAChB,EAAE,OAAO,cAAc;oBACnB,QAAQ,KAAK,CAAC,+CAA+C;gBACjE;YACJ;YAEA,IAAI,iBAAiB,IAAI;gBACrB,IAAI;oBACA,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE;oBAC/C,QAAQ,GAAG,CAAC,8BAA8B,gBAAgB,EAAE;gBAChE,EAAE,OAAO,cAAc;oBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAChD;YACJ;YAEA,MAAM,IAAI,MACN,CAAC,kCAAkC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAEvG;IACJ;IACJ,kBAAkB,uJAAA,CAAA,qBAAkB,CAC/B,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;QACjB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;IACpB,IAEH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACtB,MAAM,UAAU,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC;YACvC,QAAQ;YACR,KAAK,MAAM,OAAO;YAClB,QAAQ,MAAM,MAAM;YACpB,MAAM,OAAM,OAAO;gBACf,MAAM,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE;gBAC1B,MAAM,QAAQ,KAAK,CAAC,GAAG;YAC3B;QACJ;QACA,OAAO;YACH,WAAW,QAAQ,EAAE;YACrB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC;QACpD;IACJ;AACR", "debugId": null}}, {"offset": {"line": 2691, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/project/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './chat';\r\nexport * from './frame';\r\nexport * from './invitation';\r\nexport * from './member';\r\nexport * from './project';\r\nexport * from './sandbox';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/settings.ts"], "sourcesContent": ["import {\r\n    projectSettings,\r\n    projectSettingsInsertSchema,\r\n    toProjectSettings\r\n} from '@onlook/db';\r\nimport { TRPCError } from '@trpc/server';\r\nimport { eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../trpc';\r\n\r\nexport const settingsRouter = createTRPCRouter({\r\n    get: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const setting = await ctx.db.query.projectSettings.findFirst({\r\n                where: eq(projectSettings.projectId, input.projectId),\r\n            });\r\n            if (!setting) {\r\n                return null;\r\n            }\r\n            return toProjectSettings(setting);\r\n        }),\r\n    upsert: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n                settings: projectSettingsInsertSchema,\r\n            }),\r\n        )\r\n        .mutation(async ({ ctx, input }) => {\r\n            const [updatedSettings] = await ctx.db\r\n                .insert(projectSettings)\r\n                .values(input)\r\n                .onConflictDoUpdate({\r\n                    target: [projectSettings.projectId],\r\n                    set: input.settings,\r\n                })\r\n                .returning();\r\n            if (!updatedSettings) {\r\n                throw new TRPCError({\r\n                    code: 'INTERNAL_SERVER_ERROR',\r\n                    message: 'Failed to update project settings',\r\n                });\r\n            }\r\n            return toProjectSettings(updatedSettings);\r\n        }),\r\n    delete: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n            }),\r\n        )\r\n        .mutation(async ({ ctx, input }) => {\r\n            await ctx.db\r\n                .delete(projectSettings)\r\n                .where(eq(projectSettings.projectId, input.projectId));\r\n            return true;\r\n        }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AAAA;AACA;;;;;;AAEO,MAAM,iBAAiB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC3C,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC;YACzD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,wJAAA,CAAA,kBAAe,CAAC,SAAS,EAAE,MAAM,SAAS;QACxD;QACA,IAAI,CAAC,SAAS;YACV,OAAO;QACX;QACA,OAAO,CAAA,GAAA,yIAAA,CAAA,oBAAiB,AAAD,EAAE;IAC7B;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;QACnB,UAAU,wJAAA,CAAA,8BAA2B;IACzC,IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,CAAC,gBAAgB,GAAG,MAAM,IAAI,EAAE,CACjC,MAAM,CAAC,wJAAA,CAAA,kBAAe,EACtB,MAAM,CAAC,OACP,kBAAkB,CAAC;YAChB,QAAQ;gBAAC,wJAAA,CAAA,kBAAe,CAAC,SAAS;aAAC;YACnC,KAAK,MAAM,QAAQ;QACvB,GACC,SAAS;QACd,IAAI,CAAC,iBAAiB;YAClB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAChB,MAAM;gBACN,SAAS;YACb;QACJ;QACA,OAAO,CAAA,GAAA,yIAAA,CAAA,oBAAiB,AAAD,EAAE;IAC7B;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CACrB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC3B,MAAM,IAAI,EAAE,CACP,MAAM,CAAC,wJAAA,CAAA,kBAAe,EACtB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,wJAAA,CAAA,kBAAe,CAAC,SAAS,EAAE,MAAM,SAAS;QACxD,OAAO;IACX;AACR", "debugId": null}}, {"offset": {"line": 2799, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/constants/index.ts"], "sourcesContent": ["export const Routes = {\r\n    HOME: '/',\r\n    LOGIN: '/login',\r\n    PRICING: '/pricing',\r\n    PROJECTS: '/projects',\r\n    PROJECT: '/project',\r\n    IMPORT_PROJECT: '/projects/import',\r\n    CALLBACK_STRIPE_SUCCESS: '/callback/stripe/success',\r\n    CALLBACK_STRIPE_CANCEL: '/callback/stripe/cancel',\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,SAAS;IAClB,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,SAAS;IACT,gBAAgB;IAChB,yBAAyB;IACzB,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 2818, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/subscription/subscription.ts"], "sourcesContent": ["import { Routes } from '@/utils/constants';\r\nimport { prices, subscriptions, toSubscription } from '@onlook/db';\r\nimport { db } from '@onlook/db/src/client';\r\nimport { createBillingPortalSession, createCheckoutSession, PriceKey, releaseSubscriptionSchedule, updateSubscription, updateSubscriptionNextPeriod } from '@onlook/stripe';\r\nimport { and, eq } from 'drizzle-orm';\r\nimport { headers } from 'next/headers';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const subscriptionRouter = createTRPCRouter({\r\n    get: protectedProcedure.query(async ({ ctx }) => {\r\n        const user = ctx.user;\r\n        const subscription = await db.query.subscriptions.findFirst({\r\n            where: and(eq(subscriptions.userId, user.id), eq(subscriptions.status, 'active')),\r\n            with: {\r\n                product: true,\r\n                price: true,\r\n            },\r\n        });\r\n\r\n        if (!subscription) {\r\n            console.error('No active subscription found for user', user.id);\r\n            return null;\r\n        }\r\n\r\n        // If there is a scheduled price, we need to fetch it from the database.\r\n        let scheduledPrice = null;\r\n        if (subscription.scheduledPriceId) {\r\n            scheduledPrice = await db.query.prices.findFirst({\r\n                where: eq(prices.id, subscription.scheduledPriceId),\r\n            }) ?? null;\r\n        }\r\n\r\n        return toSubscription(subscription, scheduledPrice);\r\n    }),\r\n    getPriceId: protectedProcedure.input(z.object({\r\n        priceKey: z.nativeEnum(PriceKey),\r\n    })).mutation(async ({ input }) => {\r\n        const price = await db.query.prices.findFirst({\r\n            where: eq(prices.key, input.priceKey),\r\n        });\r\n\r\n        if (!price) {\r\n            throw new Error(`Price not found for key: ${input.priceKey}`);\r\n        }\r\n\r\n        return price.stripePriceId;\r\n    }),\r\n    checkout: protectedProcedure.input(z.object({\r\n        priceId: z.string(),\r\n    })).mutation(async ({ ctx, input }) => {\r\n        const originUrl = (await headers()).get('origin');\r\n        const user = ctx.user;\r\n        const session = await createCheckoutSession({\r\n            priceId: input.priceId,\r\n            userId: user.id,\r\n            successUrl: `${originUrl}${Routes.CALLBACK_STRIPE_SUCCESS}`,\r\n            cancelUrl: `${originUrl}${Routes.CALLBACK_STRIPE_CANCEL}`,\r\n        });\r\n\r\n        return session;\r\n    }),\r\n    manageSubscription: protectedProcedure.mutation(async ({ ctx }) => {\r\n        const user = ctx.user;\r\n        const subscription = await db.query.subscriptions.findFirst({\r\n            where: and(eq(subscriptions.userId, user.id), eq(subscriptions.status, 'active')),\r\n        });\r\n\r\n        if (!subscription) {\r\n            throw new Error('No active subscription found for user');\r\n        }\r\n\r\n        const originUrl = (await headers()).get('origin');\r\n\r\n        const session = await createBillingPortalSession({\r\n            customerId: subscription.stripeCustomerId,\r\n            returnUrl: `${originUrl}/subscription/manage`,\r\n        });\r\n\r\n        return session;\r\n    }),\r\n    update: protectedProcedure.input(z.object({\r\n        stripeSubscriptionId: z.string(),\r\n        stripeSubscriptionItemId: z.string(),\r\n        stripePriceId: z.string(),\r\n    })).mutation(async ({ input }) => {\r\n        const { stripeSubscriptionId, stripeSubscriptionItemId, stripePriceId } = input;\r\n        const subscription = await db.query.subscriptions.findFirst({\r\n            where: and(\r\n                eq(subscriptions.stripeSubscriptionId, stripeSubscriptionId),\r\n                eq(subscriptions.stripeSubscriptionItemId, stripeSubscriptionItemId),\r\n            ),\r\n            with: {\r\n                price: true,\r\n            },\r\n        });\r\n\r\n        if (!subscription) {\r\n            throw new Error('Subscription not found');\r\n        }\r\n\r\n        const currentPrice = subscription.price;\r\n        const newPrice = await db.query.prices.findFirst({\r\n            where: eq(prices.stripePriceId, stripePriceId),\r\n        });\r\n\r\n        if (!newPrice) {\r\n            throw new Error(`Price not found for priceId: ${stripePriceId}`);\r\n        }\r\n\r\n        // If there is a future scheduled change, we release it.\r\n        if (subscription.stripeSubscriptionScheduleId) {\r\n            await releaseSubscriptionSchedule({\r\n                subscriptionScheduleId: subscription.stripeSubscriptionScheduleId,\r\n            });\r\n        }\r\n\r\n        const isUpgrade = newPrice?.monthlyMessageLimit > currentPrice.monthlyMessageLimit;\r\n        if (isUpgrade) {\r\n            // If the new price is higher, we invoice the customer immediately.\r\n            const updatedSubscription = await updateSubscription({\r\n                subscriptionId: stripeSubscriptionId,\r\n                subscriptionItemId: stripeSubscriptionItemId,\r\n                priceId: stripePriceId,\r\n            });\r\n            await db.update(subscriptions).set({\r\n                priceId: newPrice.id,\r\n                status: 'active',\r\n                updatedAt: new Date(),\r\n            }).where(eq(subscriptions.stripeSubscriptionItemId, stripeSubscriptionItemId)).returning();\r\n            return updatedSubscription;\r\n        } else {\r\n            // If the new price is lower, we schedule the change for the end of the current period.\r\n            const schedule = await updateSubscriptionNextPeriod({\r\n                subscriptionId: stripeSubscriptionId,\r\n                priceId: stripePriceId,\r\n            });\r\n            const endDate = schedule.phases[0]?.end_date;\r\n            const scheduledChangeAt = endDate ? new Date(endDate * 1000) : null;\r\n\r\n            const [updatedSubscription] = await db.update(subscriptions).set({\r\n                priceId: currentPrice.id,\r\n                updatedAt: new Date(),\r\n                scheduledPriceId: newPrice.id,\r\n                stripeSubscriptionScheduleId: schedule.id,\r\n                scheduledChangeAt,\r\n            }).where(eq(subscriptions.stripeSubscriptionItemId, stripeSubscriptionItemId)).returning();\r\n            return updatedSubscription;\r\n        }\r\n    }),\r\n\r\n    releaseSubscriptionSchedule: protectedProcedure.input(z.object({\r\n        subscriptionScheduleId: z.string(),\r\n    })).mutation(async ({ input }) => {\r\n        await releaseSubscriptionSchedule({ subscriptionScheduleId: input.subscriptionScheduleId });\r\n        await db.update(subscriptions).set({\r\n            status: 'active',\r\n            updatedAt: new Date(),\r\n            scheduledPriceId: null,\r\n            stripeSubscriptionScheduleId: null,\r\n            scheduledChangeAt: null,\r\n        }).where(eq(subscriptions.stripeSubscriptionScheduleId, input.subscriptionScheduleId)).returning();\r\n    }),\r\n});\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC/C,KAAK,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,IAAI,IAAI;QACrB,MAAM,eAAe,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC;YACxD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;YACvE,MAAM;gBACF,SAAS;gBACT,OAAO;YACX;QACJ;QAEA,IAAI,CAAC,cAAc;YACf,QAAQ,KAAK,CAAC,yCAAyC,KAAK,EAAE;YAC9D,OAAO;QACX;QAEA,wEAAwE;QACxE,IAAI,iBAAiB;QACrB,IAAI,aAAa,gBAAgB,EAAE;YAC/B,iBAAiB,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,SAAM,CAAC,EAAE,EAAE,aAAa,gBAAgB;YACtD,MAAM;QACV;QAEA,OAAO,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE,cAAc;IACxC;IACA,YAAY,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC1C,UAAU,mLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,wIAAA,CAAA,WAAQ;IACnC,IAAI,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACzB,MAAM,QAAQ,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;YAC1C,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,SAAM,CAAC,GAAG,EAAE,MAAM,QAAQ;QACxC;QAEA,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,QAAQ,EAAE;QAChE;QAEA,OAAO,MAAM,aAAa;IAC9B;IACA,UAAU,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACxC,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9B,MAAM,YAAY,CAAC,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC;QACxC,MAAM,OAAO,IAAI,IAAI;QACrB,MAAM,UAAU,MAAM,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;YACxC,SAAS,MAAM,OAAO;YACtB,QAAQ,KAAK,EAAE;YACf,YAAY,GAAG,YAAY,6JAAA,CAAA,SAAM,CAAC,uBAAuB,EAAE;YAC3D,WAAW,GAAG,YAAY,6JAAA,CAAA,SAAM,CAAC,sBAAsB,EAAE;QAC7D;QAEA,OAAO;IACX;IACA,oBAAoB,uJAAA,CAAA,qBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QAC1D,MAAM,OAAO,IAAI,IAAI;QACrB,MAAM,eAAe,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC;YACxD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;QAC3E;QAEA,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,YAAY,CAAC,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC;QAExC,MAAM,UAAU,MAAM,CAAA,GAAA,wIAAA,CAAA,6BAA0B,AAAD,EAAE;YAC7C,YAAY,aAAa,gBAAgB;YACzC,WAAW,GAAG,UAAU,oBAAoB,CAAC;QACjD;QAEA,OAAO;IACX;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtC,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM;QAC9B,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM;QAClC,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM;IAC3B,IAAI,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACzB,MAAM,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,aAAa,EAAE,GAAG;QAC1E,MAAM,eAAe,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC;YACxD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACL,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE,uBACvC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,wBAAwB,EAAE;YAE/C,MAAM;gBACF,OAAO;YACX;QACJ;QAEA,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,eAAe,aAAa,KAAK;QACvC,MAAM,WAAW,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;YAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,SAAM,CAAC,aAAa,EAAE;QACpC;QAEA,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,eAAe;QACnE;QAEA,wDAAwD;QACxD,IAAI,aAAa,4BAA4B,EAAE;YAC3C,MAAM,CAAA,GAAA,wIAAA,CAAA,8BAA2B,AAAD,EAAE;gBAC9B,wBAAwB,aAAa,4BAA4B;YACrE;QACJ;QAEA,MAAM,YAAY,UAAU,sBAAsB,aAAa,mBAAmB;QAClF,IAAI,WAAW;YACX,mEAAmE;YACnE,MAAM,sBAAsB,MAAM,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;gBACjD,gBAAgB;gBAChB,oBAAoB;gBACpB,SAAS;YACb;YACA,MAAM,iIAAA,CAAA,KAAE,CAAC,MAAM,CAAC,iKAAA,CAAA,gBAAa,EAAE,GAAG,CAAC;gBAC/B,SAAS,SAAS,EAAE;gBACpB,QAAQ;gBACR,WAAW,IAAI;YACnB,GAAG,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,wBAAwB,EAAE,2BAA2B,SAAS;YACxF,OAAO;QACX,OAAO;YACH,uFAAuF;YACvF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,+BAA4B,AAAD,EAAE;gBAChD,gBAAgB;gBAChB,SAAS;YACb;YACA,MAAM,UAAU,SAAS,MAAM,CAAC,EAAE,EAAE;YACpC,MAAM,oBAAoB,UAAU,IAAI,KAAK,UAAU,QAAQ;YAE/D,MAAM,CAAC,oBAAoB,GAAG,MAAM,iIAAA,CAAA,KAAE,CAAC,MAAM,CAAC,iKAAA,CAAA,gBAAa,EAAE,GAAG,CAAC;gBAC7D,SAAS,aAAa,EAAE;gBACxB,WAAW,IAAI;gBACf,kBAAkB,SAAS,EAAE;gBAC7B,8BAA8B,SAAS,EAAE;gBACzC;YACJ,GAAG,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,wBAAwB,EAAE,2BAA2B,SAAS;YACxF,OAAO;QACX;IACJ;IAEA,6BAA6B,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC3D,wBAAwB,mLAAA,CAAA,IAAC,CAAC,MAAM;IACpC,IAAI,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACzB,MAAM,CAAA,GAAA,wIAAA,CAAA,8BAA2B,AAAD,EAAE;YAAE,wBAAwB,MAAM,sBAAsB;QAAC;QACzF,MAAM,iIAAA,CAAA,KAAE,CAAC,MAAM,CAAC,iKAAA,CAAA,gBAAa,EAAE,GAAG,CAAC;YAC/B,QAAQ;YACR,WAAW,IAAI;YACf,kBAAkB;YAClB,8BAA8B;YAC9B,mBAAmB;QACvB,GAAG,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,4BAA4B,EAAE,MAAM,sBAAsB,GAAG,SAAS;IACpG;AACJ", "debugId": null}}, {"offset": {"line": 2986, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/usage/index.ts"], "sourcesContent": ["import { subscriptions, usageRecords } from '@onlook/db';\r\nimport { db } from '@onlook/db/src/client';\r\nimport { UsageType, type Usage } from '@onlook/models';\r\nimport { FREE_PRODUCT_CONFIG } from '@onlook/stripe';\r\nimport { and, eq, gte, sql } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const usageRouter = createTRPCRouter({\r\n    get: protectedProcedure.query(async ({ ctx }) => {\r\n        const user = ctx.user;\r\n\r\n        // Calculate date ranges\r\n        const now = new Date();\r\n        const lastDay = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago\r\n        const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago\r\n\r\n\r\n        // Count records from last day\r\n        const lastDayCount = await db\r\n            .select({ count: sql<number>`count(*)` })\r\n            .from(usageRecords)\r\n            .where(\r\n                and(\r\n                    eq(usageRecords.userId, user.id),\r\n                    gte(usageRecords.timestamp, lastDay)\r\n                )\r\n            );\r\n\r\n        // Count records from last month\r\n        const lastMonthCount = await db\r\n            .select({ count: sql<number>`count(*)` })\r\n            .from(usageRecords)\r\n            .where(\r\n                and(\r\n                    eq(usageRecords.userId, user.id),\r\n                    gte(usageRecords.timestamp, lastMonth)\r\n                )\r\n            );\r\n\r\n        let dailyLimitCount = FREE_PRODUCT_CONFIG.dailyLimit;\r\n        let monthlyLimitCount = FREE_PRODUCT_CONFIG.monthlyLimit;\r\n\r\n        const subscription = await db.query.subscriptions.findFirst({\r\n            where: and(eq(subscriptions.userId, user.id), eq(subscriptions.status, 'active')),\r\n            with: {\r\n                price: true,\r\n            },\r\n        });\r\n\r\n        if (subscription) {\r\n            // Monthly and daily limits are the same for PRO subscription\r\n            dailyLimitCount = subscription.price.monthlyMessageLimit;\r\n            monthlyLimitCount = subscription.price.monthlyMessageLimit;\r\n        }\r\n\r\n        return {\r\n            daily: {\r\n                period: 'day',\r\n                usageCount: lastDayCount[0]?.count || 0,\r\n                limitCount: dailyLimitCount,\r\n            } satisfies Usage,\r\n            monthly: {\r\n                period: 'month',\r\n                usageCount: lastMonthCount[0]?.count || 0,\r\n                limitCount: monthlyLimitCount,\r\n            } satisfies Usage,\r\n        };\r\n    }),\r\n\r\n    increment: protectedProcedure.input(z.object({\r\n        type: z.nativeEnum(UsageType),\r\n    })).mutation(async ({ ctx, input }) => {\r\n        const user = ctx.user;\r\n        await db.insert(usageRecords).values({\r\n            userId: user.id,\r\n            type: input.type,\r\n            timestamp: new Date(),\r\n        });\r\n    }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;AAEO,MAAM,cAAc,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,KAAK,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,IAAI,IAAI;QAErB,wBAAwB;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,OAAO,eAAe;QAC9E,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,cAAc;QAGpF,8BAA8B;QAC9B,MAAM,eAAe,MAAM,iIAAA,CAAA,KAAE,CACxB,MAAM,CAAC;YAAE,OAAO,8IAAA,CAAA,MAAG,AAAQ,CAAC,QAAQ,CAAC;QAAC,GACtC,IAAI,CAAC,0JAAA,CAAA,eAAY,EACjB,KAAK,CACF,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,KAAK,EAAE,GAC/B,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,0JAAA,CAAA,eAAY,CAAC,SAAS,EAAE;QAIxC,gCAAgC;QAChC,MAAM,iBAAiB,MAAM,iIAAA,CAAA,KAAE,CAC1B,MAAM,CAAC;YAAE,OAAO,8IAAA,CAAA,MAAG,AAAQ,CAAC,QAAQ,CAAC;QAAC,GACtC,IAAI,CAAC,0JAAA,CAAA,eAAY,EACjB,KAAK,CACF,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,0JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,KAAK,EAAE,GAC/B,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,0JAAA,CAAA,eAAY,CAAC,SAAS,EAAE;QAIxC,IAAI,kBAAkB,wIAAA,CAAA,sBAAmB,CAAC,UAAU;QACpD,IAAI,oBAAoB,wIAAA,CAAA,sBAAmB,CAAC,YAAY;QAExD,MAAM,eAAe,MAAM,iIAAA,CAAA,KAAE,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC;YACxD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;YACvE,MAAM;gBACF,OAAO;YACX;QACJ;QAEA,IAAI,cAAc;YACd,6DAA6D;YAC7D,kBAAkB,aAAa,KAAK,CAAC,mBAAmB;YACxD,oBAAoB,aAAa,KAAK,CAAC,mBAAmB;QAC9D;QAEA,OAAO;YACH,OAAO;gBACH,QAAQ;gBACR,YAAY,YAAY,CAAC,EAAE,EAAE,SAAS;gBACtC,YAAY;YAChB;YACA,SAAS;gBACL,QAAQ;gBACR,YAAY,cAAc,CAAC,EAAE,EAAE,SAAS;gBACxC,YAAY;YAChB;QACJ;IACJ;IAEA,WAAW,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzC,MAAM,mLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6IAAA,CAAA,YAAS;IAChC,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9B,MAAM,OAAO,IAAI,IAAI;QACrB,MAAM,iIAAA,CAAA,KAAE,CAAC,MAAM,CAAC,0JAAA,CAAA,eAAY,EAAE,MAAM,CAAC;YACjC,QAAQ,KAAK,EAAE;YACf,MAAM,MAAM,IAAI;YAChB,WAAW,IAAI;QACnB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 3067, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/subscription/index.ts"], "sourcesContent": ["export * from './subscription';\r\nexport * from '../usage';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 3088, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/user/user.ts"], "sourcesContent": ["import { createDefaultUserSettings, toUserSettings, userInsertSchema, users, userSettings, userSettingsInsertSchema } from '@onlook/db';\r\nimport { eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nconst userSettingsRoute = createTRPCRouter({\r\n    get: protectedProcedure.input(z.object({ userId: z.string() })).query(async ({ ctx, input }) => {\r\n        const settings = await ctx.db.query.userSettings.findFirst({\r\n            where: eq(userSettings.userId, input.userId),\r\n        });\r\n        return toUserSettings(settings ?? createDefaultUserSettings(input.userId));\r\n    }),\r\n    upsert: protectedProcedure.input(z.object({\r\n        userId: z.string(),\r\n        settings: userSettingsInsertSchema,\r\n    })).mutation(async ({ ctx, input }) => {\r\n        if (!input.userId) {\r\n            throw new Error('User ID is required');\r\n        }\r\n\r\n        const [updatedSettings] = await ctx.db\r\n            .insert(userSettings)\r\n            .values({\r\n                ...input.settings,\r\n                userId: input.userId,\r\n            })\r\n            .onConflictDoUpdate({\r\n                target: [userSettings.userId],\r\n                set: input.settings,\r\n            })\r\n            .returning();\r\n\r\n        if (!updatedSettings) {\r\n            throw new Error('Failed to update user settings');\r\n        }\r\n\r\n        return toUserSettings(updatedSettings);\r\n    }),\r\n});\r\n\r\nexport const userRouter = createTRPCRouter({\r\n    getById: protectedProcedure.input(z.string()).query(async ({ ctx, input }) => {\r\n        const user = await ctx.db.query.users.findFirst({\r\n            where: eq(users.id, input),\r\n            with: {\r\n                userProjects: {\r\n                    with: {\r\n                        project: true,\r\n                    },\r\n                },\r\n            },\r\n        });\r\n        return user;\r\n    }),\r\n    create: protectedProcedure.input(userInsertSchema).mutation(async ({ ctx, input }) => {\r\n        const user = await ctx.db.insert(users).values(input).returning({ id: users.id });\r\n        if (!user[0]) {\r\n            throw new Error('Failed to create user');\r\n        }\r\n        return user[0];\r\n    }),\r\n    settings: userSettingsRoute,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEA,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,KAAK,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAAI,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACvF,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC;YACvD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,qJAAA,CAAA,eAAY,CAAC,MAAM,EAAE,MAAM,MAAM;QAC/C;QACA,OAAO,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,CAAA,GAAA,uJAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,MAAM;IAC5E;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM;QAChB,UAAU,qJAAA,CAAA,2BAAwB;IACtC,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC9B,IAAI,CAAC,MAAM,MAAM,EAAE;YACf,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,CAAC,gBAAgB,GAAG,MAAM,IAAI,EAAE,CACjC,MAAM,CAAC,qJAAA,CAAA,eAAY,EACnB,MAAM,CAAC;YACJ,GAAG,MAAM,QAAQ;YACjB,QAAQ,MAAM,MAAM;QACxB,GACC,kBAAkB,CAAC;YAChB,QAAQ;gBAAC,qJAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,KAAK,MAAM,QAAQ;QACvB,GACC,SAAS;QAEd,IAAI,CAAC,iBAAiB;YAClB,MAAM,IAAI,MAAM;QACpB;QAEA,OAAO,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE;IAC1B;AACJ;AAEO,MAAM,aAAa,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,SAAS,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACrE,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;YAC5C,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,iJAAA,CAAA,QAAK,CAAC,EAAE,EAAE;YACpB,MAAM;gBACF,cAAc;oBACV,MAAM;wBACF,SAAS;oBACb;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,iJAAA,CAAA,mBAAgB,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7E,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,iJAAA,CAAA,QAAK,EAAE,MAAM,CAAC,OAAO,SAAS,CAAC;YAAE,IAAI,iJAAA,CAAA,QAAK,CAAC,EAAE;QAAC;QAC/E,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACV,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,CAAC,EAAE;IAClB;IACA,UAAU;AACd", "debugId": null}}, {"offset": {"line": 3166, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/user/user-canvas.ts"], "sourcesContent": ["import { canvases, toCanvas, userCanvases, userCanvasUpdateSchema } from '@onlook/db';\r\nimport { and, eq } from 'drizzle-orm';\r\nimport { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../../trpc';\r\n\r\nexport const userCanvasRouter = createTRPCRouter({\r\n    get: protectedProcedure\r\n        .input(\r\n            z.object({\r\n                projectId: z.string(),\r\n            }),\r\n        )\r\n        .query(async ({ ctx, input }) => {\r\n            const userCanvas = await ctx.db.query.userCanvases.findFirst({\r\n                where: and(\r\n                    eq(canvases.projectId, input.projectId),\r\n                    eq(userCanvases.userId, ctx.user.id),\r\n                ),\r\n                with: {\r\n                    canvas: true,\r\n                },\r\n            });\r\n\r\n            if (!userCanvas) {\r\n                throw new Error('User canvas not found');\r\n            }\r\n            return toCanvas(userCanvas);\r\n        }),\r\n    update: protectedProcedure.input(userCanvasUpdateSchema).mutation(async ({ ctx, input }) => {\r\n        try {\r\n            if (!input.canvasId) {\r\n                throw new Error('Canvas ID is required');\r\n            }\r\n            await ctx.db\r\n                .update(userCanvases)\r\n                .set(input)\r\n                .where(\r\n                    and(\r\n                        eq(userCanvases.canvasId, input.canvasId),\r\n                        eq(userCanvases.userId, ctx.user.id),\r\n                    ),\r\n                );\r\n            return true;\r\n        } catch (error) {\r\n            console.error('Error updating user canvas', error);\r\n            return false;\r\n        }\r\n    }),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7C,KAAK,uJAAA,CAAA,qBAAkB,CAClB,KAAK,CACF,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACL,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAEH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACxB,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC;YACzD,OAAO,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACL,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,gKAAA,CAAA,WAAQ,CAAC,SAAS,EAAE,MAAM,SAAS,GACtC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,2JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,EAAE;YAEvC,MAAM;gBACF,QAAQ;YACZ;QACJ;QAEA,IAAI,CAAC,YAAY;YACb,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;IACpB;IACJ,QAAQ,uJAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,2JAAA,CAAA,yBAAsB,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QACnF,IAAI;YACA,IAAI,CAAC,MAAM,QAAQ,EAAE;gBACjB,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,IAAI,EAAE,CACP,MAAM,CAAC,2JAAA,CAAA,eAAY,EACnB,GAAG,CAAC,OACJ,KAAK,CACF,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,2JAAA,CAAA,eAAY,CAAC,QAAQ,EAAE,MAAM,QAAQ,GACxC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,2JAAA,CAAA,eAAY,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,EAAE;YAG/C,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACX;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 3215, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/user/index.ts"], "sourcesContent": ["export * from './user';\r\nexport * from './user-canvas';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 3236, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/routers/index.ts"], "sourcesContent": ["export * from './code';\r\nexport * from './domain';\r\nexport * from './forward';\r\nexport * from './github';\r\nexport * from './image';\r\nexport * from './project';\r\nexport * from './settings';\r\nexport * from './subscription';\r\nexport * from './usage';\r\nexport * from './user';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3292, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/server/api/root.ts"], "sourcesContent": ["import { createCallerFactory, createTRPCRouter } from '~/server/api/trpc';\r\nimport { canvasRouter, chatRouter, codeRouter, domainRouter, frameRouter, githubRouter, imageRouter, invitationRouter, memberRouter, projectRouter, sandboxRouter, settingsRouter, subscriptionRouter, usageRouter, userCanvasRouter, userRouter } from './routers';\r\n\r\n/**\r\n * This is the primary router for your server.\r\n *\r\n * All routers added in /api/routers should be manually added here.\r\n */\r\nexport const appRouter = createTRPCRouter({\r\n    sandbox: sandboxRouter,\r\n    user: userRouter,\r\n    invitation: invitationRouter,\r\n    project: projectRouter,\r\n    settings: settingsRouter,\r\n    chat: chatRouter,\r\n    frame: frameRouter,\r\n    canvas: canvasRouter,\r\n    userCanvas: userCanvasRouter,\r\n    code: codeRouter,\r\n    member: memberRouter,\r\n    domain: domainRouter,\r\n    github: githubRouter,\r\n    subscription: subscriptionRouter,\r\n    usage: usageRouter,\r\n    image: imageRouter,\r\n});\r\n\r\n// export type definition of API\r\nexport type AppRouter = typeof appRouter;\r\n\r\n/**\r\n * Create a server-side caller for the tRPC API.\r\n * @example\r\n * const trpc = createCaller(createContext);\r\n * const res = await trpc.post.all();\r\n *       ^? Post[]\r\n */\r\nexport const createCaller = createCallerFactory(appRouter);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAOO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD,EAAE;IACtC,SAAS,gLAAA,CAAA,gBAAa;IACtB,MAAM,0KAAA,CAAA,aAAU;IAChB,YAAY,mLAAA,CAAA,mBAAgB;IAC5B,SAAS,gLAAA,CAAA,gBAAa;IACtB,UAAU,sKAAA,CAAA,iBAAc;IACxB,MAAM,6KAAA,CAAA,aAAU;IAChB,OAAO,8KAAA,CAAA,cAAW;IAClB,QAAQ,+KAAA,CAAA,eAAY;IACpB,YAAY,oLAAA,CAAA,mBAAgB;IAC5B,MAAM,kKAAA,CAAA,aAAU;IAChB,QAAQ,+KAAA,CAAA,eAAY;IACpB,QAAQ,6KAAA,CAAA,eAAY;IACpB,QAAQ,oKAAA,CAAA,eAAY;IACpB,cAAc,0LAAA,CAAA,qBAAkB;IAChC,OAAO,4KAAA,CAAA,cAAW;IAClB,OAAO,mKAAA,CAAA,cAAW;AACtB;AAYO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,sBAAmB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3348, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/api/trpc/%5Btrpc%5D/route.ts"], "sourcesContent": ["import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from '@trpc/server/adapters/fetch';\r\nimport { type NextRequest } from 'next/server';\r\nimport { env } from '~/env';\r\nimport { appRouter } from '~/server/api/root';\r\nimport { createTRPCContext } from '~/server/api/trpc';\r\n\r\n/**\r\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\r\n * handling a HTTP request (e.g. when you make requests from Client Components).\r\n */\r\nconst createContext = async (req: NextRequest) => {\r\n    return createTRPCContext({\r\n        headers: req.headers,\r\n    });\r\n};\r\n\r\nconst handler = (req: NextRequest) =>\r\n    fetchRequestHandler({\r\n        endpoint: '/api/trpc',\r\n        req,\r\n        router: appRouter,\r\n        createContext: () => createContext(req),\r\n        onError:\r\n            env.NODE_ENV === 'development'\r\n                ? ({ path, error }) => {\r\n                    console.error(`❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`);\r\n                }\r\n                : undefined,\r\n    });\r\n\r\nexport { handler as GET, handler as POS<PERSON> };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;;;;;;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,OAAO;IACzB,OAAO,CAAA,GAAA,uJAAA,CAAA,oBAAiB,AAAD,EAAE;QACrB,SAAS,IAAI,OAAO;IACxB;AACJ;AAEA,MAAM,UAAU,CAAC,MACb,CAAA,GAAA,yKAAA,CAAA,sBAAmB,AAAD,EAAE;QAChB,UAAU;QACV;QACA,QAAQ,uJAAA,CAAA,YAAS;QACjB,eAAe,IAAM,cAAc;QACnC,SACI,qIAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBACX,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,EAAE,MAAM,OAAO,EAAE;QAC7E,IACE;IACd", "debugId": null}}]}