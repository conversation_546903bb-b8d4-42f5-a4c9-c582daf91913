{"node": {"00cd44dbe438809b8be8d4cc763972b75322394bc1": {"workers": {"app/login/page": {"moduleId": "[project]/apps/web/client/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/apps/web/client/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/login/page": "action-browser", "app/page": "action-browser"}}, "403a281bd877a4daba0b4466f7a1fa5c8394a83b9f": {"workers": {"app/login/page": {"moduleId": "[project]/apps/web/client/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/apps/web/client/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/login/page": "action-browser", "app/page": "action-browser"}}}, "edge": {}, "encryptionKey": "UbnSsXifywJT6PaEeFyV81RV7w9GKP7hknsOxWW+8jA="}