{"name": "@onlook/repo", "version": "0.0.0", "description": "Onlook Monorepo", "homepage": "https://onlook.com", "license": "Apache-2.0", "private": true, "author": {"name": "Onlook", "email": "<EMAIL>"}, "workspaces": ["packages/*", "apps/*", "tooling/*", "apps/web/*", "docs"], "scripts": {"build": "bun --filter @onlook/web build:client", "start": "bun --filter @onlook/web start:client", "dev": "bun --filter @onlook/web dev", "test": "bun --filter '*' test", "docs": "bun --filter @onlook/docs dev", "backend:start": "bun --filter @onlook/backend start", "db:gen": "bun --filter @onlook/db db:gen", "db:push": "bun --filter @onlook/db db:push", "db:seed": "bun --filter @onlook/db db:seed", "db:reset": "bun --filter @onlook/db db:reset", "db:migrate": "bun --filter @onlook/db db:migrate", "db:seed:stripe": "bun --filter @onlook/db db:seed:stripe", "db:studio": "bun --filter @onlook/db db:studio", "format": "bun --filter '*' format", "lint": "bun --filter @onlook/web-client lint", "typecheck": "bun --filter '*' typecheck", "prepare": "husky", "clean": "git clean -xdf node_modules", "clean:workspaces": "bun --filter '*' clean", "setup:env": "bun --filter @onlook/scripts build && bun packages/scripts/dist/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/onlook-dev/onlook.git"}, "bugs": {"url": "https://github.com/onlook-dev/onlook/issues"}, "devDependencies": {"extract-zip": "^2.0.1", "husky": "^9.1.6"}, "dependencies": {}, "packageManager": "bun@1.2.13"}