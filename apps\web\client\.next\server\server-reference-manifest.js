self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00cd44dbe438809b8be8d4cc763972b75322394bc1\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"403a281bd877a4daba0b4466f7a1fa5c8394a83b9f\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"UbnSsXifywJT6PaEeFyV81RV7w9GKP7hknsOxWW+8jA=\"\n}"