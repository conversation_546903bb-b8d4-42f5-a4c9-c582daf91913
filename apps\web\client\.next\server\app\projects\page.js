const CHUNK_PUBLIC_PATH = "server/app/projects/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_a17f26a9._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__292a8cba._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_client_messages_95d8ef57._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_d7c9bc58._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__2486e49e._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_client_src_app_not-found_tsx_510eaecb._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_b04db032._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_fd558822.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_dist_esm_cde6975c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_tr46_1a859af0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ws_34fa8cd4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_2a35e6ef._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_b04775b1._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__815dfad1._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__b8d1f5df._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_0913a8b0._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/client/.next-internal/server/app/projects/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/projects/page { MODULE_0 => \"[project]/apps/web/client/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/client/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/apps/web/client/src/app/projects/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/web/client/src/app/projects/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/projects/page { MODULE_0 => \"[project]/apps/web/client/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/client/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/apps/web/client/src/app/projects/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/web/client/src/app/projects/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
