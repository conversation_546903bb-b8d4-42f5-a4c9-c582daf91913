{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/messages/en.d.json.ts"], "sourcesContent": ["// This file is auto-generated by next-intl, do not edit directly.\n// See: https://next-intl.dev/docs/workflows/typescript#messages-arguments\n\ndeclare const messages: {\r\n    \"projects\": {\r\n        \"create\": {\r\n            \"settings\": {\r\n                \"title\": \"Settings\",\r\n                \"tooltip\": \"Configure new project settings\"\r\n            },\r\n            \"success\": \"Project created successfully.\",\r\n            \"steps\": {\r\n                \"count\": \"{current} of {total}\",\r\n                \"error\": \"Project data is missing.\"\r\n            },\r\n            \"methods\": {\r\n                \"new\": \"Create New Project\",\r\n                \"load\": \"Load Existing Project\"\r\n            },\r\n            \"loading\": {\r\n                \"title\": \"Setting up your new Onlook app...\",\r\n                \"description\": \"This may take a few seconds\",\r\n                \"cancel\": \"Cancel\"\r\n            },\r\n            \"error\": {\r\n                \"title\": \"Error creating your Onlook app\",\r\n                \"backToPrompt\": \"Back to Prompting\"\r\n            }\r\n        },\r\n        \"select\": {\r\n            \"empty\": \"No projects found\",\r\n            \"sort\": {\r\n                \"recent\": \"Recently Updated\",\r\n                \"name\": \"Project Name\"\r\n            },\r\n            \"lastEdited\": \"Last edited {time} ago\"\r\n        },\r\n        \"actions\": {\r\n            \"import\": \"Import Project\",\r\n            \"close\": \"Close\",\r\n            \"about\": \"About Onlook\",\r\n            \"signOut\": \"Sign out\",\r\n            \"editApp\": \"Edit App\",\r\n            \"projectSettings\": \"Project settings\",\r\n            \"showInExplorer\": \"Show in Explorer\",\r\n            \"renameProject\": \"Rename Project\",\r\n            \"deleteProject\": \"Delete Project\",\r\n            \"cancel\": \"Cancel\",\r\n            \"delete\": \"Delete\",\r\n            \"rename\": \"Rename\",\r\n            \"goToAllProjects\": \"Go to all Projects\",\r\n            \"newProject\": \"New Project\",\r\n            \"startFromScratch\": \"Start from scratch\",\r\n            \"importProject\": \"Import a project\",\r\n            \"subscriptions\": \"Subscriptions\",\r\n            \"settings\": \"Settings\",\r\n            \"downloadCode\": \"Download Code\",\r\n            \"downloadingCode\": \"Preparing download...\",\r\n            \"downloadSuccess\": \"Download started successfully\",\r\n            \"downloadError\": \"Failed to prepare download\"\r\n        },\r\n        \"dialogs\": {\r\n            \"delete\": {\r\n                \"title\": \"Are you sure you want to delete this project?\",\r\n                \"description\": \"This action cannot be undone. This will permanently delete your project and remove all associated data.\",\r\n                \"moveToTrash\": \"Also move folder to trash\"\r\n            },\r\n            \"rename\": {\r\n                \"title\": \"Rename Project\",\r\n                \"label\": \"Project Name\",\r\n                \"error\": \"Project name can't be empty\"\r\n            }\r\n        },\r\n        \"prompt\": {\r\n            \"title\": \"What kind of website do you want to make?\",\r\n            \"description\": \"Tell us a bit about your project. Be as detailed as possible.\",\r\n            \"input\": {\r\n                \"placeholder\": \"Paste a reference screenshot, write a novel, get creative...\",\r\n                \"imageUpload\": \"Upload Image Reference\",\r\n                \"fileReference\": \"File Reference\",\r\n                \"submit\": \"Start building your site\"\r\n            },\r\n            \"blankStart\": \"Start from a blank page\"\r\n        }\r\n    },\r\n    \"welcome\": {\r\n        \"title\": \"Welcome to Onlook\",\r\n        \"titleReturn\": \"Welcome back to Onlook\",\r\n        \"description\": \"Onlook is an open-source visual editor for React apps. Design directly in your live product.\",\r\n        \"alpha\": \"Alpha\",\r\n        \"login\": {\r\n            \"github\": \"Login with GitHub\",\r\n            \"google\": \"Login with Google\",\r\n            \"lastUsed\": \"You used this last time\",\r\n            \"loginToEdit\": \"Login to Edit\",\r\n            \"shareProjects\": \"Share projects, collaborate, and design more in code.\"\r\n        },\r\n        \"terms\": {\r\n            \"agreement\": \"By signing up, you agree to our\",\r\n            \"privacy\": \"Privacy Policy\",\r\n            \"and\": \"and\",\r\n            \"tos\": \"Terms of Service\"\r\n        },\r\n        \"version\": \"Version {version}\"\r\n    },\r\n    \"pricing\": {\r\n        \"plans\": {\r\n            \"basic\": {\r\n                \"name\": \"Basic\",\r\n                \"price\": \"$0/month\",\r\n                \"description\": \"Prototype and experiment in code with ease.\",\r\n                \"features\": [\r\n                    \"Visual code editor access\",\r\n                    \"Unlimited projects\",\r\n                    \"{dailyMessages} AI chat messages a day\",\r\n                    \"{monthlyMessages} AI messages a month\",\r\n                    \"Limited to 1 screenshot per chat\"\r\n                ]\r\n            },\r\n            \"pro\": {\r\n                \"name\": \"Pro\",\r\n                \"price\": \"$20/month\",\r\n                \"description\": \"Creativity – unconstrained. Build stunning sites with AI.\",\r\n                \"features\": [\r\n                    \"Visual code editor access\",\r\n                    \"Unlimited projects\",\r\n                    \"Unlimited AI chat messages a day\",\r\n                    \"Unlimited monthly chats\",\r\n                    \"Remove built with Onlook watermark\",\r\n                    \"1 free custom domain hosted with Onlook\",\r\n                    \"Priority support\"\r\n                ]\r\n            },\r\n            \"launch\": {\r\n                \"name\": \"Launch\",\r\n                \"price\": \"$50/month\",\r\n                \"description\": \"Perfect for startups and growing teams\",\r\n                \"features\": [\r\n                    \"Unlimited daily messages\",\r\n                    \"Priority support\",\r\n                    \"Advanced integrations\",\r\n                    \"Team collaboration features\"\r\n                ]\r\n            },\r\n            \"scale\": {\r\n                \"name\": \"Scale\",\r\n                \"price\": \"$100/month\",\r\n                \"description\": \"Enterprise-grade features for large teams\",\r\n                \"features\": [\r\n                    \"Everything in Launch plan\",\r\n                    \"Dedicated account manager\",\r\n                    \"Custom integrations\",\r\n                    \"Advanced analytics\",\r\n                    \"24/7 premium support\"\r\n                ]\r\n            }\r\n        },\r\n        \"titles\": {\r\n            \"choosePlan\": \"Choose your plan\",\r\n            \"proMember\": \"Thanks for being a Pro member!\"\r\n        },\r\n        \"buttons\": {\r\n            \"currentPlan\": \"Current Plan\",\r\n            \"getPro\": \"Get Pro\",\r\n            \"manageSubscription\": \"Manage Subscription\"\r\n        },\r\n        \"loading\": {\r\n            \"checkingPayment\": \"Checking for payment...\"\r\n        },\r\n        \"toasts\": {\r\n            \"checkingOut\": {\r\n                \"title\": \"Checking out\",\r\n                \"description\": \"You will now be redirected to Stripe to complete the payment.\"\r\n            },\r\n            \"redirectingToStripe\": {\r\n                \"title\": \"Redirecting to Stripe\",\r\n                \"description\": \"You will now be redirected to Stripe to manage your subscription.\"\r\n            },\r\n            \"error\": {\r\n                \"title\": \"Error\",\r\n                \"description\": \"Could not initiate checkout process. Please try again.\"\r\n            }\r\n        },\r\n        \"footer\": {\r\n            \"unusedMessages\": \"Unused chat messages don't rollover to the next month\"\r\n        }\r\n    },\r\n    \"editor\": {\r\n        \"modes\": {\r\n            \"design\": {\r\n                \"name\": \"Design\",\r\n                \"description\": \"Edit and modify your website's design\",\r\n                \"tooltip\": \"Switch to design mode\"\r\n            },\r\n            \"preview\": {\r\n                \"name\": \"Preview\",\r\n                \"description\": \"Preview and test your website's functionality\",\r\n                \"tooltip\": \"Switch to Preview mode\"\r\n            }\r\n        },\r\n        \"toolbar\": {\r\n            \"tools\": {\r\n                \"select\": {\r\n                    \"name\": \"Select\",\r\n                    \"tooltip\": \"Select and modify elements\"\r\n                },\r\n                \"pan\": {\r\n                    \"name\": \"Pan\",\r\n                    \"tooltip\": \"Pan and move around the canvas\"\r\n                },\r\n                \"insertDiv\": {\r\n                    \"name\": \"Insert Container\",\r\n                    \"tooltip\": \"Add a new container element\"\r\n                },\r\n                \"insertText\": {\r\n                    \"name\": \"Insert Text\",\r\n                    \"tooltip\": \"Add a new text element\"\r\n                }\r\n            },\r\n            \"versionHistory\": \"Version History\"\r\n        },\r\n        \"panels\": {\r\n            \"edit\": {\r\n                \"tabs\": {\r\n                    \"chat\": {\r\n                        \"name\": \"Chat\",\r\n                        \"emptyState\": \"Select an element to chat with AI\",\r\n                        \"emptyStateStart\": \"Start the project to chat\",\r\n                        \"input\": {\r\n                            \"placeholder\": \"Type your message...\",\r\n                            \"tooltip\": \"Chat with AI about the selected element\"\r\n                        },\r\n                        \"controls\": {\r\n                            \"newChat\": \"New Chat\",\r\n                            \"history\": \"Chat History\"\r\n                        },\r\n                        \"settings\": {\r\n                            \"showSuggestions\": \"Show suggestions\",\r\n                            \"expandCodeBlocks\": \"Show code while rendering\"\r\n                        },\r\n                        \"miniChat\": {\r\n                            \"button\": \"Chat with AI\"\r\n                        }\r\n                    },\r\n                    \"styles\": {\r\n                        \"name\": \"Styles\",\r\n                        \"emptyState\": \"Select an element to edit its style properties\",\r\n                        \"groups\": {\r\n                            \"position\": \"Position & Dimensions\",\r\n                            \"layout\": \"Flexbox & Layout\",\r\n                            \"style\": \"Styles\",\r\n                            \"text\": \"Text\"\r\n                        },\r\n                        \"tailwind\": {\r\n                            \"title\": \"Tailwind Classes\",\r\n                            \"placeholder\": \"Add tailwind classes here\",\r\n                            \"componentClasses\": {\r\n                                \"title\": \"Main Component Classes\",\r\n                                \"tooltip\": \"Changes apply to component code. This is the default.\"\r\n                            },\r\n                            \"instanceClasses\": {\r\n                                \"title\": \"Instance Classes\",\r\n                                \"tooltip\": \"Changes apply to instance code.\"\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            \"layers\": {\r\n                \"name\": \"Layers\",\r\n                \"tabs\": {\r\n                    \"layers\": \"Layers\",\r\n                    \"pages\": \"Pages\",\r\n                    \"components\": \"Elements\",\r\n                    \"images\": \"Images\",\r\n                    \"windows\": {\r\n                        \"name\": \"Windows\",\r\n                        \"emptyState\": \"Select a window to edit its settings\"\r\n                    },\r\n                    \"brand\": \"Brand\",\r\n                    \"apps\": \"Apps\"\r\n                }\r\n            }\r\n        },\r\n        \"settings\": {\r\n            \"preferences\": {\r\n                \"language\": \"Language\",\r\n                \"theme\": \"Theme\",\r\n                \"deleteWarning\": \"Delete Warning\",\r\n                \"analytics\": \"Analytics\",\r\n                \"editor\": {\r\n                    \"ide\": \"Editor\",\r\n                    \"shouldWarnDelete\": \"Warn when deleting elements\",\r\n                    \"enableAnalytics\": \"Enable analytics\"\r\n                },\r\n                \"shortcuts\": \"Shortcuts\"\r\n            }\r\n        },\r\n        \"frame\": {\r\n            \"startDesigning\": {\r\n                \"prefix\": \"Press \",\r\n                \"action\": \"Play\",\r\n                \"suffix\": \" to start designing your App\"\r\n            },\r\n            \"playButton\": \"Play\",\r\n            \"waitingForApp\": \"Waiting for the App to start...\"\r\n        },\r\n        \"runButton\": {\r\n            \"portInUse\": \"Port in Use\",\r\n            \"loading\": \"Loading\",\r\n            \"play\": \"Play\",\r\n            \"retry\": \"Retry\",\r\n            \"stop\": \"Stop\"\r\n        },\r\n        \"zoom\": {\r\n            \"level\": \"Zoom Level\",\r\n            \"in\": \"Zoom In\",\r\n            \"out\": \"Zoom Out\",\r\n            \"fit\": \"Zoom Fit\",\r\n            \"reset\": \"Zoom 100%\",\r\n            \"double\": \"Zoom 200%\"\r\n        }\r\n    },\r\n    \"help\": {\r\n        \"menu\": {\r\n            \"reloadOnlook\": \"Reload Onlook\",\r\n            \"theme\": {\r\n                \"title\": \"Theme\",\r\n                \"light\": \"Light\",\r\n                \"dark\": \"Dark\",\r\n                \"system\": \"System\"\r\n            },\r\n            \"language\": \"Language\",\r\n            \"openSettings\": \"Open Settings\",\r\n            \"contactUs\": {\r\n                \"title\": \"Contact Us\",\r\n                \"website\": \"Website\",\r\n                \"discord\": \"Discord\",\r\n                \"github\": \"GitHub\",\r\n                \"email\": \"Email\"\r\n            },\r\n            \"reportIssue\": \"Report Issue\",\r\n            \"shortcuts\": \"Shortcuts\"\r\n        }\r\n    }\r\n};\nexport default messages;"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,0EAA0E;;;;uCAyV3D", "debugId": null}}]}